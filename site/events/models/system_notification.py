from django.db import models
from django.utils import timezone


class SystemNotification(models.Model):
    class Meta:
        db_table = 'system_notifications'

    id = models.BigAutoField(primary_key=True)

    scope: str = models.CharField(max_length=1024, null=False, default="profile:edit")

    category = models.CharField(max_length=99)
    type_notification = models.CharField(max_length=99)
    level = models.CharField(max_length=99, default="ERROR")

    title = models.CharField(max_length=1024, null=False)
    content = models.TextField(null=False)

    group = models.ForeignKey(
        "Group", on_delete=models.RESTRICT, related_name='admin_notifies'
    )

    change_date = models.DateTimeField(default=timezone.now, null=False)
    time_created = models.DateTimeField(default=timezone.now)

    is_read = models.BooleanField(default=False, null=False)
    is_deleted = models.BooleanField(default=False, null=False)

    recipient_type = models.Char<PERSON>ield(max_length=99, default="ADMIN", null=False) # ADMIN | USER

    recipient = models.ForeignKey(
        "TelegramUser", on_delete=models.CASCADE, null=True, blank=True
    )

