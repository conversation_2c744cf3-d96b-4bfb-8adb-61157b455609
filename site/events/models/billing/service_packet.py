from django.db import models
from django.utils import timezone

from pay4say.settings import DEFAULT_LANG


class BillingServicePacket(models.Model):
    class Meta:
        db_table = 'billing_service_packets'

    id = models.SmallAutoField(primary_key=True)
    is_deleted = models.BooleanField(default=False)
    position = models.PositiveSmallIntegerField()

    name = models.CharField(max_length=128)
    subtitle = models.CharField(max_length=128)
    description = models.CharField(max_length=1024)

    recurring_interval = models.CharField(max_length=5)
    recurring_interval_count = models.PositiveSmallIntegerField(default=1)

    country_mode = models.CharField(max_length=8)
    # if country_mode is SPECIFIC
    countries = models.JSONField(null=True, blank=True)
    currency = models.CharField(max_length=3)

    is_public = models.BooleanField(default=False)

    billing_amount_threshold = models.PositiveIntegerField(null=True, blank=True)

    trial_allowed = models.Bo<PERSON>anField(default=False)
    is_free_plan = models.<PERSON><PERSON>anField(default=False)

    lang = models.CharField(max_length=2, default=DEFAULT_LANG)
    _langs_list = models.JSONField(null=True, blank=True)

    time_created = models.DateTimeField(default=timezone.now)
