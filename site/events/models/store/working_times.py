from django.db import models


class WorkingDay(models.Model):
    class Meta:
        db_table = "working_days"
        verbose_name = "Рабочее время, дни недели"
        verbose_name_plural = "Рабочее время, дни недели"

    id = models.BigAutoField(primary_key=True)
    day = models.CharField(max_length=10, blank=False, null=False, verbose_name="День недели")
    store = models.ForeignKey(
        "Store",
        on_delete=models.PROTECT,
        blank=True, null=True, default=None,
        verbose_name="Магазин"
    )
    slots = models.ForeignKey(
        "WorkingSlot",
        on_delete=models.PROTECT,
        blank=True, null=True, default=None,
        verbose_name="слоты"
    )
    is_weekend = models.BooleanField(default=True)


class WorkingSlot(models.Model):
    class Meta:
        db_table = "working_slots"
        verbose_name = "Рабочее время, слоты"
        verbose_name_plural = "Рабочее время, слоты"

    id = models.BigAutoField(primary_key=True)
    start_time = models.TimeField(verbose_name="Время начала работы")
    end_time = models.TimeField(verbose_name="Время окончания работы")
    working_day = models.ForeignKey(
        "WorkingDay",
        on_delete=models.PROTECT,
        blank=True, null=True, default=None,
        verbose_name="день"
    )
