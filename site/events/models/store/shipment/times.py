from django.db import models


class ShipmentTimeToSettings(models.Model):

    class Meta:
        db_table = "shipment_time_to_settings"
        verbose_name = "Связь времени доставки с настройками"
        verbose_name_plural = "Связи времени доставки с настройками"

    id = models.BigAutoField(primary_key=True)
    shipment_time = models.ForeignKey(
        "ShipmentTime", to_field="id",
        on_delete=models.CASCADE, null=False,
        related_name="shipment_time",
        verbose_name="Время доставки"
    )
    settings = models.ForeignKey(
        "BrandCustomSettings", to_field="id",
        on_delete=models.CASCADE,
        null=True, blank=True, default=None,
        related_name="settings_to_time",
        verbose_name="Настройки доставки"
    )
    zone = models.ForeignKey(
        "ShipmentZone", to_field="id",
        on_delete=models.CASCADE,
        null=True, blank=True, default=None,
        related_name="zone_to_time",
        verbose_name="Зона доставки"
    )


class NotWorkingHours(models.TextChoices):
    NOTHING = "nothing"
    WARNING = "warning"
    ERROR = "error"


class ShipmentTime(models.Model):

    class Meta:
        db_table = "shipment_times"
        verbose_name = "Время доставки"
        verbose_name_plural = "Время доставок"

    id = models.BigAutoField(primary_key=True)
    delivery_datetime_mode = models.CharField(
        max_length=50, verbose_name="Режим выбора времени доставки",
        null=False, blank=False, default="datetime"
    )
    not_working_hours = models.CharField(
        max_length=7,
        choices=NotWorkingHours.choices,
        default=NotWorkingHours.NOTHING,
        verbose_name="Действие на заказ в не рабочее время",
    )

    _min_time = models.FloatField(
        null=True, blank=True, default=None,
        verbose_name="Минимальный интервал времени доставки"
    )
    _max_time = models.FloatField(
        null=True, blank=True, default=None,
        verbose_name="Максимальный интервал времени доставки"
    )
    _order_execution = models.FloatField(
        null=True, blank=True, default=None,
        verbose_name="Ориентировочное время исполнения заказа"
    )
