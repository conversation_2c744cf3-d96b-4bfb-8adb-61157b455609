from django.db import models


class StoreCategory(models.Model):
    class Meta:
        db_table = "store_categories"

    id = models.BigAutoField(primary_key=True)

    is_deleted = models.BooleanField(default=False)
    is_default = models.BooleanField(default=False)

    get_order_id = models.IntegerField(null=True, blank=True)

    external_id = models.CharField(max_length=255, null=True, blank=True)
    external_type = models.CharField(max_length=20, null=True, blank=True)

    name = models.CharField(max_length=255)
    position = models.PositiveSmallIntegerField(default=0)

    media = models.ForeignKey("MediaObject", null=True, blank=True, on_delete=models.SET_NULL)

    brand = models.ForeignKey("Brand", on_delete=models.CASCADE)

    father_category = models.ForeignKey(
        "StoreCategory", on_delete=models.CASCADE,
        null=True, blank=True,
        verbose_name="Батьківська категорія",
        related_name="child_categories",
        related_query_name="child_categories",
    )


class StoreCategoryFilter(models.Model):
    class Meta:
        db_table = "store_category_filters"

    id = models.BigAutoField(primary_key=True)
    category = models.ForeignKey(StoreCategory, on_delete=models.CASCADE)
    characteristic = models.ForeignKey("StoreCharacteristic", on_delete=models.CASCADE)
