from django.db import models


class VirtualManager(models.Model):
    class Meta:
        db_table = "virtual_managers"

    id = models.BigAutoField(primary_key=True)

    name = models.Char<PERSON>ield(max_length=100, verbose_name="имя")
    name_id = models.Char<PERSON>ield(max_length=100, null=True, blank=True)

    is_deleted = models.BooleanField(default=False)

    group = models.ForeignKey("Group", on_delete=models.CASCADE)

    questionnaire = models.OneToOneField(
        "Questionnaire", on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="vm_questionnaire",
        related_query_name="vm_questionnaire"
    )

    creator = models.ForeignKey("TelegramUser", on_delete=models.CASCADE)

    allow_click_old_messages = models.BooleanField(default=True)
    start_only_in_owner_profile = models.BooleanField(default=False)

    on_start_delay = models.FloatField(default=0)
    message_delay = models.FloatField(default=0)

    is_reminder_enabled = models.<PERSON>oleanField(default=True)
    reminder_delay = models.FloatField(default=60.0 * 60.0)
    reminds_count = models.IntegerField(default=3)

    bot_hello_message_enabled = models.BooleanField(default=True)

    time_created = models.DateTimeField(auto_now_add=True)
