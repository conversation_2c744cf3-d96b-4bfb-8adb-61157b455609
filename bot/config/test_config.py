# system
PYTHON = "/root/Env/test_payforsay/bin/python"

# Токен бота
SERVICE_BOT_API_TOKEN = "1465893526:AAG5TIqWFSrermjQrnRvLmaRXQHK4qgOBDY"
ROOT_BOT_API_TOKEN = "1463787075:AAEsIUS69UfWiEPpMUEUFzX9ihTnE5nUtRY"
# username ботов
SERVICE_BOT_USERNAME = "Pay4SayServiceBot"
FATHER_BOT_USERNAME = "Pay4SayAdminBot"
ROOT_BOT_USERNAME = "Pay4SayRootBot"
DEFAULT_PAY4SAY_BOT_USERNAME = "Pay4SayBot"
# Данные для подключения к бд
HOST = "localhost"
USER = "payforsay"
PASSWD = "PaY4SaY"
DATABASE = "test_payforsay"
# пароль для супер бота
SUPER_BOT_PASSWORD = "sayforpay261"
LOCALISATION_REDIS_DB = 6
POSTER_PLUGIN_LOCALISATION_REDIS_DB = 7

PLATFORM_ADMINS = {
    *********: True,  # dev Max-semka
    315373186: True,  # dev Ruslan
    394456075: False,  # Guy Secret
    397122680: False,  # Maxim Ronshin
    327721042: True,  # dev Eugene
}

MEDIA_UPLOAD_URL = "ta.payforsay.com/api/uploads/"

P4S_API_URL = "https://testapi.payforsay.com"

TIME_SLEEP_BETWEEN_STARTING_BOTS = 0

# API мониторинг клиента

session = "dev_pysariev"
api_id = 7752172
api_hash = "f605d66c8313e25b2a4b4cb223557768"

RUN_MONITORING = False

ROOT_BOT_PORT = 2009

DB_URL = f"mysql+pymysql://{USER}:{PASSWD}@{HOST}/{DATABASE}"

phone = "+************"

# переопределённые переменные

REPEAT_SCROLL_COUNT = 0

OASIS_SESSION = "oasis/account"
OASIS_API_ID = "********"
OASIS_API_HASH = "bdbda6812e001e64e99d188102a2b577"

WEB_APP_HOST = "https://testwebapp.payforsay.com"
WEB_APP_PATH = WEB_APP_HOST

LIP_LEP_API_HOST = "http://localhost"
LIP_LEP_API_PORT = 1916

LIP_LEP_BOT_URL = "http://127.0.0.1:2015"
LIP_LEP_WEB_APP_PATH = "https://app.liplep.com"

# don't run fastapi reload server on prod
FASTAPI_RELOAD = False
FASTAPI_DEBUG = False

TESTER_USERS = {
    "1": {
        "query_id": "1",
        "user": {
            "id": *********, "first_name": "Максим🇺🇦",
            "last_name": "Скуйбіда", "username": "Semka_96",
            "language_code": "uk",
        },
    },
}

REDIS_PREFIX = "test-server"

WHATSAPP_BOT_TOKEN = ""
WHATSAPP_BOT_FROM = ""

RUN_WHATSAPP = False

CROCKFORD_SECRET = 291643611538407651076939180748166897859
CROCKFORD_SHIFT = 32

WHATSAPP_WEBHOOK_URL = "https://wa.ms.dev.payforsay.com/whatsapp/{app_id}/"
WHATSAPP_WEBHOOK_URL_TOKEN = "payforsay"

INCUST_SERVER_API = 'https://testapi.incust.com'
INCUST_CLIENT_URL = 'https://testclient.incust.com'

API_RESTART = False
RELOAD_API_COMMAND = "systemctl reload api-payforsay-test"
PULL_API_COMMAND = 'git pull'

WEB_INDEX_HTML_PATH = "/var/www/test_payforsay_web/build/index.html"

ROOT_BOT_DEPLOY_WEB_COMMAND = "p4s_deploy_web test"

PL24API = "https://sandbox.przelewy24.pl"
PL24_PMT_PAGE = "https://sandbox.przelewy24.pl/trnRequest"

# TPAY_PMT_PAGE = "https://secure.tpay.com"
# TPAY_TRN_REQ = "https://secure.tpay.com/trnRequest"
# TPAY_OPENAPI = "https://api.tpay.com"

TPAY_OPENAPI = "https://openapi.sandbox.tpay.com"
TPAY_PMT_PAGE = "https://secure.sandbox.tpay.com"
TPAY_TRN_REQ = "https://secure.sandbox.tpay.com/trnRequest"

INCUST_EXTERNAL_SYSTEM_ID = "7loc_test"
# SECRET_KEY = "bf6cc2ab13328179a95b5a3e4815ef29b42618be9e743f0d4483614c8167e39f"

SYSTEM_NAME = "7loc_test"

GOOGLE_PLACES_GEOCODING_API_KEY = "AIzaSyBwZaW3t6xV_dJfCaI4wHMf0mUTqy-HDWQ"
GOOGLE_MAPS_API_KEY = "AIzaSyA9HNE-FyohRst4NWb5WzgID-KgSWHeDuw"

DEBUG = True
