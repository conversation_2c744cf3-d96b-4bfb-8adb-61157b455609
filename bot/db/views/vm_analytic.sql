CREATE OR REPLACE VIEW vm_analytic AS
    SELECT
        vmc.id AS `vm_chat_id`,
        DAT<PERSON>(vmc.started_datetime) as `date`,
        vmc.started_datetime as `datetime`,
        vm.id AS `vm_id`,
        vm.name AS `vm_name`,
        bots.id AS `bot_id`,
        bots.username AS `bot_username`,
        `groups`.id AS `group_id`,
        `groups`.name AS `group_name`,
        users.id as `user_id`,
        IF(users.username IS NOT NULL, CONCAT('@', users.username), users.full_name) as `user_name`,
        COUNT(messages.id) as `answers_count`,
        COUNT(messages.id) < 1 AS `is_level_0`,
        COUNT(messages.id) = 1 AS `is_level_1`,
        COUNT(messages.id) = 2 AS `is_level_2`,
        COUNT(messages.id) > 2 AND COUNT(messages.id) < 6 AS `is_level_3`,
        COUNT(messages.id) > 5 AND COUNT(messages.id) < 10 AS `is_level_4`,
        COUNT(messages.id) > 9 AND COUNT(messages.id) < 20 AS `is_level_5`,
        COUNT(messages.id) > 19 AS `is_level_6`
    FROM virtual_manager_chats AS `vmc`
    INNER JOIN virtual_managers AS `vm` ON vmc.virtual_manager_id = vm.id
    INNER JOIN users ON vmc.user_id = users.id
    INNER JOIN `groups` ON vmc.group_id = `groups`.id
    INNER JOIN bots ON vmc.bot_id = bots.id
    LEFT JOIN messages ON
        messages.virtual_manager_chat_id = vmc.id
            AND messages.user_id = users.id
            AND messages.chat_mode = 'chat_with_service'
    GROUP BY `vm_chat_id`
;
