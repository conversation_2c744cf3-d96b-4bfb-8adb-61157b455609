CREATE OR REPLACE VIEW bots_days_active_users AS
    SELECT DISTINCT
        bots_days_users.date as `date`,
        bots_days_users.bot_id as `bot_id`,
        bots_days_users.user_id as `user_id`
    FROM bots_days_users
    WHERE EXISTS(
        SELECT 1
        FROM user_analytic_actions
        WHERE user_analytic_actions.user_id = bots_days_users.user_id
          AND user_analytic_actions.bot_id = bots_days_users.bot_id
          AND
            DATE(user_analytic_actions.datetime)
                BETWEEN bots_days_users.date - INTERVAL 6 DAY AND bots_days_users.date
        )
;
