CREATE OR REPLACE VIEW friendly_members AS
    SELECT
        friendly_bot_analytic_actions.type,
        friendly_bot_analytic_actions.datetime,
        DATE(friendly_bot_analytic_actions.datetime) as `date`,

        friendly_bot_analytic_actions.member_id,
        friendly_bot_analytic_actions.recommender_id,

        friendly_bot_analytic_actions.channel_id,
        channels.name AS channel_name

    FROM friendly_bot_analytic_actions

    INNER JOIN channels on friendly_bot_analytic_actions.channel_id = channels.id

    WHERE friendly_bot_analytic_actions.type IN (
        'user_joined',
        'user_left',
        'user_returned'
    )

    GROUP BY friendly_bot_analytic_actions.id
;
