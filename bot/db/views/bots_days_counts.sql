CREATE OR REPLACE VIEW bots_days_counts AS
    SELECT
        bots_days.date as `date`,
        bots_days.bot_id as `bot_id`,
        bots_days.bot_username as `bot_username`,
        (
            SELECT
                COUNT(DISTINCT user_analytic_actions.user_id)
            FROM user_analytic_actions
            WHERE user_analytic_actions.bot_id = bots_days.bot_id
              AND DATE(user_analytic_actions.datetime) = bots_days.date
              AND user_analytic_actions.type = 'user_joined_to_bot'
            ) AS `new_users_count`,
        (
          SELECT COUNT(DISTINCT users.user_id)
          FROM
           (
               SELECT DISTINCT user_analytic_actions.user_id as `user_id`
               FROM user_analytic_actions
               WHERE user_analytic_actions.bot_id = bots_days.bot_id
                 AND DATE(user_analytic_actions.datetime) = bots_days.date - INTERVAL 6 DAY
               UNION

               SELECT DISTINCT user_analytic_actions.user_id as `user_id`
               FROM user_analytic_actions
               WHERE user_analytic_actions.bot_id = bots_days.bot_id
                 AND DATE(user_analytic_actions.datetime) = bots_days.date - INTERVAL 5 DAY

               UNION

               SELECT DISTINCT user_analytic_actions.user_id as `user_id`
               FROM user_analytic_actions
               WHERE user_analytic_actions.bot_id = bots_days.bot_id
                 AND DATE(user_analytic_actions.datetime) = bots_days.date - INTERVAL 4 DAY

               UNION

               SELECT DISTINCT user_analytic_actions.user_id as `user_id`
               FROM user_analytic_actions
               WHERE user_analytic_actions.bot_id = bots_days.bot_id
                 AND DATE(user_analytic_actions.datetime) = bots_days.date - INTERVAL 3 DAY

               UNION

               SELECT DISTINCT user_analytic_actions.user_id as `user_id`
               FROM user_analytic_actions
               WHERE user_analytic_actions.bot_id = bots_days.bot_id
                 AND DATE(user_analytic_actions.datetime) = bots_days.date - INTERVAL 2 DAY

               UNION

               SELECT DISTINCT user_analytic_actions.user_id as `user_id`
               FROM user_analytic_actions
               WHERE user_analytic_actions.bot_id = bots_days.bot_id
                 AND DATE(user_analytic_actions.datetime) = bots_days.date - INTERVAL 1 DAY

               UNION

               SELECT DISTINCT user_analytic_actions.user_id as `user_id`
               FROM user_analytic_actions
               WHERE user_analytic_actions.bot_id = bots_days.bot_id
                 AND DATE(user_analytic_actions.datetime) = bots_days.date
               ) as `users`
           ) AS `active_users_count`
    FROM bots_days
    GROUP BY `bot_id`, `date`
;
