from datetime import datetime

from sqlalchemy import <PERSON>umn, BigInteger, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship, backref

from db import models
from db.mixins import BaseDBModel
from db.my_columns import NestedMutableJson
from db.connection import Base
from db.helpers import sess
from db.decorators import db_func


class UserAdminBotActivity(Base, BaseDBModel):
    __tablename__ = "users_admin_bots_activity"

    id = Column(BigInteger, autoincrement=True, primary_key=True)
    is_active = Column(Boolean, default=True)

    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"), unique=True)
    user = relationship("User", foreign_keys=user_id, backref=backref("admin_bot_activity", uselist=False))

    is_content_admin = Column(Boolean, default=False)

    not_delete_vm_messages_mode = Column(Boolean, default=False)

    filters = Column(NestedMutableJson)

    last_activity = Column(DateTime(timezone=True), default=datetime.utcnow)

    def __init__(self, user: "models.User"):
        self.user = user

    @classmethod
    @db_func
    def create(cls, user: "models.User") -> "UserAdminBotActivity":
        user_admin_bot_activity = cls(user)
        sess().add(user_admin_bot_activity)
        sess().commit()
        return user_admin_bot_activity

    @classmethod
    @db_func
    def get(cls, user_id: int) -> "UserAdminBotActivity":
        return sess().query(cls).filter_by(user_id=user_id).one_or_none()

    @classmethod
    async def get_or_create(cls, user: "models.User", return_is_created: bool = False) -> "UserAdminBotActivity":
        user_admin_bot_activity = await cls.get(user.id)
        if user_admin_bot_activity:
            is_created = False
        else:
            is_created = True
            user_admin_bot_activity = await cls.create(user)
        return (user_admin_bot_activity, is_created) if return_is_created else user_admin_bot_activity

    @db_func
    def update_last_activity(self):
        self.last_activity = datetime.utcnow()
        sess().commit()

    @db_func
    def update(self, data: dict = None, **kwargs):
        if data:
            data.update(**kwargs)
        else:
            data = kwargs
        for key, value in data.items():
            if key in dir(self):
                setattr(self, key, value)
        sess().commit()

    @db_func
    def deactivate(self):
        self.is_active = False
        sess().commit()
