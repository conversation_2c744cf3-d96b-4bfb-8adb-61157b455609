from datetime import datetime

from sqlalchemy import (
    BigInteger, Column, DateTime, ForeignKey, String, Text,
    UniqueConstraint,
)
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin


class IncustCustomer(Base, BaseDBModel, TimeCreatedMixin):
    user_id: int = Column(BigInteger, ForeignKey("users.id", ondelete="RESTRICT"), nullable=False)
    user: "models.User" = relationship("User", backref="incust_customers")

    brand_id: int = Column(BigInteger, ForeignKey("brands.id", ondelete="RESTRICT"), nullable=False)
    brand: "models.Brand" = relationship("Brand", backref="incust_customers")

    token: str = Column(Text, nullable=False)
    token_update_datetime: datetime = Column(DateTime, nullable=False, default=datetime.utcnow)

    push_token: str | None = Column(Text, nullable=True)
    push_token_wl_id: str | None = Column(String(20), nullable=True)
    push_token_update_datetime: datetime | None = Column(DateTime, nullable=True)

    __table_args__ = (
        UniqueConstraint('user_id', 'brand_id', name='uix_incust_customer_user_brand'),
    )

    @classmethod
    def validate_push_token_values(cls, push_token: str | None, push_token_wl_id: str | None):
        values = (push_token, push_token_wl_id)
        if any(values) and not all(values):
            raise ValueError("Specify both of (push_token, push_token_wl_id) or not specify both")

    @classmethod
    def create(
            cls,
            user_or_id: "models.User | int",
            brand_or_id: "models.Brand | None | int",
            token: str,
            push_token: str | None = None,
            push_token_wl_id: str | None = None,
    ):
        if not all((user_or_id, brand_or_id, token)):
            raise ValueError("user_or_id, brand_or_id and token are required fields")

        cls.validate_push_token_values(push_token, push_token_wl_id)

        kwargs = {
            "token": token,
            "push_token": push_token,
            "push_token_wl_id": push_token_wl_id,
        }

        for key, value in (("user", user_or_id), ("brand", brand_or_id)):
            if isinstance(value, int):
                kwargs[f"{key}_id"] = value
            else:
                kwargs[key] = value

        return super().create(**kwargs)

    def set_token(self, token: str, no_commit: bool = False):
        return self.update(
            token=token,
            token_update_datetime=datetime.utcnow(),
            no_commit=no_commit,
        )

    def set_push_token(self, push_token: str | None, push_token_wl_id: str | None):
        self.validate_push_token_values(push_token, push_token_wl_id)
        return self.update(
            push_token=push_token,
            push_token_wl_id=push_token_wl_id,
            push_token_update_datetime=datetime.utcnow()
        )
