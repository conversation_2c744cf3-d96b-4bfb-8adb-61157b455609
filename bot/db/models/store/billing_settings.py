from sqlalchemy import BigInteger, Boolean, Column, ForeignKey

from db import db_func, sess
from db.connection import Base


class BillingSettings(Base):
    __tablename__ = "billing_settings"

    id = Column(BigInteger, primary_key=True)
    brand_id: int = Column(BigInteger, ForeignKey("brands.id", ondelete="CASCADE"), nullable=True)
    store_id: int = Column(BigInteger, ForeignKey("stores.id", ondelete="CASCADE"), nullable=True)

    is_enable: bool = Column(Boolean, default=False)
    is_require: bool = Column(Boolean, default=False)
    need_person: bool = Column(Boolean, default=False)
    need_organisation: bool = Column(Boolean, default=False)

    def __repr__(self):
        return f"<BillingSettings {self.id}>"

    @classmethod
    @db_func
    def create(cls, object_type: str, object_id: int) -> "BillingSettings":
        if object_type == "Store":
            billing_settings = cls(store_id=object_id)
        else:
            billing_settings = cls(brand_id=object_id, is_enable=True)
        sess().add(billing_settings)
        sess().commit()
        return billing_settings

    @db_func
    def update(self, **kwargs) -> bool:
        for k, v in kwargs.items():
            if k not in dir(self) or k == "id":
                continue
            setattr(self, k, v)
        sess().commit()
        return True

    @db_func
    def delete(self) -> bool:
        sess().delete(self)
        sess().commit()
        return True

    @classmethod
    @db_func
    def get_by_id(cls, id_: int) -> "BillingSettings":
        billing_settings = sess().query(cls).filter_by(id=id_).one_or_none()
        return billing_settings

    @classmethod
    @db_func
    def get_by_brand(cls, brand_id: int) -> "BillingSettings":
        query = sess().query(cls)
        query = query.filter(cls.brand_id == brand_id)
        query = query.filter(cls.store_id.is_(None))
        billing_settings = query.first()
        return billing_settings

    @classmethod
    @db_func
    def get_by_store(cls, store_id: int) -> "BillingSettings":
        query = sess().query(cls)
        query = query.filter(cls.store_id == store_id)
        billing_settings = query.first()
        return billing_settings

    @classmethod
    async def get(cls, object_type: str, object_id: int) -> "BillingSettings":
        if object_type == "Store":
            setting = await cls.get_by_store(object_id)
        else:
            setting = await cls.get_by_brand(object_id)
        return setting

    @classmethod
    async def enable_or_disable(cls, object_type: str, object_id: int) -> "BillingSettings":
        if not all([object_type, object_id]):
            raise ValueError("object must be Brand or Store exists object")

        setting = await cls.get(object_type, object_id)
        if setting:
            await setting.update(is_enable=not setting.is_enable)
            return setting

        return await cls.create(object_type, object_id)
