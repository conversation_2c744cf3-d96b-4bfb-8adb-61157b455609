import uuid
from datetime import datetime

from sqlalchemy import (
    BigInteger, Boolean, Column, DateTime, Enum, ForeignKey,
    String, \
    Text,
)
from sqlalchemy.orm import relationship

from core.ext.types import ActionType, ExternalAPITypeLiteral, StatusType
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin


class DataPorter(Base, BaseDBModel, TimeCreatedMixin):

    action_type: ActionType = Column(Enum(ActionType), nullable=False)
    external_type: ExternalAPITypeLiteral = Column(String(10), nullable=False)
    status: StatusType = Column(
        Enum(StatusType), nullable=False, default=StatusType.NOT_STARTED
    )

    uuid_id: str = Column(
        String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4())
    )

    start_time: datetime | None = Column(DateTime, nullable=True, default=None)
    end_time: datetime | None = Column(DateTime, nullable=True, default=None)

    user_id: int | None = Column(
        BigInteger, ForeignKey("users.id", ondelete="SET NULL"), nullable=True,
        default=None
    )
    user = relationship("User", foreign_keys=user_id)
    message_id: int | None = Column(BigInteger, nullable=True, default=None)

    brand_id: int = Column(
        BigInteger, ForeignKey("brands.id", ondelete="CASCADE"), nullable=False
    )
    brand = relationship("Brand", foreign_keys=brand_id)

    lang: str | None = Column(String(2), nullable=True, default=None)
    user_lang: str | None = Column(String(2), nullable=True, default=None)
    prom_file_main_lang: str | None = Column(String(2), nullable=True, default=None)

    source_data: str | None = Column(
        Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None
    )
    result_data: str | None = Column(
        Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None
    )
    error_text: str | None = Column(
        Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None
    )
    callback_url: str | None = Column(String(256), nullable=True, default=None)
    is_read: bool | None = Column(Boolean, nullable=True, default=False)
