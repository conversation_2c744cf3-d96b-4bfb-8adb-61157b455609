from datetime import datetime
from sqlalchemy import <PERSON>umn, BigInteger, ForeignKey, String, DateTime, UniqueConstraint
from sqlalchemy.dialects.mysql import JSON
from db.connection import Base
from db import sess, db_func
from db.mixins import BaseDBModel


class BrandSettings(Base, BaseDBModel):
    __tablename__ = "brand_settings"
    brand_id: int = Column(BigInteger, ForeignKey('brands.id'))
    type_data: str = Column(String(50), nullable=False)
    json_data: dict = Column(JSON(none_as_null=True), nullable=True, default=None)
    create_date: datetime = Column(DateTime, default=datetime.utcnow)
    update_date: datetime = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    value_data: str = Column(String(255), nullable=True, default=None)
    name_data: str = Column(String(99), nullable=True, default=None)

    __table_args__ = (
        UniqueConstraint("brand_id", "type_data"),
    )

    def __repr__(self):
        return '<BrandSettings %r>' % self.id

    @classmethod
    @db_func
    def create(
        cls,
        brand_id: int,
        type_data: str,
        json_data: dict = None,
        value_data: str = None
    ):
        brand_settings = cls(
            brand_id=brand_id,
            type_data=type_data,
            json_data=json_data,
            value_data=value_data,
        )
        sess().add(brand_settings)
        sess().commit()
        return brand_settings

    @db_func
    def delete(self) -> bool:
        sess().delete(self)
        sess().commit()
        return True

    @classmethod
    @db_func
    def get_by_brand_and_type(
            cls,
            brand_id: int,
            type_data: str,
            return_value: bool = False
    ) -> "BrandSettings | str | None":
        brand_settings = sess().query(cls).filter_by(brand_id=brand_id).\
            filter_by(type_data=type_data).first()
        if return_value:
            if brand_settings:
                return brand_settings.value_data
            return None
        return brand_settings

    @classmethod
    @db_func
    def get_by_brand(cls, brand_id: int,) -> list["BrandSettings"]:
        brand_settings = sess().query(cls).filter_by(brand_id=brand_id).\
            all()
        return brand_settings

    @classmethod
    @db_func
    def get_settings_by_type(cls, brand_id: int, type_data: str) -> "BrandSettings":
        brand_settings = sess().query(cls).filter_by(brand_id=brand_id).\
            filter_by(type_data=type_data).one()
        return brand_settings

    @classmethod
    async def create_or_update(
            cls,
            brand_id: int,
            type_data: str,
            json_data: dict = None,
            value_data: str = None,
    ):
        setting: BrandSettings = await cls.get_by_brand_and_type(brand_id, type_data)
        if setting:
            await setting.update(json_data=json_data, value_data=value_data)
            return True

        await cls.create(brand_id, type_data, json_data, value_data)
        return True
