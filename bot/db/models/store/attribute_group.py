from typing import List

from sqlalchemy import (
    <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>n, <PERSON><PERSON>ey, Integer, String,
    UniqueConstraint, func,
)
from sqlalchemy.dialects.mysql import SMALLINT
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

from db import db_func, models, sess
from db.connection import Base
from db.mixins import BaseDBModel


class StoreAttributeGroup(Base, BaseDBModel):
    attribute_group_id: str = Column(String)

    name: str = Column(String(255))
    min: int = Column(Integer)
    max: int = Column(Integer)
    is_deleted: bool = Column(Boolean, default=False)

    external_id: str = Column(String(255))
    external_type: str = Column(String(99))

    attributes: list["models.StoreAttribute"] = relationship(
        "StoreAttribute", back_populates="attribute_group"
    )

    products: list["models.StoreProduct"] = relationship(
        "StoreProduct",
        secondary="attribute_groups_to_products",
        back_populates="attribute_groups"
    )

    brand_id: int = Column(BigInteger, ForeignKey("brands.id", ondelete="CASCADE"))
    brand: "models.Brand" = relationship("Brand", foreign_keys=brand_id)

    position: int = Column(Integer)
    excel_row_number: int | None = Column(SMALLINT(unsigned=True), nullable=True)

    _internal_name = Column(String(255), nullable=True, default=None)

    @hybrid_property
    def internal_name(self):
        return self._internal_name or self.name

    @internal_name.expression
    def internal_name(self):
        return func.IFNULL(self._internal_name, self.name)

    @hybrid_property
    def raw_internal_name(self):
        return self._internal_name

    @raw_internal_name.setter
    def raw_internal_name(self, value):
        self._internal_name = value

    @classmethod
    @db_func
    def get_all(cls, brand_id: int) -> List["StoreAttributeGroup"]:
        attribute_groups = sess().query(cls).filter(cls.brand_id == brand_id).all()
        return attribute_groups

    @db_func
    def update(self, **kwargs) -> bool:
        for k, v in kwargs.items():
            if k not in dir(self) or k == "id":
                continue

            setattr(self, k, v)

        sess().commit()
        return True


class AttributeGroupToProduct(Base):
    __tablename__ = "attribute_groups_to_products"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    attribute_group_id = Column(BigInteger, ForeignKey("store_attribute_groups.id"))
    product_id = Column(BigInteger, ForeignKey("store_products.id"))

    __table_args__ = (
        UniqueConstraint(
            'attribute_group_id', 'product_id', name='uq_attribute_group_product'
        ),
    )
