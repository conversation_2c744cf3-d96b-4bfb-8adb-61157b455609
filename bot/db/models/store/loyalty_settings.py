from sqlalchemy import (
    BigInteger, Boolean, <PERSON>umn, DateTime, Enum as S<PERSON><PERSON><PERSON>, ForeignKey, Integer, String,
    desc,
)

from core.store.schemas.loyalty import (
    LoyaltySettingsApplicableTypes, LoyaltySettingsTypeClientAuth,
)
from db.connection import Base
from db.decorators import db_func
from db.helpers import sess
from db.mixins import BaseDBModel, TimeCreatedMixin
from db.my_columns import NestedMutableJson
from utils.date_time import utcnow


class LoyaltySettings(Base, BaseDBModel, TimeCreatedMixin):

    __tablename__ = "loyalty_settings"
    
    ewallet_id: int | None = Column(
        BigInteger, ForeignKey("ewallets.id", ondelete="CASCADE"), index=True
    )

    profile_id: int | None = Column(
        BigInteger, ForeignKey("groups.id", ondelete="CASCADE"), index=True
    )

    brand_id: int | None = Column(
        BigInteger, ForeignKey("brands.id", ondelete="CASCADE"), index=True
    )

    invoice_template_id = Column(
        BigInteger, ForeignKey("invoice_templates.id", ondelete="CASCADE"), index=True
    )
    
    store_id: int | None = Column(
        BigInteger, ForeignKey("stores.id", ondelete="CASCADE"), index=True
    )
    
    product_id: int | None = Column(
        BigInteger, ForeignKey("store_products.id", ondelete="CASCADE"), index=True
    )
    
    # Основні налаштування InCust
    white_label_id = Column(String(255), nullable=False)
    terminal_api_key = Column(String(255), nullable=False)
    terminal_id = Column(String(255), nullable=False)
    loyalty_id = Column(String(255), nullable=False)
    server_url = Column(String(255), nullable=False)
    
    # Налаштування клієнта
    client_url = Column(String(255))
    type_client_auth = Column(SQLEnum(LoyaltySettingsTypeClientAuth))

    prohibit_redeeming_bonuses = Column(Boolean, default=False, nullable=False)
    prohibit_redeeming_coupons = Column(Boolean, default=False, nullable=False)
    
    # Налаштування застосування лояльності
    loyalty_applicable_type = Column(
        SQLEnum(LoyaltySettingsApplicableTypes),
        nullable=False,
        default=LoyaltySettingsApplicableTypes.FOR_PARTICIPANTS,
    )

    priority = Column(Integer, default=0, nullable=False)

    is_enabled = Column(Boolean, default=True, nullable=False)
    
    # Назва для адмін панелі
    name = Column(String(255))
    description = Column(String(1000))

    time_updated = Column(DateTime, default=utcnow, nullable=False)

    json_data = Column(NestedMutableJson, nullable=True, default=None)
    
    # Метод для копіювання налаштувань
    def copy_from(self, source: "LoyaltySettings") -> None:
        """Копіює всі налаштування з іншого об'єкта"""
        skip_fields = {
            "id", "ewallet_id", "profile_id", "store_id", "invoice_template_id",
            "product_id", "name", "description"
        }
        
        for column in self.__table__.columns:
            field_name = column.name
            if field_name not in skip_fields:
                setattr(self, field_name, getattr(source, field_name))

    @classmethod
    @db_func
    def get_loyalty_settings_for_context(
            cls,
            group_id: int = None,
            ewallet_id: int = None,
            brand_id: int = None,
            store_id: int = None,
            product_id: int = None,
            invoice_template_id: int = None,
            terminal_api_key: str = None,
    ) -> "LoyaltySettings | None":
        """
        Отримує налаштування лояльності з найвищим пріоритетом для заданого контексту.
        
        Логіка: шукає послідовно від найбільш специфічного до найменш специфічного контексту.
        Якщо для певного рівня специфічності знайдено записи - повертає найкращий з них.

        Args:
            group_id: ID групи/профілю
            ewallet_id: ID гаманця
            brand_id: ID бренду
            store_id: ID магазину
            product_id: ID продукту
            invoice_template_id: ID шаблону рахунку
            terminal_api_key: АПІ ключ терміналу

        Returns:
            Найкращі налаштування лояльності або None
        """
        base_query = sess().query(cls).filter(cls.is_enabled == True)
        
        # Шукаємо послідовно від найбільш до найменш специфічного

        if terminal_api_key is not None:
            result = base_query.filter(cls.terminal_api_key == terminal_api_key).order_by(desc(cls.priority), desc(cls.id)).first()
            if result:
                return result
        
        # 1. Спочатку шукаємо точний збіг по product_id
        if product_id is not None:
            result = base_query.filter(cls.product_id == product_id).order_by(desc(cls.priority), desc(cls.id)).first()
            if result:
                return result
        
        # 2. Шукаємо точний збіг по store_id
        if store_id is not None:
            result = base_query.filter(cls.store_id == store_id).order_by(desc(cls.priority), desc(cls.id)).first()
            if result:
                return result
        
        # 3. Шукаємо точний збіг по invoice_template_id
        if invoice_template_id is not None:
            result = base_query.filter(cls.invoice_template_id == invoice_template_id).order_by(desc(cls.priority), desc(cls.id)).first()
            if result:
                return result
        
        # 4. Шукаємо точний збіг по ewallet_id
        if ewallet_id is not None:
            result = base_query.filter(cls.ewallet_id == ewallet_id).order_by(desc(cls.priority), desc(cls.id)).first()
            if result:
                return result
        
        # 5. Шукаємо точний збіг по brand_id (тільки загальні налаштування для бренду)
        if brand_id is not None:
            result = base_query.filter(
                cls.brand_id == brand_id,
                cls.product_id.is_(None),
                cls.store_id.is_(None),
                cls.invoice_template_id.is_(None),
                cls.ewallet_id.is_(None)
            ).order_by(desc(cls.priority), desc(cls.id)).first()
            if result:
                return result
        
        # 6. Шукаємо точний збіг по group_id (profile_id) - найменш специфічний
        if group_id is not None:
            result = base_query.filter(
                cls.profile_id == group_id,
                cls.product_id.is_(None),
                cls.store_id.is_(None),
                cls.invoice_template_id.is_(None),
                cls.ewallet_id.is_(None),
            ).order_by(desc(cls.priority), desc(cls.id)).first()
            if result:
                return result
        
        return None
