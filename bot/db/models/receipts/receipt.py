from datetime import datetime
from typing import Any, Dict

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Column, DateTime, Float, ForeignKey, String, func
from sqlalchemy.orm import relationship

import config as cfg
from db import db_func, models, sess
from db.connection import Base
from db.my_columns import NestedMutableJson
from utils.numbers import format_currency
from utils.text import f, value_or_empty_text
from .exceptions import ReceiptAlreadyScanned


def convert_tow(name):
    res = name.replace("I", "І")
    if "ТОВАРИСТВО З ОБМЕЖЕНОЮ ВІДПОВІДАЛЬНІСТЮ" in name:
        res = name.replace("ТОВАРИСТВО З ОБМЕЖЕНОЮ ВІДПОВІДАЛЬНІСТЮ", "ТОВ")
    return res


class Receipt(Base):
    __tablename__ = "receipts"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    receipt_id = Column(String(50))

    time_scanned = Column(DateTime(timezone=True), default=datetime.utcnow)
    issue_datatime = Column(DateTime(timezone=True), default=datetime.utcnow)

    user_id: int = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    user: "models.User" = relationship("User", foreign_keys=user_id)

    organisation_id = Column(BigInteger, ForeignKey("organisations.id"))
    organisation = relationship("Organisation", foreign_keys=organisation_id)

    pos_id = Column(BigInteger, ForeignKey("pos.id"))
    pos = relationship("Pos", foreign_keys=pos_id)

    ppo_id = Column(BigInteger, ForeignKey("ppo.id"))
    ppo = relationship("Ppo", foreign_keys=ppo_id)

    items = relationship("ReceiptItem", back_populates="receipt")
    json_data = Column(NestedMutableJson)
    total_price = Column(Float, default=0.0)

    store_order_id: int = Column(
        BigInteger, ForeignKey("store_orders.id", ondelete="CASCADE"),
        nullable=True
    )
    store_order: "models.StoreOrder" = relationship("StoreOrder", foreign_keys=store_order_id)
    group_id = Column(BigInteger, ForeignKey("groups.id", ondelete="RESTRICT"), nullable=True)
    group: "models.Group" = relationship("Group")
    incust_check = Column(NestedMutableJson, default=None)
    incust_transaction = Column(NestedMutableJson, default=None)

    def __init__(
            self,
            receipt_id: int,
            organisation: "models.Organisation",
            pos: "models.Pos",
            total_price: float,
            issue_datetime: datetime,
            json_data: dict,
            user: "models.User",
            incust_check: dict | None = None,
            ppo_id: int = None,
            brand: "models.Brand" = None,
            group_id: int | None = None,
            order_id: int | None = None,
    ):
        assert receipt_id
        assert isinstance(total_price, int | float)
        assert issue_datetime
        assert type(json_data) is dict

        self.receipt_id = receipt_id
        self.organisation = organisation
        self.pos = pos
        self.total_price = total_price
        self.issue_datetime = issue_datetime
        self.json_data = json_data
        self.ppo_id = ppo_id

        self.brand = brand
        self.group_id = group_id
        self.user = user
        self.store_order_id = order_id
        self.incust_check = incust_check

    @classmethod
    @db_func
    def create(
            cls,
            receipt_id: int,
            organisation: "models.Organisation",
            pos: "models.Pos",
            total_price: float,
            issue_datetime: datetime,
            json_data: dict,
            user: "models.User",
            ppo_id: int = None,
    ):
        receipt = cls(receipt_id, organisation, pos, total_price, issue_datetime, json_data, ppo_id=ppo_id, user=user)
        sess().add(receipt)
        sess().commit()
        return receipt

    @classmethod
    async def save(cls, json_data: Dict[str, Any]) -> "Receipt":
        receipt_id = json_data["receipt"]["receiptId"]
        issue_datetime = json_data["receipt"]["issueDate"]
        receipt = await Receipt.get_by_receipt_id(receipt_id)
        if receipt:
            raise ReceiptAlreadyScanned()

        organisation_json = json_data["receipt"]["organization"]
        country = organisation_json["country"]
        ico = organisation_json["ico"]

        organisation = await models.Organisation.get(ico, country)
        if organisation is None:
            organisation = await models.Organisation.create_by_json(organisation_json)

        pos_json = json_data["receipt"]["unit"]
        pos = await models.Pos.get(pos_json)
        if pos is None:
            pos = await models.Pos.create_by_json(pos_json, organisation.id)

        total_price = json_data["receipt"]["totalPrice"]
        receipt = await cls.create(receipt_id, organisation, pos, total_price, issue_datetime, json_data)

        if json_data["receipt"]["items"] is not None:
            await models.ReceiptItem.create_items_by_list(receipt.id, json_data["receipt"]["items"])
        return receipt

    @classmethod
    async def save_ua(
            cls,
            receipt_info: Dict[str, Any],
            ppo_: Dict[str, Any],
            items: Dict[str, Any],
            additional_data: Any,  # Дополнительная информация про чек(
            # используется для создания организации в случае, кога невозможно сооздать организацию с ппо)
    ):
        receipt_id = receipt_info["id"]
        issue_datetime = receipt_info["date"] if "date" in receipt_info else datetime.utcnow()
        ppo_id = receipt_info["fn"]
        receipt = await Receipt.get_by_receipt_id(receipt_id)
        if receipt:
            raise ReceiptAlreadyScanned()

        organisation = None
        ppo = None
        if ppo_:
            full_name = convert_tow(ppo_["FULL_NAME"])
            ppo_data = ppo_["DATA"][0]
            ppo = await models.Ppo.get(ppo_id)
            if ppo is None:
                ppo = await models.Ppo.create(
                    ppo_data["N_FIS"],
                    full_name,
                    ppo_["TIN_S"],
                    ppo_data["D_REG_DATE"],
                    ppo_data["SFERA"],
                    ppo_data["LAST_N_POR"],
                    ppo_data["D_REG_LAST_KORO"],
                    ppo_data["ADDRESS"]
                )
            # Добавить организацю в случае, когда есть ппо
            organisation = await models.Organisation.get_ua("Ukraine", name=full_name)
            if organisation is None:
                params = {
                    "country": "Ukraine",
                    "ico": None,
                    "name": full_name,
                    "municipality": None,
                    "postalCode": None,
                    "streetName": ppo_data["ADDRESS"],
                    "inn": ppo_data["N_FIS"],
                }
                organisation = await models.Organisation.create_by_json(params)
        elif additional_data[0]["name"] and additional_data[0]["fn"]:
            # Добавить организацю в случае, когда нет ппо и есть информация в чеке
            full_name = convert_tow(additional_data[0]["name"])
            organisation = await models.Organisation.get_ua("Ukraine", name=full_name)
            if organisation is None:
                params = {
                    "country": "Ukraine",
                    "ico": None,
                    "name": full_name,
                    "municipality": None,
                    "postalCode": None,
                    "streetName": None,
                    "inn": additional_data[0]["fn"],
                }
                organisation = await models.Organisation.create_by_json(params)
        elif "CHECKHEAD" in str(additional_data[1]):
            # Добавить организацю для чеков новой почты
            data = additional_data[1]["CHECK"]["CHECKHEAD"]
            full_name = convert_tow(data["ORGNM"])
            organisation = await models.Organisation.get_ua("Ukraine", name=full_name)
            if organisation is None:
                params = {
                    "country": "Ukraine",
                    "ico": None,
                    "name": full_name,
                    "municipality": None,
                    "postalCode": None,
                    "streetName": None,
                    "inn": receipt_info["fn"],
                }
                organisation = await models.Organisation.create_by_json(params)

        receipt = await cls.create(
            receipt_id,
            organisation if organisation else None,
            None,
            items["total_price"],
            issue_datetime,
            {
                "ppo_info": ppo_,
                "items": items
            },
            ppo.id if ppo else None
        )

        await models.ReceiptItem.create_items_by_list(receipt.id, items["items"])

        return receipt

    @classmethod
    @db_func
    def get(cls, id: str):
        receipt = sess().query(cls).filter_by(id=id).one()
        return receipt

    @classmethod
    @db_func
    def get_by_receipt_id(cls, receipt_id: str):
        receipt = sess().query(cls).filter_by(receipt_id=receipt_id).one()
        return receipt

    def get_total_price_with_currency(self, lang: str):
        country = self.organisation.country if self.organisation else "Ukraine"
        currency = cfg.COUNTRY_CURRENCY[country]
        return format_currency(self.total_price, currency, locale=lang)

    async def get_info_str(self, lang: str):
        date_time = self.issue_datatime.strftime("%d.%m.%Y %H:%M")
        total_price = self.get_total_price_with_currency(lang)

        receipt_info = await f(
            "receipt view", lang,
            receipt_id=self.receipt_id,
            datetime=await value_or_empty_text(date_time, lang),
            total_price=await value_or_empty_text(total_price, lang)
        )
        if self.organisation:
            organisation_info = await self.organisation.get_info_str(lang)
            receipt_info += f"\n\n{organisation_info}"
        if self.pos:
            pos_info = await self.pos.get_info_str(lang)
            receipt_info += f"\n\n{pos_info}"

        return receipt_info

    @db_func
    def get_items(
            self,
            position: int = 0,
            limit: int = None,
            operation: str = "all",
    ):
        if operation == "count":
            query = sess().query(func.count(models.ReceiptItem.id))
        else:
            query = sess().query(models.ReceiptItem)

        query = query.filter(models.ReceiptItem.receipt_id == self.id)

        slice_args = [position, None]
        if limit:
            slice_args[1] = position + limit
        query = query.slice(*slice_args)

        if operation == "count":
            receipt_items = query.scalar()
        else:
            receipt_items = query.all()
        return receipt_items
