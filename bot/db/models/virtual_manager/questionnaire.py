import re
import random

from typing import Any, Dict, List, Optional, Literal

from sqlalchemy import <PERSON>umn, String, BigInteger, ForeignKey, <PERSON><PERSON>an
from sqlalchemy.orm import relationship

from db import models
from db.decorators import db_func
from db.connection import Base
from db.helpers import sess
from db.my_columns import NestedMutableJson

from utils.media import delete_file


class Questionnaire(Base):

    __tablename__ = "questionnaires"

    id = Column(BigInteger, autoincrement=True, primary_key=True)
    is_deleted = Column(Boolean, default=False)
    name = Column(String(100, collation="utf8mb4_unicode_ci"))
    group_id = Column(BigInteger, ForeignKey("groups.id", ondelete="CASCADE"))
    group = relationship("Group", backref="questionnaires")
    questions = Column(NestedMutableJson)

    REQUIRED_CREATE_EVENT_TAGS = ["<event_name>", "<event_title>", "<event_description>"]

    ALLOWED_CREATE_EVENT_TAGS = [
                                    "<event_media",
                                    "<event_datetime>",
                                    "<event_end_date>",
                                    "<event_address>"
                                ] + REQUIRED_CREATE_EVENT_TAGS

    SUPPORTED_QUESTION_FORMATS = ["text", "num", "date", "time", "datetime", "email", "photo", "media"]

    def __init__(self, group_id: int, name: str, questions: List[Dict[str, Any]]):
        self.group_id = group_id
        self.name = name
        self.questions = questions or list()
        super().__init__()

    @db_func
    def update_name(self, new_name: str):
        self.name = new_name
        sess().commit()

    def get_question(self, question_id: int | Literal["random"]) -> Optional[dict]:
        if question_id == "random":
            return random.choice(self.questions)
        elif question_id is not None:
            if isinstance(question_id, str) and question_id.isdecimal():
                question_id = int(question_id)
            elif not isinstance(question_id, int):
                return

            if question_id is not None and question_id < len(self.questions):
                return self.questions[question_id]

    @staticmethod
    def get_question_or_answer_content_type(data: dict):
        if len(data.keys()) == 0:
            return "text"

        if len(data.keys()) == 1:
            content_type = list(data.keys())[0]
        else:
            content_type = [key for key in data.keys() if key != "text"][0]
        return content_type

    @db_func
    def update_question(self, question_id: int, updated_question_data: dict = None, delete_media: bool = False) -> dict:
        if not updated_question_data:
            updated_question_data = dict()

        question = self.get_question(question_id)

        content_type = self.get_question_or_answer_content_type(question)
        new_content_type = self.get_question_or_answer_content_type(updated_question_data)

        if content_type != "text" and new_content_type != "text":
            old_media = question.get(content_type)
            delete_file(old_media)

        text = updated_question_data.pop("text", None) or question.get("text") or ""

        if delete_media or updated_question_data:
            question.clear()

        question.update(text=text, **updated_question_data)

        sess().commit()
        return question

    @db_func
    def add_question(self, question: dict) -> int:
        self.questions.append(question)
        sess().commit()
        return len(self.questions) - 1

    @staticmethod
    def get_question_text(
            question_text: str, user: "models.User" = None,
            vmc: "" = None,
            bot: "models.ClientBot" = None,
    ) -> str:
        if not bot:
            bot = vmc.bot if vmc else None

        format_texts = re.findall(r"<format [^<>]+>", question_text)
        for format_text in format_texts:
            question_text = question_text.replace(format_text, "")

        buttons_texts = re.findall(r"<buttons[^<>]*>", question_text)
        for buttons_text in buttons_texts:
            question_text = question_text.replace(buttons_text, "")

        simple_buttons_texts = re.findall(r"\[[^\[\]]+]", question_text)
        for simple_buttons_text in simple_buttons_texts:
            question_text = question_text.replace(simple_buttons_text, "")

        tag_texts = re.findall(r"<event[^<>]*>", question_text)
        for tag_text in tag_texts:
            question_text = question_text.replace(tag_text, "")

        continue_tags = re.findall(r"<continue[^<>]*>", question_text)
        for continue_tag in continue_tags:
            question_text = question_text.replace(continue_tag, "")

        no_remind_tags = re.findall(r"<noremind>", question_text, flags=re.IGNORECASE)
        for no_remind_tag in no_remind_tags:
            question_text = question_text.replace(no_remind_tag, "")

        remind_tags = re.findall(r"<remind(?: after ?= ?\d+)?(?: count ?= ?\d+)?>", question_text, flags=re.IGNORECASE)
        for remind_tag in remind_tags:
            question_text = question_text.replace(remind_tag, "")

        set_var_tags = re.findall(r"<setVar[^<>]+>", question_text, flags=re.IGNORECASE)
        for set_var_tag in set_var_tags:
            question_text = question_text.replace(set_var_tag, "")

        set_input_to_var_tags = re.findall(r"<setInputTo[^<>]+>", question_text, flags=re.IGNORECASE)
        for set_input_to_var_tag in set_input_to_var_tags:
            question_text = question_text.replace(set_input_to_var_tag, "")

        notify_tags = re.findall(r"\{notify\([^{}()]+\)}", question_text, flags=re.IGNORECASE)
        for notify_tag in notify_tags:
            question_text = question_text.replace(notify_tag, "")

        question_text = re.sub(r"<ticket[^<>]*>", "", question_text, flags=re.IGNORECASE)

        allowed_objects = dict()
        if user:
            allowed_objects.update(user=user)
        if vmc:
            allowed_objects.update(vmc=vmc, vm=vmc.virtual_manager)
        if bot:
            allowed_objects.update(bot=bot)
        try:
            text_vars = re.findall(r"{\w+}", question_text)
        except:
            pass
        else:
            for var in text_vars:
                if var.lower() == "{recommenderlink}" and bot and user:
                    recommender_link = bot.get_link_for_share(user.id)
                    question_text = question_text.replace(var, recommender_link)
                    continue

                if var.lower() == "{firstname}" and user:
                    question_text = question_text.replace(var, user.first_name or "")
                if var.lower() == "{lastname}" and user:
                    question_text = question_text.replace(var, user.last_name or "")
                if var.lower() == "{fullname}" and user:
                    question_text = question_text.replace(var, user.name or "")
                if var.lower() == "{username}" and user:
                    question_text = question_text.replace(var, user.username or "")

                split_var = var[1:-1].split("_", 1)
                if len(split_var) != 2:
                    continue
                obj_type, variable = split_var
                obj = allowed_objects.get(obj_type)
                if not obj:
                    continue
                if variable not in dir(obj):
                    continue
                value = getattr(obj, variable)
                question_text = question_text.replace(var, str(value))

        return question_text

    @staticmethod
    def get_question_formats(question_text: str) -> List[str]:
        format_texts = re.findall(r"<format [^<>]+>", question_text)
        return [format_text[8:-1].strip() for format_text in format_texts]

    @staticmethod
    def question_has_media(question: dict) -> bool:
        return len(question.keys()) > 1

    @db_func
    def change_questions_order(self, question_id: int, up_or_down: Literal["up", "down"]) -> int:
        questions: list = list(self.questions)
        if up_or_down == "down":
            new_question_id = question_id + 1
        else:
            new_question_id = question_id - 1
        if new_question_id >= len(questions):
            new_question_id = 0
            questions.insert(new_question_id, questions.pop(question_id))
        elif new_question_id < 0:
            new_question_id = len(questions) + new_question_id
            questions.insert(new_question_id, questions.pop(question_id))
        else:
            questions[question_id], questions[new_question_id] = questions[new_question_id], questions[question_id]
            if new_question_id < 0:
                new_question_id = len(questions) + new_question_id
        self.questions = questions
        sess().commit()
        return new_question_id

    @property
    def with_virtual_manager(self):
        result = bool(self.virtual_manager)
        return result

    @db_func
    def delete(self) -> bool:
        assert self.with_virtual_manager is False
        self.is_deleted = True
        sess().commit()
        return True

    def __str__(self):
        return self.name
