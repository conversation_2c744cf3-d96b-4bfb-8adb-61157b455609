from __future__ import annotations

from datetime import datetime
from typing import Literal, TYPE_CHECKING

from sqlalchemy import Column, BigInteger, ForeignKey, func
from sqlalchemy import <PERSON>olean, Text, DateTime
from sqlalchemy.orm import relationship

from db.connection import Base
from db import sess, db_func, models
from db.mixins import BaseDBModel
from schemas import InvoicePaymentModeEnum

if TYPE_CHECKING:
    from db.models import Store, Group, InvoiceTemplate


class MenuInStore(Base, BaseDBModel):
    __tablename__ = "menus_in_store"

    comment: str = Column(Text(collation="utf8mb4_unicode_ci"), nullable=False)

    group_id: int = Column(BigInteger, ForeignKey("groups.id", ondelete="CASCADE"))
    group: Group = relationship("Group", foreign_keys=group_id, backref="menus_in_store")

    store_id: int = Column(BigInteger, ForeignKey("stores.id"), nullable=True, default=None)
    store: Store = relationship("Store", foreign_keys=store_id, backref="menus_in_store")

    need_save_as_active: bool = Column(Boolean, default=True, nullable=False)
    redirect_type: str = Column(Text(collation="utf8mb4_unicode_ci"), default="web")
    payment_option: str = Column(Text(collation="utf8mb4_unicode_ci"), default="disabled")
    invoice_template_id: int = Column(BigInteger, ForeignKey("invoice_templates.id", ondelete="RESTRICT"))
    invoice_template: InvoiceTemplate = relationship("InvoiceTemplate")

    is_e_menu: bool = Column(Boolean, default=False)

    create_date: datetime = Column(DateTime, default=datetime.utcnow)
    update_date: datetime = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    is_deleted: bool = Column(Boolean, default=False)

    @classmethod
    @db_func
    def create(
            cls,
            group_id: int,
            comment: str,
            store: Store | None = None,
    ) -> MenuInStore:
        menu_settings = cls(group_id=group_id, comment=comment, store=store)
        sess().add(menu_settings)
        sess().commit()
        return menu_settings

    @db_func
    def delete(self) -> bool:
        sess().delete(self)
        sess().commit()
        return True

    @classmethod
    @db_func
    def get(
            cls, id: int | None = None,
            group_id: int | None = None,
            store_id: int | None = None,
            comment: str | None = None,
            position: int = 0,
            limit: int | None = None,
            operation: Literal["all", "count"] = "all",
            search_text: str | None = None,
    ) -> MenuInStore | list[MenuInStore] | None:
        if not any([id, group_id, store_id]):
            return None

        query = sess().query(cls)
        query = query.filter(cls.is_deleted.is_(False))

        if group_id:
            query = query.filter(cls.group_id == group_id)
        if store_id:
            query = query.filter(cls.store_id == store_id)
        if comment:
            query = query.filter(cls.comment == comment)

        if search_text:
            query = query.filter(cls.comment.contains(search_text))

        if id:
            query = query.filter(cls.id == id)
            return query.one_or_none()

        if operation == "count":
            return query.with_entities(func.count(cls.id)).scalar()

        slice_args = [position, None]
        if limit:
            slice_args[1] = position + limit
        query = query.slice(*slice_args)

        return query.all()

    @db_func
    def _get_calculated_in_store_name(self):
        if self.store_id:
            store = models.Store.get_expression(id=self.store_id).one_or_none()
            name = store.name
        # settings variables brand and group
        elif brand := (group := self.group).brand:
            name = brand.name
        else:
            name = group.name

        return name

    async def get_calculated_in_store_name(self, store: Store | None = None, ) -> str:
        if store:
            return store.name

        return await self._get_calculated_in_store_name()

    def button_or_text(self):
        pass

    @property
    def invoice_payment_mode(self) -> InvoicePaymentModeEnum:
        match self.payment_option:
            case "amount_from_template":
                return InvoicePaymentModeEnum.TEMPLATE
            case "amount_from_user":
                return InvoicePaymentModeEnum.ENTERED_AMOUNT
            case "disabled":
                raise ValueError("payments are disabled")
            case _:
                raise ValueError("Unknown payments")
