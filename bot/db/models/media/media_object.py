import os

from sqlalchemy import Column, String, func
from sqlalchemy.dialects.mysql import INTEGER
from sqlalchemy.ext.hybrid import hybrid_property

from config import P4S_API_URL
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin


class MediaObject(Base, BaseDBModel, TimeCreatedMixin):
    media_type: str = Column(String(11), nullable=False)
    mime_type: str = Column(String(100), nullable=False, default="")

    hash_sum: str = Column(
        String(64, collation="utf8_bin"), nullable=False, unique=True
    )
    file_path: str = Column(String(256), nullable=False, unique=True)
    original_file_name: str | None = Column(String(1024), nullable=True)

    file_size: int = Column(INTEGER(unsigned=True), nullable=False)

    @hybrid_property
    def url(self):
        return f"{P4S_API_URL}/{self.file_path}"

    @url.expression
    def url(self):
        return func.CONCAT(f"{P4S_API_URL}/", self.file_path)

    @property
    def file_name(self):
        return os.path.basename(self.file_path)

    @property
    def extension(self):
        return self.file_path.split(".", 1)[1]
