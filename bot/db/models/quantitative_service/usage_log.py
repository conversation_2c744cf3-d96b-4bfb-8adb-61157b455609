from sqlalchemy import <PERSON>um<PERSON>, BigInteger, Foreign<PERSON>ey, Enum
from sqlalchemy.dialects.mysql import INTEGER
from sqlalchemy.orm import relationship

from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from .service import QuantitativeService, QuantitativeServiceTypeEnum


class QuantitativeServiceUsageLog(Base, BaseDBModel, TimeCreatedMixin):
    service_id: int = Column(BigInteger, ForeignKey("quantitative_services.id", ondelete="RESTRICT"))
    service: QuantitativeService = relationship(QuantitativeService, backref="usages_log")

    service_type: QuantitativeServiceTypeEnum = Column(Enum(QuantitativeServiceTypeEnum), nullable=False)
    quantity: int | None = Column(INTEGER(unsigned=True), nullable=True)  # None means unlimited

    used_before: int = Column(INTEGER(unsigned=True), nullable=False)
    used: int = Column(INTEGER(unsigned=True), nullable=False)
    used_after: int = Column(INTEGER(unsigned=True), nullable=False)
