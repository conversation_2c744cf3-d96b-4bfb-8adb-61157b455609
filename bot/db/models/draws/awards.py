import logging
import config as cfg
from typing import Dict

from sqlalchemy import Column, Integer, String, BigInteger, Text
from sqlalchemy import <PERSON><PERSON><PERSON>
from sqlalchemy.orm import relationship, backref

from db.connection import Base
from db.helpers import sess
from db.decorators import db_func

from utils.media import delete_file


class Award(Base):

    __tablename__ = "awards"

    id = Column(BigInteger, primary_key=True, autoincrement=True)

    award_category_id = Column(BigInteger, ForeignKey("award_categories.id", ondelete="CASCADE"), nullable=False)
    award_category = relationship(
        "AwardCategory",
        uselist=False,
        backref=backref("award", cascade="all,delete"),
        passive_deletes=True
    )

    content_type = Column(String(15))
    text = Column(Text(collation="utf8mb4_unicode_ci"), default=None)
    media_path = Column(String(512), default=None, nullable=True)

    SUPPORTED_MESSAGE_TYPES = cfg.MEDIA_WITH_CAPTION + ["text"]

    def __init__(
            self,
            award_category: "AwardCategory",
            content_type: str,
            text: str = None,
            media_path: str = None,
    ):
        assert award_category
        assert content_type
        assert text or media_path

        self.award_category = award_category
        self.content_type = content_type
        self.text = text
        self.media_path = media_path

    @classmethod
    @db_func
    def create(
            cls,
            award_category: "AwardCategory",
            content_type: str, text: str = None,
            media_path: str = None,
            **kwargs
    ):
        if not isinstance(award_category, AwardCategory):
            raise ValueError(f"award_category must be typeof AwardCategory, not {type(award_category)}")

        if not text and not media_path:
            raise ValueError(f"one of text and media_path must be specified. Now: {text = }, {media_path = }")

        award = cls(award_category, content_type, text, media_path)
        sess().add(award)
        sess().commit()
        return award

    @staticmethod
    @db_func
    def get(award_id: int) -> "Award":
        query = sess().query(Award)
        query = query.filter(Award.id == award_id)
        query = query.one_or_none()

    @property
    def kwargs_for_send(self) -> Dict[str, str]:
        content_type = self.content_type
        kwargs = dict(content_type=content_type)
        kwargs.update(text=self.text)
        if content_type != "text":
            kwargs[content_type] = self.media_path
        return kwargs

    @db_func
    def delete(self) -> bool:
        if self.media_path:
            delete_file(self.media_path)

        sess().delete(self)
        sess().commit()
        return True


class AwardCategory(Base):

    __tablename__ = "award_categories"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(Text(collation="utf8mb4_unicode_ci"))

    count_winners = Column(Integer, default=1)

    draw_id = Column(BigInteger, ForeignKey("draws.id", ondelete="CASCADE"))
    draw = relationship("Draw", back_populates="award_categories")

    @staticmethod
    @db_func
    def create(
            draw_id: int,
            name: str,
            count_winners: int = 1,
    ) -> "AwardCategory":
        award_category = AwardCategory(
            draw_id=draw_id,
            name=name,
            count_winners=count_winners
        )

        sess().add(award_category)
        sess().commit()
        return award_category

    @db_func
    def _update(self, **kwargs) -> bool:
        for field_name, value in kwargs.items():
            if field_name not in dir(self):
                continue
            setattr(self, field_name, value)
        sess().commit()
        return True

    async def update(self, **kwargs) -> bool:
        award = kwargs.pop("award", None)

        if award:
            logger = logging.getLogger("warning")
            logger.warning("Award specified in AwardCategory.update", exc_info=True)

        result = await self._update(**kwargs)
        if not result:
            return result

        return True

    async def add_award(self, content_type: str, **award_data: Dict[str, str]) -> Award:
        award = await Award.create(self, content_type, **award_data)
        return award

    @staticmethod
    @db_func
    def get(award_category_id: int):
        query = sess().query(AwardCategory)
        query = query.filter(AwardCategory.id == award_category_id)
        return query.one_or_none()

    @db_func
    def _delete_award(self):
        query = sess().query(Award)
        query = query.filter(Award.award_category_id == self.id)
        query.delete(synchronize_session="fetch")
        sess().commit()

    @db_func
    def _delete(self):
        sess().delete(self)
        sess().commit()

    async def delete(self):
        await self._delete_award()
        await self._delete()
        return True
