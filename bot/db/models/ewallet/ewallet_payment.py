import uuid
from datetime import datetime, timezone

from sqlalchemy import (
    BigInteger, Column, DateTime, Enum, ForeignKey, String,
    UniqueConstraint,
)
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from schemas import EWalletPaymentStatus


class EWalletPayment(Base, BaseDBModel, TimeCreatedMixin):

    __tablename__ = 'ewallet_payments'

    __table_args__ = (
        UniqueConstraint('uuid_id', name='uq_uuid_id'),
    )

    uuid_id = Column(
        String(36),
        default=lambda: str(uuid.uuid4()),
        nullable=False,
        unique=True
    )

    ewallet_id: int = Column(
        BigInteger, ForeignKey("ewallets.id", ondelete="RESTRICT"), nullable=False
    )
    ewallet: "models.EWallet" = relationship("EWallet", foreign_keys=ewallet_id)

    profile_id: int = Column(
        BigInteger, ForeignKey("groups.id", ondelete="RESTRICT"), nullable=False
    )
    profile: "models.Group" = relationship("Group", foreign_keys=profile_id)

    external_id: str = Column(String(99), nullable=False)

    description: str = Column(String(255), nullable=True, default=None)

    status: EWalletPaymentStatus = Column(
        Enum(EWalletPaymentStatus), nullable=False,
        default=EWalletPaymentStatus.CREATED,
    )
    amount = Column(BigInteger(), nullable=False, default=0)
    currency = Column(String(3), nullable=False, default="USD")

    success_url = Column(String(255), nullable=True)
    redirect_url: str = Column(String(255), nullable=True, default=None)

    creator_id = Column(BigInteger, ForeignKey("users.id", ondelete="RESTRICT"))
    creator: "models.User" = relationship("User", foreign_keys=creator_id)

    bot_id = Column(BigInteger, ForeignKey("bots.id", ondelete="RESTRICT"))
    bot: "models.ClientBot" = relationship("ClientBot", foreign_keys=bot_id)

    update_date = Column(
        DateTime, default=datetime.now(timezone.utc),
        onupdate=datetime.now(timezone.utc)
    )

    paid_datetime = Column(
        DateTime, default=None, nullable=True,
    )

    payment_settings_id = Column(
        BigInteger, ForeignKey('payment_settings.id'), nullable=True, default=None
    )

    paid_user_id = Column(
        BigInteger, ForeignKey("users.id", ondelete="RESTRICT"), nullable=True,
        default=None
    )
    paid_user: "models.User" = relationship("User", foreign_keys=paid_user_id)

    error: str = Column(String(255), nullable=True, default=None)
