import logging

from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.exc import MultipleResultsFound

import schemas
from config import ALL_PAYMENT_METHODS, USE_LOCALISATION
from db import db_func, sess
from db.crud.translation.update import (
    clear_object_updated_fields_translations_sync,
    update_object_translations_sync,
)
from db.models import (
    Brand, Group, InvoiceTemplate, InvoiceTemplateItem,
    ObjectPaymentSettings, PaymentSettings,
)
from schemas import AdminInvoicePaymentInvoiceTemplate
from schemas.payment_settings.schemas_add import ObjectPaymentSettingsTarget

logger = logging.getLogger('debugger')


@db_func
def update_invoice_template(
        invoice_template_id: int,
        data: schemas.AdminUpdateInvoiceTemplateSchema,
        group_id: int
) -> InvoiceTemplate:
    client_web_pages_ids = data.client_web_pages or []

    invoice_template = sess().query(InvoiceTemplate).filter_by(
        id=invoice_template_id
    ).first()

    if invoice_template:
        update_data = data.dict(exclude_unset=True, exclude={'client_web_pages'})

        if client_web_pages_ids:
            invoice_template.client_web_pages.clear()

            client_web_pages_objects = sess().query(InvoiceTemplate).filter(
                InvoiceTemplate.id.in_(client_web_pages_ids),
                InvoiceTemplate.group_id == group_id
            ).all()

            invoice_template.client_web_pages.extend(client_web_pages_objects)

        for field, value in update_data.items():
            if field == "comment_label_raw" and (value in [None, ""]):
                value = USE_LOCALISATION

            if field in ("min_amount", "max_amount") and value:
                value = round(value * 100)

            setattr(invoice_template, field, value)

        sess().commit()

    return invoice_template


@db_func
def update_invoice_template_item(
        invoice_template_item_id: int,
        data: schemas.AdminUpdateInvoiceTemplateItemSchema
) -> InvoiceTemplateItem:
    invoice_template_item = sess().query(InvoiceTemplateItem).filter_by(
        id=invoice_template_item_id
    ).first()

    if invoice_template_item:
        for field, value in data.dict(exclude_unset=True).items():
            setattr(invoice_template_item, field, value)

        sess().commit()

    return invoice_template_item


@db_func
def update_object_payment_settings_for_object(
        profile_id: int,
        invoice_template_id: int | None = None,
        store_id: int | None = None,
        payment_data: list[
                          AdminInvoicePaymentInvoiceTemplate |
                          schemas.CreateAdminStorePaymentData] | None = None
):
    if not payment_data:
        payment_data = []

    # Отримуємо групу для мов профілю
    group = Group.get_sync(profile_id)
    profile_langs = group.get_langs_list(with_main_lang=True)

    payments_settings = sess().execute(
        select(PaymentSettings).join(
            Group, Group.id == profile_id
        ).join(
            Brand, Brand.group_id == Group.id
        ).where(
            PaymentSettings.brand_id == Brand.id,
            PaymentSettings.payment_method.in_(ALL_PAYMENT_METHODS),
            Group.status == "enabled"
        )
    ).scalars().all()

    payment_datas = {pd.payment_settings_id: pd for pd in payment_data or []}

    for ps in payments_settings:
        object_payment_data = payment_datas.get(ps.id)
        object_payment_settings = None
        if invoice_template_id:
            try:
                object_payment_settings = ObjectPaymentSettings.get_sync(
                    payment_settings_id=ps.id, invoice_template_id=invoice_template_id,
                    is_deleted=False,
                )
            except MultipleResultsFound as err:
                logger.error(f"MultipleResultsFound: payment_settings_id={ps.id}, invoice_template_id"
                             f"={invoice_template_id}, is_deleted=False")
                raise err
        elif store_id:
            object_payment_settings = ObjectPaymentSettings.get_sync(
                payment_settings_id=ps.id, store_id=store_id,
                is_deleted=False,
            )

        json_data = None
        if (object_payment_data and object_payment_data.json_data
                and object_payment_data.json_data.data):
            json_data = object_payment_data.json_data.data

        if json_data:
            if isinstance(json_data, BaseModel):
                json_data = json_data.dict()

        if not object_payment_settings:
            object_payment_settings = ObjectPaymentSettings(
                invoice_template_id=invoice_template_id if invoice_template_id else
                None,
                store_id=store_id if store_id else None,
                payment_settings_id=ps.id,
                json_data=json_data,
                is_enabled=object_payment_data.is_enabled if
                object_payment_data and
                object_payment_data.is_enabled is not None else False,
                target=ObjectPaymentSettingsTarget.INVOICE_TEMPLATE if
                invoice_template_id else ObjectPaymentSettingsTarget.STORE,
                post_payment_info=object_payment_data.post_payment_info if
                object_payment_data and
                object_payment_data.post_payment_info is not None else None,
            )

            sess().add(object_payment_settings)
            sess().flush()  # Потрібно для отримання ID перед оновленням перекладів
            
            # Оновлюємо переклади для нового об'єкта
            if object_payment_data and getattr(object_payment_data, 'translations', None):
                update_object_translations_sync(object_payment_settings, object_payment_data.translations)
        else:
            if object_payment_data:
                # Зберігаємо дані для перевірки перекладів
                data_dict = {
                    "post_payment_info": object_payment_data.post_payment_info,
                }
                
                # Оновлюємо основні дані
                object_payment_settings.update_sync(
                    json_data=json_data,
                    is_enabled=object_payment_data.is_enabled,
                    post_payment_info=object_payment_data.post_payment_info,
                    no_commit=True
                )
                
                # Очищуємо переклади для оновлених полів
                clear_object_updated_fields_translations_sync(
                    object_payment_settings, data_dict, profile_langs
                )
                
                # Оновлюємо переклади, якщо вони передані
                if getattr(object_payment_data, 'translations', None):
                    update_object_translations_sync(object_payment_settings, object_payment_data.translations)

    sess().commit()



@db_func
def update_object_payment_settings_with_translations(
        object_payment_settings_id: int,
        is_enabled: bool,
        json_data: dict | None,
        post_payment_info: str | None,
        translations: dict | None,
        profile_id: int,
):
    """Оновлює ObjectPaymentSettings з перекладами"""
    object_payment_settings = sess().query(ObjectPaymentSettings).filter_by(
        id=object_payment_settings_id
    ).one_or_none()
    
    if not object_payment_settings:
        return None
    
    # Отримуємо групу для мов профілю
    group = sess().query(Group).filter_by(id=profile_id).one_or_none()
    profile_langs = group.get_langs_list(with_main_lang=True) if group else []
    
    # Зберігаємо дані для перевірки перекладів
    data_dict = {
        "post_payment_info": post_payment_info,
    }
    
    # Оновлюємо основні дані
    object_payment_settings.update_sync(
        is_enabled=is_enabled,
        json_data=json_data,
        post_payment_info=post_payment_info,
        no_commit=True
    )
    
    # Очищуємо переклади для оновлених полів
    clear_object_updated_fields_translations_sync(
        object_payment_settings, data_dict, profile_langs
    )
    
    # Оновлюємо переклади, якщо вони передані
    if translations:
        update_object_translations_sync(object_payment_settings, translations)
    
    sess().commit()
    
    return object_payment_settings
