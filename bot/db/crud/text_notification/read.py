from datetime import datetime

from sqlalchemy import case, func, literal_column, select

import schemas
from config import INBOX_MAX_AGE
from db import db_func, sess
from db.crud.helpers import (
    filter_by_phone, get_inbox_status_sort_stmt,
    user_field_if_not_anonymous,
)
from db.crud.helpers.crm import (
    crm_filter_by_inbox_cursor, crm_params_filter,
    crm_scope_filter, get_crm_list, get_inbox_type_sort_stmt,
)
from db.models import ClientBot, Group, Scope, TextNotification, User
from db.types.operation import Operation
from schemas import TextNotificationTargetEnum


@db_func
def get_text_notification(
        text_notification_id: int,
        action: str | None = None,
        user_id: int | None = None
):
    stmt = select(TextNotification)
    stmt = stmt.where(TextNotification.id == text_notification_id)

    if any((action, user_id)) and not all((action, user_id)):
        raise ValueError("Specify both action and user_id or none of them")

    if action and user_id:
        stmt = stmt.join(TextNotification.profile)
        stmt = stmt.where(Group.status == "enabled")
        stmt = stmt.where(
            Scope.filter_for_action(
                action, "user", user_id,
                {
                    "profile_id": Group.id,
                    "text_notification_id": text_notification_id,
                }
            )
        )

    stmt = stmt.group_by(TextNotification.id)

    return sess().scalar(stmt)


def get_crm_text_notification_list_statement(
        user_id: int,
        params: schemas.CRMTextNotificationListParams | None = None,
        operation: Operation = "list",
        cursor: schemas.IDCursor | None = None,
        inbox_cursor: schemas.InboxCursor | None = None,
        for_inbox: bool = False,
        is_platform_admin: bool | None = None,
):
    inbox_status = case(
        [
            (TextNotification.is_read.is_(False),
             literal_column(f"'{schemas.InboxStatus.NEW.value}'")),
            (TextNotification.is_read.is_(True),
             literal_column(f"'{schemas.InboxStatus.RECENT.value}'"))
        ]
    )

    inbox_status_sort = get_inbox_status_sort_stmt(inbox_status)

    if operation == "count":
        stmt = select(
            func.count(TextNotification.id)
        )
    else:
        stmt = select(
            literal_column(f"'{schemas.InboxType.TEXT_NOTIFICATION.value}'").label(
                "inbox_type"
            ),
            inbox_status.label("inbox_status"),
            inbox_status_sort,
            get_inbox_type_sort_stmt(schemas.InboxType.TEXT_NOTIFICATION),
            TextNotification.change_date.label("change_date"),
            literal_column("NULL").label("desired_delivery_date"),
            TextNotification.id,
            literal_column("NULL").label("is_pending"),
            func.lower(TextNotification.type).label("type"),
            literal_column("NULL").label("privacy"),
            TextNotification.text,
            literal_column("NULL").label("additional_text"),
            literal_column("NULL").label("media"),
            TextNotification.crm_tag.label("crm_tag"),
            literal_column("NULL").label("invoice_type"),
            literal_column("NULL").label("status"),
            literal_column("NULL").label("status_pay"),
            literal_column("NULL").label("current_status"),
            literal_column("NULL").label("shipment_name"),
            literal_column("NULL").label("shipment_type"),
            user_field_if_not_anonymous(User.id, "first_name", User.first_name),
            user_field_if_not_anonymous(User.id, "last_name", User.last_name),
            user_field_if_not_anonymous(User.id, "full_name", User.name),
            user_field_if_not_anonymous(User.id, "email", User.email),
            user_field_if_not_anonymous(User.id, "phone", User.wa_phone),
            user_field_if_not_anonymous(User.id, "photo_url", User.photo_url),
            literal_column("NULL").label("currency"),
            user_field_if_not_anonymous(User.id),
            literal_column("0").label("store_id"),
            literal_column("NULL").label("store_name"),
            literal_column("NULL").label("ticket_title"),
            Group.id.label("profile_id"),
            Group.name.label("profile_name"),
            Group.name.label("business_name"),
            ClientBot.display_name.label("bot_name"),
            literal_column("0").label("before_loyalty_sum"),
            literal_column("0").label("discount"),
            literal_column("0").label("bonuses_redeemed"),
            literal_column("0").label("discount_and_bonuses_redeemed"),
            literal_column("0").label("total_sum"),
            literal_column("0").label("tips_sum"),
            literal_column("0").label("sum_to_pay"),
            literal_column("0").label("payer_fee"),
            literal_column("0").label("paid_sum"),
            literal_column("0").label("menu_in_store_id"),
            literal_column("NULL").label("menu_in_store_comment"),
            literal_column("NULL").label("title"),
            literal_column("NULL").label("mark"),
            TextNotification.text.label("items_text"),
            literal_column("NULL").label("last_message_text"),
            literal_column("NULL").label("last_message_content_type"),
            literal_column("NULL").label("last_message_media_url"),
            literal_column("NULL").label("last_message_media_mime_type"),
            literal_column("NULL").label("last_message_content"),
            TextNotification.is_read.label("is_read"),
            TextNotification.read_by_user_id,
            literal_column("NULL").label("comment"),
            TextNotification.time_created,
        )

    if operation != "count" or (params and params.search_text):
        stmt = stmt.outerjoin(User, TextNotification.from_user_id == User.id)

    stmt = stmt.join(Group, TextNotification.profile_id == Group.id)
    stmt = stmt.outerjoin(ClientBot, TextNotification.from_bot_id == ClientBot.id)
    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(TextNotification.target == TextNotificationTargetEnum.CRM)

    stmt = crm_filter_by_inbox_cursor(
        stmt, schemas.InboxType.TEXT_NOTIFICATION, inbox_cursor,
        TextNotification.change_date, TextNotification.id, inbox_status_sort
    )

    if for_inbox:
        stmt = stmt.where(
            TextNotification.change_date >= datetime.utcnow() - INBOX_MAX_AGE
        )

    extra_search_conditions = []
    if params:
        if params.search_text:
            extra_search_conditions.extend(
                [
                    User.full_name.contains(params.search_text.strip()),
                    User.email.contains(params.search_text.strip()),
                    filter_by_phone(User.wa_phone, params.search_text)
                ]
            )
        if params.statuses and len(params.statuses) == 1:
            if params.statuses[0] == schemas.CRMTextNotificationReadStatusEnum.READ:
                stmt = stmt.where(TextNotification.is_read.is_(True))
            else:
                stmt = stmt.where(TextNotification.is_read.is_(False))

    stmt = crm_scope_filter(
        stmt, "text_notification", user_id,
        TextNotification.id, is_platform_admin=is_platform_admin,
    )

    stmt = crm_params_filter(
        stmt, TextNotification, params, cursor, operation, extra_search_conditions
    )

    return stmt


@db_func
def get_crm_text_notification_list(
        user_id: int,
        params: schemas.CRMTextNotificationListParams | None = None,
        operation: Operation = "list",
        cursor: schemas.IDCursor | None = None,
):
    stmt = get_crm_text_notification_list_statement(user_id, params, operation, cursor)
    return get_crm_list(stmt, TextNotification, operation, params, cursor)
