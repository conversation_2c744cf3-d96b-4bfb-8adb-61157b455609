from db import db_func, sess
from db.crud.group.admins.create import add_admin_sync
from db.crud.group.create import create_group_sync
from db.crud.group.update import change_group_owner_sync
from db.models import Channel, ChatMember, ClientBot, User


@db_func
def add_channel(
        user: User,
        bot: ClientBot,
        chat_type: str,
        title: str,
        chat_id: int,
        user_status: str,
        username: str = None,
) -> Channel:
    channel = Channel.get_sync(chat_id=chat_id, bot_id=bot.id)

    # если канал ещё никогда не был добавлен - создаём
    if channel is None:
        channel = Channel(
            chat_id, chat_type,
            title, bot,
            username.replace("@", "") if username else None,
        )
    if user not in channel.admins:
        channel.admins.append(user)

    if not channel.group:
        channel.group = create_group_sync(title, user)
    else:
        channel.group.status = "enabled"

        if user_status == "creator" and channel.group.owner_id != user.id:
            change_group_owner_sync(channel.group, user)

    if user_status != "creator":
        add_admin_sync(user, channel.group)

    if not ChatMember.get_sync(user_id=user.id, channel_id=channel.id, for_update=True):
        sess().add(ChatMember(user, channel, None))

    sess().commit()
    return channel
