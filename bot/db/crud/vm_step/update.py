import enum
from typing import Any

from sqlalchemy import select, update

import exceptions
import schemas
from db import db_func, sess
from db.crud.translation.update import (
    clear_object_updated_fields_translations_sync,
    update_object_translations_sync,
)
from db.models import MediaObject, VirtualManagerInteractive, VirtualManagerStep


def process_interactive_params(
        el: Any, steps_by_id: dict[int | str, VirtualManagerStep]
):
    if isinstance(el, dict):
        return {
            key: process_interactive_params(
                value,
                steps_by_id
            )
            for key, value in el.items()
            # excluding id for button_actions
            if key != "id"
        }
    elif isinstance(el, list):
        return [process_interactive_params(item, steps_by_id) for item in el]
    elif isinstance(el, enum.Enum):
        return el.value
    else:
        return el


@db_func
def set_virtual_manager_steps(
        vm_id: int,
        steps: list[schemas.AdminModifyVirtualManagerStepData],
        medias_by_url: dict[int | str, MediaObject],
        profile_langs: list[str],
):
    all_ids = [step.id for step in steps]
    if len(all_ids) != len(set(all_ids)):
        raise exceptions.AdminVMStepIdsNotUniqueError(
            set(el for el in all_ids if all_ids.count(el) > 1)
        )

    # string ids are temporary linking ids between interactives and steps
    existing_steps_ids = [step.id for step in steps if isinstance(step.id, int)]
    existing_steps: dict[int, VirtualManagerStep] = {
        el.id: el
        for el in
        sess().scalars(
            select(VirtualManagerStep)
            .where(
                VirtualManagerStep.id.in_(existing_steps_ids),
                VirtualManagerStep.virtual_manager_id == vm_id,
            )
        ).all()
    }
    if len(existing_steps_ids) != len(existing_steps):
        not_found_ids = [el for el in existing_steps_ids if el not in existing_steps]
        raise exceptions.AdminVMStepsNotFoundError(not_found_ids)

    existing_interactives_ids: list[int] = sum(
        [
            [interactive.id for interactive in step_data.interactives if
             isinstance(interactive.id, int)]
            for step_data in steps
            if step_data.interactives
        ],
        [],
    )
    existing_interactives: dict[int, VirtualManagerInteractive] = {
        el.id: el
        for el in
        sess().scalars(
            select(VirtualManagerInteractive)
            .where(
                VirtualManagerInteractive.id.in_(existing_interactives_ids),
                VirtualManagerInteractive.step_id.in_(existing_steps_ids)
            )
        ).all()
    }
    if len(existing_interactives_ids) != len(existing_interactives):
        not_found_ids = [el for el in existing_interactives_ids if
                         el not in existing_interactives]
        raise exceptions.AdminVMStepsInteractivesNoFoundError(not_found_ids)

    medias_ids = [step.media_id for step in steps if step.media_id]
    existing_medias: dict[int, MediaObject] = {
        media.id: media
        for media in sess().scalars(
            select(MediaObject)
            .where(MediaObject.id.in_(medias_ids))
        ).all()
    }

    sess().execute(
        update(VirtualManagerStep).values({"is_deleted": True})
        .where(
            VirtualManagerStep.id.not_in(existing_steps_ids),
            VirtualManagerStep.virtual_manager_id == vm_id,
        ).execution_options(synchronize_session="fetch")
    )
    sess().execute(
        update(VirtualManagerInteractive).values({"is_deleted": True})
        .where(
            VirtualManagerInteractive.id.not_in(existing_interactives_ids),
            VirtualManagerInteractive.step_id.in_(existing_steps_ids),
        ).execution_options(synchronize_session="fetch")
    )

    db_steps = []

    for i, step_data in enumerate(steps):
        data = step_data.dict(
            exclude_unset=True,
            exclude={"interactives", "translations", "id", "media_type"}
        )

        if "media_id" in data or "media_url" in data:
            data["media"] = (
                existing_medias[media_id]
                if (media_id := data.get("media_id")) else
                medias_by_url[media_url]
                if (media_url := data.get("media_url"))
                else None
            )
            data.pop("media_id", None)
            data.pop("media_url", None)

        data["position"] = i

        if isinstance(step_data.id, int):
            step = existing_steps[step_data.id]
            clear_object_updated_fields_translations_sync(step, data, profile_langs)
            step.update_sync(data, no_commit=True)
        else:
            data.pop("id", None)
            step = VirtualManagerStep(**data, virtual_manager_id=vm_id)
            sess().add(step)
            sess().flush()

        db_steps.append(step)

        if step_data.translations:
            update_object_translations_sync(step, step_data.translations)

    steps_by_id = {
        step_data.id: db_steps[i]
        for i, step_data in enumerate(steps)
    }
    for i, step_data in enumerate(steps):
        if not step_data.interactives:
            continue

        step = db_steps[i]

        for j, interactive_data in enumerate(step_data.interactives):
            params = process_interactive_params(
                interactive_data.params.dict() if interactive_data.params else None,
                steps_by_id
            )

            if isinstance(interactive_data.id, int):
                interactive = existing_interactives[interactive_data.id]

                clear_object_updated_fields_translations_sync(
                    interactive, interactive_data, profile_langs
                )
                interactive.params = params
                interactive.position = j
            else:
                interactive = VirtualManagerInteractive(
                    type=schemas.VMInteractiveType(interactive_data.type),
                    subtype=interactive_data.subtype,
                    position=j,
                    step=step,
                    params=params,
                )
                sess().add(interactive)
                sess().flush()

            if interactive_data.translations:
                update_object_translations_sync(
                    interactive, interactive_data.translations
                )

    sess().commit()
    return True
