from sqlalchemy import select

from db import db_func, sess
from ...scope.read import check_access_to_action_sync
from db.models import Admin, Group, Scope, User


def add_admin_sync(user: User, group: Group, no_commit: bool = False, scope: str = "profile:edit"):
    if not (admin := sess().scalar(
            select(Admin).where(
                Admin.user_id == user.id,
                Admin.group_id == group.id
            ).with_for_update()
    )
    ):
        admin = Admin(
            user=user,
            group=group,
        )
        sess().add(admin)

    if not check_access_to_action_sync(
            scope,
            "user", user.id,
            available_data={
                "profile_id": group.id,
            }
    ):
        sess().add(
            Scope(
                target="user",
                scope=scope,
                user=user,
                profile=group,
            )
        )

    if not no_commit:
        sess().commit()
    return admin


@db_func
def add_admin(user: User, group: Group, no_commit: bool = False):
    return add_admin_sync(user, group, no_commit)
