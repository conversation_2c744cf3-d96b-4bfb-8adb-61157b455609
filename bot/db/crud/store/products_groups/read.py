from sqlalchemy import and_, or_, select
from sqlalchemy.engine import Row
from sqlalchemy.sql import Select
from sqlalchemy.sql.expression import func

from db import db_func, sess
from db.models import (
    Brand, Group, Scope, StoreCharacteristic, StoreCharacteristicValue, StoreProduct,
    StoreProductGroup, StoreProductGroupCharacteristic,
)


@db_func
def get_products_groups_list(
        profile_id: int,
        user_id: int,
        offset: int | None = None,
        limit: int | None = None,
        search_text: str | None = None,
        is_count: bool = False,
) -> list[Row] | int:

    available_data = {
        "product_group_id": StoreProductGroup.id,
        "profile_id": profile_id,
    }

    if is_count:
        stmt: Select = select(
            func.count(StoreProductGroup.id)
        )
        stmt = stmt.where(
            Scope.filter_for_action(
                "product_group:read",
                "user", user_id,
                available_data,
            )
        )
    else:
        read_allowed, edit_allowed = Scope.allowed_scopes_list(
            "read", "edit",
            object_name="product_group",
            target="user",
            target_id=user_id,
            available_data=available_data
        )
        stmt: Select = select(
            StoreProductGroup.id,
            StoreProductGroup.name,
            StoreProductGroup.external_id,
            StoreProductGroup.external_type,
            read_allowed, edit_allowed
        )
        stmt = stmt.where(read_allowed.is_(True))

    stmt = stmt.join(StoreProductGroup.brand)
    stmt = stmt.join(Brand.group)

    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(Group.id == profile_id)

    stmt = stmt.where(StoreProductGroup.is_deleted.is_(False))

    stmt = stmt.distinct()

    if search_text:
        stmt = stmt.where(
            or_(
                StoreProductGroup.name.ilike(f"%{search_text}%"),
                StoreProductGroup.external_id.ilike(f"%{search_text}%")
            )
        )

    if is_count:
        return sess().scalar(stmt)

    if offset:
        stmt = stmt.offset(offset)
    if limit:
        stmt = stmt.limit(limit)

    return sess().execute(stmt).fetchall()


@db_func
def get_product_group_by_id(user_id: int, profile_id: int, group_id: int) -> Row:
    stmt = select(
        StoreProductGroup.id,
        StoreProductGroup.name,
        StoreProductGroup.external_id,
        StoreProductGroup.external_type,
        *Scope.allowed_scopes_list(
            "read", "edit",
            object_name="product_group",
            target="user",
            target_id=user_id,
            available_data={
                'product_group_id': StoreProductGroup.id,
                'profile_id': profile_id,
            }
        )
    )

    stmt = stmt.join(StoreProductGroup.brand)
    stmt = stmt.join(Brand.group)

    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(Group.id == profile_id)

    stmt = stmt.where(StoreProductGroup.is_deleted.is_(False))

    stmt = stmt.where(
        StoreProductGroup.id == group_id,
    )

    return sess().execute(stmt).first()


@db_func
def get_products_for_group_by_id(
        user_id: int,
        profile_id: int,
        group_id: int,
) -> list[Row]:
    stmt = select(
        StoreProduct.id,
        StoreProduct.name,
        StoreProduct.product_id,
        StoreProduct.internal_name,
        StoreProduct.position,
        func.json_arrayagg(
            func.json_object(
                'id', StoreCharacteristicValue.id,
                'value', StoreCharacteristicValue.value,
                'modifier_id', StoreCharacteristicValue.characteristic_id,
                'modifier_name', StoreCharacteristic.name
            )
        ).label('modifier_values'),
        *Scope.allowed_scopes_list(
            "read", "edit",
            object_name="product",
            target="user",
            target_id=user_id,
            available_data={
                'product_id': StoreProduct.id,
                'profile_id': profile_id,
            }
        )
    )
    stmt = stmt.where(StoreProduct.is_deleted.is_(False))

    stmt = stmt.outerjoin(
        StoreProductGroupCharacteristic,
        and_(
            StoreProductGroupCharacteristic.product_group_id ==
            StoreProduct.product_group_id
        )
    )

    stmt = stmt.outerjoin(
        StoreCharacteristicValue,
        and_(
            StoreCharacteristicValue.characteristic_id ==
            StoreProductGroupCharacteristic.characteristic_id,
            StoreCharacteristicValue.product_id ==
            StoreProduct.id
        )
    )

    stmt = stmt.outerjoin(
        StoreCharacteristic,
        StoreCharacteristicValue.characteristic_id == StoreCharacteristic.id
    ).filter(
        StoreProduct.product_group_id == group_id,
    ).group_by(StoreProduct.id)

    stmt = stmt.order_by(StoreProduct.position.is_(None))
    stmt = stmt.order_by(StoreProduct.position)

    return sess().execute(stmt).all()


@db_func
def get_product_group_modifiers(user_id: int, profile_id: int, group_id: int) -> list[
    Row]:
    available_data = {
        "characteristic_id": StoreCharacteristic.id,
        'profile_id': profile_id,
    }

    stmt = select(
        StoreCharacteristic.id,
        StoreCharacteristic.name,
        StoreCharacteristic.is_hide,
        StoreProductGroupCharacteristic.show_one_modification,
        *Scope.allowed_scopes_list(
            "read", "edit",
            object_name="characteristic",
            target="user",
            target_id=user_id,
            available_data=available_data
        )
    )
    stmt = stmt.join(StoreCharacteristic.brand)
    stmt = stmt.join(Brand.group)
    stmt = stmt.join(
        StoreProductGroupCharacteristic,
        StoreCharacteristic.id == StoreProductGroupCharacteristic.characteristic_id
    )
    stmt = stmt.where(Group.status == 'enabled')
    stmt = stmt.where(Group.id == profile_id)

    stmt = stmt.filter(
        StoreProductGroupCharacteristic.product_group_id == group_id,
    )

    return sess().execute(stmt).all()


@db_func
def get_product_group_modifier(
        user_id: int, profile_id: int, group_id: int, characteristic_id: int
) -> Row:
    available_data = {
        "characteristic_id": StoreCharacteristic.id,
        'profile_id': profile_id,
    }

    stmt = select(
        StoreCharacteristic.id,
        StoreCharacteristic.name,
        StoreCharacteristic.is_hide,
        StoreProductGroupCharacteristic.show_one_modification,
        *Scope.allowed_scopes_list(
            "read", "edit",
            object_name="characteristic",
            target="user",
            target_id=user_id,
            available_data=available_data
        )
    )
    stmt = stmt.join(StoreCharacteristic.brand)
    stmt = stmt.join(Brand.group)
    stmt = stmt.join(
        StoreProductGroupCharacteristic,
        StoreCharacteristic.id == StoreProductGroupCharacteristic.characteristic_id
    )
    stmt = stmt.where(Group.status == 'enabled')
    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.filter(
        StoreProductGroupCharacteristic.product_group_id == group_id,
        StoreCharacteristic.id == characteristic_id
    )

    return sess().execute(stmt).first()


@db_func
def get_products_without_products_group(
        profile_id: int,
        user_id: int,
        offset: int,
        limit: int,
        search_text: str
):
    available_data = {
        "profile_id": profile_id,
        "product_id": StoreProduct.id,
    }

    select_fields = [
        StoreProduct.id,
        StoreProduct.name,
        StoreProduct.internal_name,
        StoreProduct.media_id,
    ]

    select_fields.extend(
        Scope.allowed_scopes_list(
            "read", "edit",
            object_name="product",
            target="user",
            target_id=user_id,
            available_data=available_data,
        )
    )

    stmt = sess().query(*select_fields)

    stmt = stmt.filter(
        or_(
            StoreProduct.product_group_id.is_(None),
            and_(
                StoreProductGroup.id.isnot(None),
                StoreProductGroup.is_deleted.is_(True)
            )
        )
    )

    stmt = stmt.join(StoreProduct.brand)
    stmt = stmt.join(Brand.group)
    stmt = stmt.outerjoin(
        StoreProductGroup, StoreProductGroup.id == StoreProduct.product_group_id
    )

    stmt = stmt.where(Group.status == 'enabled')
    stmt = stmt.where(Group.id == profile_id)

    stmt = stmt.where(StoreProduct.is_deleted.is_(False))

    stmt = stmt.order_by(StoreProduct.position.is_(None))
    stmt = stmt.order_by(StoreProduct.position)

    stmt = stmt.order_by(StoreProduct.id)

    if search_text:
        stmt = stmt.filter(StoreProduct.name.ilike(f"%{search_text}%"))

    if offset:
        stmt = stmt.offset(offset)
    if limit:
        stmt = stmt.limit(limit)

    return stmt.all()
