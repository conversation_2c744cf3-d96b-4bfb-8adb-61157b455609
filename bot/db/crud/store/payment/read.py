from sqlalchemy import desc, select
from sqlalchemy.orm import joinedload

from core.payment.exceptions import (
    PaymentInvoiceNotFoundError,
    PaymentNotFoundPaymentDataByUuidError,
)
from db import db_func, sess
from db.models import Brand, Group, Invoice, Payment, StoreOrder


@db_func
def get_data_by_payment_uuid(payment_uuid: str) -> tuple[
    int, int | None, int | None, int]:

    res = sess().query(Brand.id, StoreOrder.store_id, StoreOrder.id, Invoice.id).join(
        Payment, Invoice.id == Payment.invoice_id
    ).join(
        Group, Group.id == Invoice.group_id
    ).join(Brand, Brand.group_id == Group.id).outerjoin(
        StoreOrder, StoreOrder.invoice_id == Invoice.id
    ).filter(Payment.uuid_id == payment_uuid).one_or_none()

    if not res:
        raise PaymentNotFoundPaymentDataByUuidError(f"{payment_uuid=}")

    return res


@db_func
def get_payment_data_(payment_uuid: str) -> tuple[Payment, Invoice, StoreOrder, Brand]:
    query = (
        sess().query(Payment, Invoice, StoreOrder, Brand)
        .outerjoin(Invoice, Invoice.id == Payment.invoice_id)
        .join(Group, Group.id == Invoice.group_id)
        .join(Brand, Brand.group_id == Group.id)
        .outerjoin(StoreOrder, StoreOrder.invoice_id == Invoice.id)
        .filter(Payment.uuid_id == payment_uuid)
        .options(joinedload(Payment.invoice))
        .options(joinedload(Invoice.store_order))
        .options(joinedload(Invoice.group).joinedload(Group.brand))
    )

    result = query.one_or_none()
    if not result:
        raise PaymentNotFoundPaymentDataByUuidError(f"{payment_uuid=}")

    payment, invoice, store_order, brand = result

    if not invoice:
        raise PaymentInvoiceNotFoundError(f"{payment_uuid=}")

    return payment, invoice, store_order, brand


@db_func
def get_payment_data_by_order_or_invoice(
        order_id: int | None = None,
        invoice_id: int | None = None,
) -> tuple[Payment, Invoice, StoreOrder, Brand]:
    store_order = None
    invoice = None
    print(f"*** ORDER ID {order_id}")

    if order_id:
        store_order = sess().query(StoreOrder).filter(
            StoreOrder.id == order_id
        ).one_or_none()
        print(f"*** STORE ORDER {store_order}")

    if invoice_id:
        invoice = sess().query(Invoice).filter(Invoice.id == invoice_id).one_or_none()

    if not invoice and store_order:
        invoice = sess().query(Invoice).filter(
            Invoice.id == store_order.invoice_id
        ).one_or_none()
        print(f"*** INVOICE {invoice}")

    payment, brand = None, None
    if invoice:
        payment = (
            sess()
            .query(Payment)
            .filter(Payment.invoice_id == invoice.id)
            .order_by(desc(Payment.create_date))
            .first()
        )
        brand = sess().query(Brand).filter(Brand.group_id == invoice.group_id).one_or_none()

    return payment, invoice, store_order, brand


@db_func
def get_payment_by_invoice_and_payment_settings(
        invoice_id: int,
        payment_settings_id: int,
        object_payment_settings_id: int | None = None,
) -> dict | None:
    stmt = select(Payment).where(
        Payment.invoice_id == invoice_id,
        Payment.payment_settings_id == payment_settings_id,
    )
    if object_payment_settings_id:
        stmt = stmt.where(
            Payment.object_payment_settings_id == object_payment_settings_id
        )

    stmt = stmt.order_by(Payment.create_date.desc()).limit(1)

    return sess().execute(stmt).scalar_one_or_none()
