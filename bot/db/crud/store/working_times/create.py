import logging

from db import db_func, sess
from db.models import Store, WorkingDay, WorkingSlot
from schemas import WorkingDaySchema


@db_func
def create_working_times(store_id: int, working_days: list[WorkingDaySchema]) -> list[WorkingDay] | None:
    query = sess().query(Store)
    query = query.filter(Store.id == store_id)

    try:
        store = query.one_or_none()
    except Exception as e:
        logging.error(e, exc_info=True)
        return None

    if not store:
        return None

    res = []
    for day in working_days:
        db_day = sess().query(WorkingDay) \
            .filter(WorkingDay.store_id == store_id) \
            .filter(WorkingDay.day == day.day) \
            .one_or_none()
        if db_day:
            continue

        db_day = WorkingDay(day=day.day, is_weekend=day.is_weekend, store=store)
        sess().add(db_day)

        if day.slots:
            for slot in day.slots:
                db_slot = WorkingSlot(start_time=slot.start_time, end_time=slot.end_time)
                db_day.slots.append(db_slot)

                sess().add(db_slot)
        res.append(db_day)

    sess().commit()

    return res
