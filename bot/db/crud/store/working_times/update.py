from db import db_func, sess
from db.models import WorkingDay, WorkingSlot
from schemas import WorkingSlotSchema


@db_func
def update_working_times(
        store_id: int,
        working_day_id: int,
        slots: list[WorkingSlotSchema] | None = None,
        is_weekend: bool = False,
) -> WorkingDay:
    query = sess().query(WorkingDay)
    query = query.filter(WorkingDay.id == working_day_id)
    query = query.filter(WorkingDay.store_id == store_id)
    db_day = query.one_or_none()

    if db_day:
        if slots:
            for slot in slots:
                db_slot = WorkingSlot(
                    start_time=slot.start_time,
                    end_time=slot.end_time,
                )
                db_day.slots.append(db_slot)
                sess().add(db_slot)

        if db_day.is_weekend != is_weekend:
            db_day.is_weekend = is_weekend

        sess().commit()

    return db_day
