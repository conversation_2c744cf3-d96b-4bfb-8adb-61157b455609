from typing import Literal

from sqlalchemy import desc

from db import db_func, sess
from db.helpers import get_query_by_operation, order_by_slice_and_result
from db.models import (
    BrandCustomSettings, ShipmentPrice, ShipmentPriceToSettings, ShipmentTime,
    ShipmentTimeToSettings, ShipmentZone, StoreCustomSettings,
)


def get_shipment_time_to_settings(
        settings_id: int | None = None, zone_id: int | None = None
) -> ShipmentTimeToSettings:
    if all([settings_id, zone_id]):
        raise ValueError("Only one parameter must be specified: settings_id or zone_id")
    if not any([settings_id, zone_id]):
        raise ValueError("Must specify one of the parameters: settings_id or zone_id")

    query = sess().query(ShipmentTimeToSettings)

    if settings_id:
        query = query.filter(ShipmentTimeToSettings.settings_id == settings_id)
    else:
        query = query.filter(ShipmentTimeToSettings.zone_id == zone_id)

    return query.one_or_none()


@db_func
def get_shipment_time(
        settings_id: int | None = None, zone_id: int | None = None
) -> ShipmentTime:
    shipment_time_to_settings = get_shipment_time_to_settings(settings_id, zone_id)

    if shipment_time_to_settings:
        return shipment_time_to_settings.shipment_time
    return None


@db_func
def get_shipment_zones(
        brand_id: int, store_id: int | None = None,
        shipment_store_settings_id: int | None = None,
        search_text: str | None = None,
        position: int | None = None,
        limit: int | None = None,
        operation: Literal["all", "count"] = "all",
) -> list[ShipmentZone] | int:
    query = get_query_by_operation(ShipmentZone, operation).distinct()

    if shipment_store_settings_id:
        query = query.filter(ShipmentZone.shipment_id == shipment_store_settings_id)
    else:
        query = query.join(
            StoreCustomSettings, StoreCustomSettings.id == ShipmentZone.shipment_id
        )
        if store_id:
            query = query.filter(StoreCustomSettings.store_id == store_id)

        query = query.join(
            BrandCustomSettings,
            BrandCustomSettings.id == StoreCustomSettings.custom_settings_id
        )
        query = query.filter(BrandCustomSettings.brand_id == brand_id)

    if search_text:
        query = query.filter(ShipmentZone.name.contains(search_text))

    return order_by_slice_and_result(
        query, position, limit,
        (desc(ShipmentZone.is_distance), ShipmentZone.distance, ShipmentZone.id),
        operation
    )


def get_shipment_prices_to_settings(
        brand_id: int, store_id: int | None = None,
        settings_id: int | None = None,
        zone_id: int | None = None, zone_ids: list[int] | None = None,
        position: int | None = None, limit: int | None = None,
        operation: Literal["all", "count"] = "all",
) -> list[ShipmentPriceToSettings] | int:
    if all([settings_id, zone_id, zone_ids]):
        raise ValueError(
            "Only one parameter must be specified: settings_id or zone_id or zone_ids"
        )
    if not any([settings_id, zone_id, zone_ids]):
        raise ValueError("Must specify one of the parameters: settings_id or zone_id")

    query = get_query_by_operation(ShipmentPriceToSettings, operation).distinct()

    if settings_id:
        query = query.filter(ShipmentPriceToSettings.settings_id == settings_id)
    elif zone_id:
        query = query.filter(ShipmentPriceToSettings.zone_id == zone_id)
    elif zone_ids:
        query = query.filter(ShipmentPriceToSettings.zone_id.in_(zone_ids))
    else:
        query = query.join(
            StoreCustomSettings,
            StoreCustomSettings.id == ShipmentPriceToSettings.settings_id
        )
        if store_id:
            query = query.filter(StoreCustomSettings.store_id == store_id)

        query = query.join(
            BrandCustomSettings,
            BrandCustomSettings.id == StoreCustomSettings.custom_settings_id
        )
        query = query.filter(BrandCustomSettings.brand_id == brand_id)

    return order_by_slice_and_result(
        query, position, limit, (ShipmentPriceToSettings.id,), operation
    )


@db_func
def get_shipment_prices(
        brand_id: int, store_id: int | None = None,
        settings_id: int | None = None,
        zone_id: int | None = None, zone_ids: list[int] | None = None,
        position: int | None = None,
        limit: int | None = None,
        operation: Literal["all", "count"] = "all",
) -> list[ShipmentPrice] | dict[int, list[ShipmentPrice]] | int:
    shipment_prices_to_settings = get_shipment_prices_to_settings(
        brand_id, store_id, settings_id, zone_id, zone_ids,
        position, limit, operation
    )

    if operation == "count":
        return shipment_prices_to_settings

    if zone_ids:
        return {
            zone_id: [item.shipment_price for item in shipment_prices_to_settings if
                      zone_id == item.zone_id]
            for zone_id in zone_ids
        }
    return [item.shipment_price for item in shipment_prices_to_settings]
