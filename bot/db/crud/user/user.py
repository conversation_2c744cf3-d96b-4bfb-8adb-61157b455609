from passlib.handlers.pbkdf2 import pbkdf2_sha256
from sqlalchemy import select
from sqlalchemy.orm import Session
from sqlalchemy.sql import Select

import schemas
from config import SERVICE_BOT, SERVICE_BOT_USERNAME
from core.user.types import CreateOrUpdateMessangerUserInfo
from db import db_func, sess
from db.crud.store.email_request import delete_confirm_email_request_sync
from db.decorators.other import safe_deadlock_handler
from db.models import (
    ClientBot, Friend, IncustCustomer, InvoiceToFriend, User, UserAnalyticAction,
    UserClientBotActivity, UserServiceBotActivity,
)
from schemas.auth import BaseUpdateUser
from utils.date_time import utcnow
from utils.helpers import get_running_file_name


@db_func
def check_is_email_exists(db: Session | None = None, email: str | None = None) -> bool:
    if not db:
        db = sess()
    query = db.query(User).filter(User.email == email)
    query = db.query(query.exists())
    return query.scalar()


@db_func
def check_is_tg_connected(db: Session, email: str) -> bool:
    query = db.query(User).filter(User.email == email)
    query = query.filter(User.chat_id.is_not(None))
    return query.scalar()


@db_func
def check_exists_chat_id(db: Session, chat_id: int) -> User | None:
    query = db.query(User).filter(User.chat_id == chat_id)
    return query.one()


@db_func
def create_user(
        email: str | None = None,
        password: str | None = None,
        first_name: str | None = None,
        last_name: str | None = None,
        full_name: str | None = None,
        birth_date: str | None = None,
        chat_id: int | None = None,
        username: str | None = None,
        photo: str | None = None,
        telegram_data: schemas.TelegramData | None = None,
        is_accepted_agreement: bool = False,
        is_guest_user: bool = False,
        is_system_user: bool = False,
        system_user_owned_by_profile_id: int | None = None,
        lang: str | None = None,
        is_confirmed_email: bool = False,
        is_auth_google: bool = False,
        is_auth_apple: bool = False,
) -> User:
    hashed_password = pbkdf2_sha256.hash(password) if password else None

    if telegram_data:
        if chat_id:
            raise ValueError("specify one of telegram_data, chat_id")

        chat_id = telegram_data.id

        if not first_name:
            first_name = telegram_data.first_name
        if not last_name:
            last_name = telegram_data.last_name
        if not username:
            username = telegram_data.username

    if not full_name:
        if first_name and last_name:
            full_name = " ".join([first_name, last_name])
        else:
            full_name = first_name or last_name

    user = User(
        lang=lang,  # type: ignore
        chat_id=chat_id,
        username=username,
        first_name=first_name,
        last_name=last_name,  # type: ignore
        full_name=full_name,
        birth_date=birth_date,
        photo=photo,
        email=email,
        hashed_password=hashed_password,
        is_accepted_agreement=is_accepted_agreement,
        accept_agreement_datetime=utcnow(),
        is_guest_user=is_guest_user,
        is_system_user=is_system_user,
        system_user_owned_by_profile_id=system_user_owned_by_profile_id,
        is_confirmed_email=is_confirmed_email,
        is_auth_google=is_auth_google,
        is_auth_apple=is_auth_apple,
    )

    sess().add(user)
    sess().commit()

    return user


@db_func
@safe_deadlock_handler
def create_or_update_messanger_user(
        new_lang: str,  # if creating user or user language is invalid
        available_langs_list: list[str],
        bot: ClientBot | None = None,
        chat_id: int | None = None,
        username: str | None = None,
        first_name: str | None = None,
        last_name: str | None = None,
        full_name: str | None = None,

        wa_phone: str | None = None,
        wa_name: str | None = None,

        need_set_is_accepted_agreement: bool = False,
        allowed_create_user: bool = True,
) -> CreateOrUpdateMessangerUserInfo:
    if all((chat_id, wa_phone)):
        raise ValueError("specify only one of chat_id, wa_phone")
    if all((username, wa_phone)):
        raise ValueError("specify only of username, wa_phone")
    if wa_name and not wa_phone:
        raise ValueError("wa_name can be specified only together with wa_phone")
    if not any((chat_id, wa_phone)):
        raise ValueError("specify at least one of chat_id, wa_phone")

    by_chat_id = User.get_sync(chat_id=chat_id, for_update=True) if chat_id else None
    by_username = User.get_sync(
        username=username, for_update=True
    ) if username else None
    by_wa_phone = User.get_sync(
        wa_phone=wa_phone, for_update=True
    ) if wa_phone else None

    data = {}
    if chat_id and bot and bot.bot_type == "telegram":
        data.update(
            chat_id=chat_id,
            username=username,
            first_name=first_name,
            last_name=last_name,
            full_name=full_name,
        )
    elif wa_phone and bot and bot.bot_type == "whatsapp":
        data.update(
            wa_phone=wa_phone,
            wa_name=wa_name,
        )

    if need_set_is_accepted_agreement:
        data["is_accepted_agreement"] = True

    try:
        file_name = get_running_file_name()
        # create or update user bot activity
        if bot:
            bot_username = bot.display_name
        elif file_name == SERVICE_BOT:
            bot_username = SERVICE_BOT_USERNAME
        else:
            bot_username = "unknown"

        # create or update user
        users = list(
            filter(lambda x: x is not None, (by_chat_id, by_username, by_wa_phone))
        )
        if not users:
            if not allowed_create_user:
                return CreateOrUpdateMessangerUserInfo()

            # noinspection PyArgumentList
            user = User(
                **data,
                lang=new_lang,
            )
            sess().add(user)

            user_joined_analytic_action = UserAnalyticAction(
                type="user_joined",
                user=user,
                bot=bot,
                bot_username=bot_username,
            )
            sess().add(user_joined_analytic_action)

            is_user_created = True
        else:
            if len(users) == 1:
                user = users[0]
            else:
                user = by_chat_id
                by_username.username = None

            user.update_sync(data, no_commit=True)
            if not user.lang or \
                    (not bot and (
                            not user.lang or
                            user.lang not in available_langs_list
                    )):
                user.lang = new_lang

            is_user_created = False

        # create or update user bot activity
        if bot:
            if not is_user_created:
                user_bot_activity = UserClientBotActivity.get_sync(
                    user_id=user.id,
                    bot_id=bot.id,
                    for_update=True,
                )
            else:
                user_bot_activity = None

            if not user_bot_activity:
                # noinspection PyArgumentList
                user_bot_activity = UserClientBotActivity(
                    user=user,
                    bot=bot,
                    lang=new_lang,
                )
                sess().add(user_bot_activity)

                user_joined_to_bot_analytic_action = UserAnalyticAction(
                    type="user_joined_to_bot",
                    user=user,
                    bot=bot,
                    bot_username=bot.display_name,
                )
                sess().add(user_joined_to_bot_analytic_action)

                is_bot_activity_created = True
            else:
                if (not user_bot_activity.lang or user_bot_activity.lang not in
                        available_langs_list):
                    user_bot_activity.lang = new_lang
                is_bot_activity_created = False
                if not user_bot_activity.is_active:
                    user_bot_activity.is_active = True

        elif bot_username == SERVICE_BOT_USERNAME:
            if not is_user_created:
                user_bot_activity = UserServiceBotActivity.get_sync(
                    user_id=user.id, for_update=True
                )
            else:
                user_bot_activity = None

            if not user_bot_activity:
                user_bot_activity = UserServiceBotActivity(
                    user=user
                )
                sess().add(user_bot_activity)
                is_bot_activity_created = True
            else:
                is_bot_activity_created = False
        else:
            user_bot_activity = None
            is_bot_activity_created = False

        # set last activity
        if user_bot_activity:
            user_bot_activity.last_activity = utcnow()

        if not user.db_timezone and bot and bot.timezone:
            user.db_timezone = bot.timezone

        sess().commit()
    except Exception as e:
        sess().rollback()
        raise e

    if isinstance(user_bot_activity, UserClientBotActivity):
        lang = user_bot_activity.lang
    elif user:
        lang = user.lang
    else:
        lang = new_lang

    return CreateOrUpdateMessangerUserInfo(
        user,
        lang,
        is_user_created,
        user_bot_activity,
        is_bot_activity_created
    )


@db_func
def get_user(
        db: Session | None = None,
        user_id: int = None,
        email: str = None,
        chat_id: int = None
) -> User:
    if not any((user_id, email, chat_id)):
        raise ValueError("one of user_id, email, chat_id must be specified")

    query = db.query(User) if db else sess().query(User)

    if user_id:
        query = query.filter(User.id == user_id)

    elif email:
        query = query.filter(User.email == email)

    elif chat_id:
        query = query.filter(User.chat_id == chat_id)

    return query.one_or_none()


@db_func
def get_system_user(id: int, profile_id: int | None = None) -> User | None:
    stmt: Select = select(User)
    stmt = stmt.where(User.id == id)
    if profile_id:
        stmt = stmt.where(User.system_user_owned_by_profile_id == profile_id)
    return sess().scalar(stmt)


@db_func
def update_user(
        db: Session, user: User,
        new_data: BaseUpdateUser,
        bot_id: int | None = None,
        need_lang_updated_info: bool = False,
) -> tuple[User, bool] | User:
    data = new_data.dict(exclude_unset=True)

    password = data.pop("password", None)

    if password:
        data["hashed_password"] = pbkdf2_sha256.hash(password)

    is_lang_updated = False
    for field, value in data.items():
        if not hasattr(user, field):
            continue

        if field == "lang":
            if bot_id:
                user_bot_activity = UserClientBotActivity.get_sync(
                    user_id=user.id, bot_id=bot_id
                )
                if user_bot_activity and user_bot_activity.lang != value:
                    user_bot_activity.lang = value
                    is_lang_updated = True
                    continue

            if user.lang != value:
                user.lang = value
                is_lang_updated = True
                continue

        if getattr(user, field) != value:
            setattr(user, field, value)

    if user.first_name and user.last_name:
        user.full_name = " ".join([user.first_name, user.last_name])
    else:
        if user.first_name or user.last_name:
            user.full_name = user.first_name or user.last_name

    db.commit()

    if not need_lang_updated_info:
        return user

    return user, is_lang_updated


@db_func
def update_user_email_or_password(
        db: Session,
        user: User,
        purpose: schemas.ConfirmEmailPurposeLiteral,
        email: str | None = None,
        password: str | None = None,
) -> User:
    delete_confirm_email_request_sync(db, email, purpose)

    if email:
        user.email = email

    if password:
        user.hashed_password = pbkdf2_sha256.hash(password)

    sess().commit()

    return user


@db_func
def delete_incust_customers(brand_id: int) -> bool:
    sess().query(IncustCustomer).filter(IncustCustomer.brand_id == brand_id).delete()

    sess().commit()
    return True


@db_func
def get_incust_customers(brand_id: int) -> list[IncustCustomer]:
    query = sess().query(IncustCustomer).distinct()
    query = query.filter(IncustCustomer.brand_id == brand_id)
    return query.all()


@db_func
def get_user_friends(
        user_id: int,
        bot_type: str | None = None,
) -> list[schemas.FriendData]:

    query1 = sess().query(Friend, User).join(User, User.id == Friend.friend_id).filter(
        Friend.user_id == user_id
    )

    query2 = sess().query(Friend, User).join(User, User.id == Friend.user_id).filter(
        Friend.friend_id == user_id
    )

    result = query1.union(query2).order_by(Friend.datetime_used.desc()).all()

    return [schemas.FriendData(
        id=friend.id,
        user_id=friend.friend_id if friend.user_id == user_id else friend.user_id,
        name=user.name,
        email=user.email,
        photo=user.photo,
        wa_phone=user.wa_phone,
        username=user.username,
        sending_possible=(
            True
            if (
                    user.email or
                    (
                            bot_type and
                            (
                                    (bot_type == 'telegram' and user.chat_id) or
                                    (bot_type == 'whatsapp' and user.wa_phone)
                            )
                    )
            )
            else False
        ),
    ) for friend, user in result]


@db_func
def check_invoice_by_friend(invoice_id: int, friend_id: int, need_write: bool = False):
    permissions = ["pending", "payed", "canceled"]
    if need_write:
        permissions = ["pending"]

    return sess().query(InvoiceToFriend).filter(
        InvoiceToFriend.invoice_id == invoice_id,
        InvoiceToFriend.friend_id == friend_id,
        InvoiceToFriend.status.in_(permissions)
    ).all()


__all__ = [
    "check_is_email_exists",
    "check_exists_chat_id",
    "check_is_tg_connected",
    "create_user",
    "create_or_update_messanger_user",
    "get_user",
    "update_user",
    "delete_incust_customers",
    "get_incust_customers",
    "update_user_email_or_password",
    "get_user_friends",
    "check_invoice_by_friend",
    "get_system_user",
]
