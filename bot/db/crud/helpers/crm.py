from typing import Any, Iterable, Literal, Protocol

import time
from sqlalchemy import Column, and_, case, literal_column, or_, select
from sqlalchemy.sql import Select

import schemas
from db import sess
from db.crud.helpers import filter_by_phone
from db.helpers import statement_to_str
from db.models import Group, Scope, Store
from db.types.operation import Operation
from loggers import JSONLogger
from schemas import (
    ChangeDateCursor, Cursor, CursorDirection,
    DesiredDeliveryDateCursor, IDCursor,
)
from ..scope.read import check_access_to_action_sync

STATUS_SORTS = {
    schemas.InboxStatus.NEW: 0,
    schemas.InboxStatus.IN_PROGRESS: 1,
    schemas.InboxStatus.DELIVERING: 2,
    schemas.InboxStatus.RECENT: 3,
}

TYPE_SORTS = {
    schemas.InboxType.EWALLET_EXT_PAYMENT: 0,
    schemas.InboxType.ORDER: 1,
    schemas.InboxType.INVOICE: 2,
    schemas.InboxType.TICKET: 3,
    schemas.InboxType.REVIEW: 4,
    schemas.InboxType.CHAT: 5,
    schemas.InboxType.TEXT_NOTIFICATION: 6,
}


def get_inbox_status_sort_stmt(inbox_status):
    return case(
        [
            (status.value == inbox_status, sort)
            for status, sort in STATUS_SORTS.items()
        ],
    ).label("inbox_status_sort")


def get_inbox_status_sort_value(inbox_status: schemas.InboxStatus):
    return STATUS_SORTS[inbox_status]


def get_inbox_type_sort_stmt(inbox_type: schemas.InboxType):
    return literal_column(f"'{TYPE_SORTS[inbox_type]}'").label("inbox_type_sort")


def get_inbox_type_sort_value(inbox_type: schemas.InboxType):
    return TYPE_SORTS[inbox_type]


class ModelProtocol(Protocol):
    id: Column[int] | int


class ParamsProtocol(Protocol):
    profile_ids: list[int] | None
    statuses: list[str] | None
    search_text: str | None
    cursor: IDCursor | None
    offset: int | None
    limit: int | None


class ParamsWithStoreIdsProtocol(ParamsProtocol):
    store_ids: list[int] | None


def crm_params_filter(
        stmt: Select, model: ModelProtocol,
        params: ParamsProtocol | ParamsWithStoreIdsProtocol | None,
        cursor: IDCursor | ChangeDateCursor | DesiredDeliveryDateCursor | None,
        operation: str,
        extra_search_conditions: Iterable | None = None,
        filter_by_statuses: bool = True,
):
    if params:
        if params.profile_ids:
            stmt = stmt.where(Group.id.in_(params.profile_ids))
        if hasattr(params, "store_ids") and params.store_ids:
            stmt = stmt.where(Store.id.in_(params.store_ids))
        if filter_by_statuses and params.statuses and hasattr(model, "status"):
            stmt = stmt.where(model.status.in_(params.statuses))
        if params.search_text:
            search_text = params.search_text.strip()
            search_conditions = [
                model.id == search_text
            ]
            if hasattr(model, "full_name"):
                search_conditions.append(
                    model.full_name.contains(search_text)
                )
            if hasattr(model, "email"):
                search_conditions.append(
                    model.email.contains(search_text)
                )
            if hasattr(model, "phone"):
                search_conditions.append(
                    filter_by_phone(model.phone, search_text)
                )
            if extra_search_conditions:
                search_conditions.extend(extra_search_conditions)
            stmt = stmt.where(or_(*search_conditions))

        if not cursor:
            cursor = params.cursor

    if cursor:
        if isinstance(cursor, IDCursor):
            if cursor.direction == CursorDirection.BACK:
                stmt = stmt.where(model.id > cursor.id)
            else:
                stmt = stmt.where(model.id < cursor.id)
        elif (
                isinstance(cursor, ChangeDateCursor) and
                hasattr(model, "change_date")
        ):
            if cursor.direction == CursorDirection.BACK:
                stmt = stmt.where(
                    or_(
                        model.change_date > cursor.change_date,
                        and_(
                            model.change_date == cursor.change_date,
                            model.id > cursor.id
                        )
                    )
                )
            else:
                stmt = stmt.where(
                    or_(
                        model.change_date < cursor.change_date,
                        and_(
                            model.change_date == cursor.change_date,
                            model.id < cursor.id
                        )
                    )
                )
        elif (
                isinstance(cursor, DesiredDeliveryDateCursor) and
                hasattr(model, "desired_delivery_date")
        ):
            if cursor.direction == CursorDirection.BACK:
                if cursor.desired_delivery_date:
                    stmt = stmt.where(
                        or_(
                            model.desired_delivery_date < cursor.desired_delivery_date,
                            and_(
                                model.desired_delivery_date ==
                                cursor.desired_delivery_date,
                                model.id < cursor.id
                            )
                        )
                    )
                else:
                    stmt = stmt.where(
                        or_(
                            model.desired_delivery_date.is_(None),
                            and_(
                                model.desired_delivery_date.is_not(None),
                                model.id < cursor.id
                            )
                        )
                    )
            else:
                if cursor.desired_delivery_date:
                    stmt = stmt.where(
                        or_(
                            model.desired_delivery_date > cursor.desired_delivery_date,
                            and_(
                                model.desired_delivery_date ==
                                cursor.desired_delivery_date,
                                model.id > cursor.id
                            )
                        )
                    )
                else:
                    stmt = stmt.where(
                        or_(
                            model.desired_delivery_date.is_not(None),
                            and_(
                                model.desired_delivery_date.is_(None),
                                model.id > cursor.id
                            )
                        )
                    )
        else:
            raise ValueError("Invalid cursor or model")

    if operation != "count" and params:
        if params.offset:
            stmt = stmt.offset(params.offset)
        if params.limit:
            stmt = stmt.limit(params.limit)

    return stmt


def crm_scope_filter(
        stmt: Select,
        obj_name: str,
        user_id: int,
        obj_id_expr: Any,
        profile_id_expr: Any = None,
        is_platform_admin: bool | None = None,
):
    if is_platform_admin is None:
        is_platform_admin = check_access_to_action_sync(
            "platform:admin", "user", user_id,
        )

    # no access filters if platform admin
    if is_platform_admin:
        return stmt

    if profile_id_expr is None:
        profile_id_expr = Group.id

    stmt = stmt.where(
        Scope.filter_for_action(
            f"crm_{obj_name}:read",
            "user", user_id,
            {"profile_id": profile_id_expr, f"{obj_name}_id": obj_id_expr},
            exclude_scopes=("platform:superadmin", "platform:admin"),
        )
    )

    return stmt


def crm_filter_by_inbox_cursor(
        stmt: Select,
        inbox_type: schemas.InboxType,
        inbox_cursor: schemas.InboxCursor | None,
        change_date_stmt: Any,
        id_stmt: Any,
        inbox_status_sort_stmt: Any,
):
    if not inbox_cursor:
        return stmt

    if inbox_cursor.type == inbox_type:
        object_condition = or_(
            change_date_stmt < inbox_cursor.change_date,
            and_(
                change_date_stmt == inbox_cursor.change_date,
                id_stmt < inbox_cursor.id,
            )
        )
    else:
        object_condition = change_date_stmt <= inbox_cursor.change_date

    return stmt.where(
        or_(
            inbox_status_sort_stmt > get_inbox_status_sort_value(inbox_cursor.status),
            and_(
                inbox_status_sort_stmt == get_inbox_status_sort_value(
                    inbox_cursor.status
                ),
                object_condition
            )
        ),
    )


def get_crm_list(
        stmt: Select,
        model: ModelProtocol,
        operation: Operation,
        params: ParamsProtocol | None,
        cursor: Cursor | None,
        order_by_fields: str | Iterable[str] = "id",
        order_by_direction: Literal["asc", "desc"] = "desc",
        debug: bool = False,
):
    if debug:
        logger = JSONLogger(f"crm.get_crm_list")
    else:
        logger = None

    start = time.time()

    try:
        if operation == "count":
            return sess().scalar(stmt)
        if operation == "exists":
            return sess().scalar(select(stmt.exists()))

        if not cursor and params:
            cursor = params.cursor

        is_reversed = cursor and cursor.direction == CursorDirection.BACK

        if isinstance(order_by_fields, str):
            order_by_fields = [order_by_fields]

        for order_by_field in order_by_fields:
            order_by_stmt = getattr(model, order_by_field)
            if (
                    (order_by_direction == "asc" and not is_reversed) or
                    (order_by_direction == "desc" and is_reversed)
            ):
                stmt = stmt.order_by(order_by_stmt)
            else:
                stmt = stmt.order_by(order_by_stmt.desc())

        result = sess().execute(stmt).fetchall()
        if is_reversed:
            result.reverse()
        return result
    finally:
        end = time.time()
        if debug:
            logger.debug(
                f"get_crm_list for {model.__name__} {operation}", {
                    "execution_time": end - start,
                    "statement": statement_to_str(stmt)
                }
            )
