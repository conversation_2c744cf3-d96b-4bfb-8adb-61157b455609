from datetime import datetime
from typing import Iterable

from sqlalchemy import case, func, literal_column, select

import schemas
from config import INBOX_MAX_AGE
from db import db_func, sess
from db.crud.helpers import (
    filter_by_phone, get_inbox_status_sort_stmt,
    user_field_if_not_anonymous,
)
from db.crud.helpers.crm import (
    crm_filter_by_inbox_cursor, crm_params_filter, crm_scope_filter, get_crm_list,
    get_inbox_type_sort_stmt,
)
from db.models import (
    CRMTicket, CRMTicketStatus, ClientBot, Group, Scope, User,
    VirtualManagerChat,
)
from db.types.operation import Operation
from schemas import CRMTicketStatusEnum, IDCursor


@db_func
def get_crm_ticket(
        ticket_id: int,
        action: str | None = None,
        user_id: int | None = None
):
    stmt = select(CRMTicket)
    stmt = stmt.where(CRMTicket.id == ticket_id)

    if any((action, user_id)) and not all((action, user_id)):
        raise ValueError("Specify both action and user_id or none of them")

    if action and user_id:
        stmt = stmt.join(CRMTicket.group)
        stmt = stmt.where(Group.status == "enabled")
        stmt = stmt.where(
            Scope.filter_for_action(
                action, "user", user_id,
                {
                    "profile_id": Group.id,
                    "ticket_id": ticket_id,
                }
            )
        )

    stmt = stmt.group_by(CRMTicket.id)

    return sess().scalar(stmt)


@db_func
def get_crm_ticket_for_user(
        user_id: int,
        bot: ClientBot | None = None,
        ticket_title: str | None = None,
        vmc_id: int | None = None
) -> CRMTicket | None:
    vmc = VirtualManagerChat.get_sync(vmc_id)
    group_id = vmc.group_id if vmc else bot.group_id if bot else None
    if not group_id:
        raise ValueError("Either vmc_id or bot has to be specified")

    ticket: CRMTicket | None = None

    if vmc:
        if not ticket_title:
            ticket_title = vmc.virtual_manager.name

        if vmc.ticket_id:
            vmc_ticket = vmc.ticket
            if vmc_ticket.title == ticket_title:
                ticket = vmc_ticket

    if not ticket:
        stmt = select(CRMTicket).where(
            CRMTicket.user_id == user_id,
            CRMTicket.group_id == group_id
        )
        if ticket_title:
            stmt = stmt.where(CRMTicket.title == ticket_title)
        if bot:
            stmt = stmt.where(CRMTicket.bot_id == bot.id)
        stmt = stmt.order_by(CRMTicket.time_created.desc())
        stmt = stmt.limit(1)
        ticket = sess().scalar(stmt)

    return ticket


@db_func
def get_crm_ticket_status_history(ticket_id: int):
    stmt = select(
        CRMTicketStatus.id,
        CRMTicketStatus.status,
        CRMTicketStatus.initiated_by,
        CRMTicketStatus.initiated_by_user_id,
        CRMTicketStatus.time_created.label("set_datetime"),
        User.name.label("initiated_by_user_name"),
        User.email.label("initiated_by_email"),
    )
    stmt = stmt.outerjoin(CRMTicketStatus.initiated_by_user)
    stmt = stmt.where(CRMTicketStatus.ticket_id == ticket_id)
    stmt = stmt.order_by(CRMTicketStatus.time_created)
    stmt = stmt.order_by(CRMTicketStatus.id)

    data = sess().execute(stmt).fetchall()
    return [schemas.CRMTicketStatusHistoryObject.from_orm(el) for el in data]


def get_crm_ticket_list_statement(
        user_id: int,
        params: schemas.CRMTicketListParams | None = None,
        operation: Operation = "list",
        cursor: schemas.IDCursor | None = None,
        inbox_cursor: schemas.InboxCursor | None = None,
        for_inbox: bool = False,
        is_platform_admin: bool | None = None,
):
    inbox_status = case(
        [
            (
                CRMTicket.status == CRMTicketStatusEnum.OPEN_UNCONFIRMED,
                literal_column(f"'{schemas.InboxStatus.NEW.value}'"),
            ),
            (
                CRMTicket.status == CRMTicketStatusEnum.OPEN_CONFIRMED,
                literal_column(f"'{schemas.InboxStatus.IN_PROGRESS.value}'"),
            ),
        ],
        else_=literal_column(f"'{schemas.InboxStatus.RECENT.value}'"),
    )

    inbox_status_sort = get_inbox_status_sort_stmt(inbox_status)

    if operation == "count":
        stmt = select(
            func.count(CRMTicket.id)
        )
    else:
        stmt = select(
            literal_column(f"'{schemas.InboxType.TICKET.value}'").label("inbox_type"),
            inbox_status.label("inbox_status"),
            inbox_status_sort,
            get_inbox_type_sort_stmt(schemas.InboxType.TICKET),
            CRMTicket.change_date,
            literal_column("NULL").label("desired_delivery_date"),
            CRMTicket.id,
            literal_column("NULL").label("is_pending"),
            literal_column("'ticket'").label("type"),
            literal_column("NULL").label("privacy"),
            literal_column("NULL").label("text"),
            literal_column("NULL").label("additional_text"),
            literal_column("NULL").label("media"),
            CRMTicket.crm_tag.label("crm_tag"),
            literal_column("NULL").label("invoice_type"),
            func.lower(CRMTicket.status).label("status"),
            literal_column("NULL").label("status_pay"),
            func.lower(CRMTicket.status).label("current_status"),
            literal_column("NULL").label("shipment_name"),
            literal_column("NULL").label("shipment_type"),
            user_field_if_not_anonymous(User.id, "first_name", User.first_name),
            user_field_if_not_anonymous(User.id, "last_name", User.last_name),
            user_field_if_not_anonymous(User.id, "full_name", User.name),
            user_field_if_not_anonymous(User.id, "email", User.email),
            user_field_if_not_anonymous(User.id, "phone", User.wa_phone),
            user_field_if_not_anonymous(User.id, "photo_url", User.photo_url),
            literal_column("NULL").label("currency"),
            user_field_if_not_anonymous(User.id),
            literal_column("0").label("store_id"),
            literal_column("NULL").label("store_name"),
            CRMTicket.title.label("ticket_title"),
            Group.id.label("profile_id"),
            Group.name.label("profile_name"),
            Group.name.label("business_name"),
            ClientBot.display_name.label("bot_name"),
            literal_column("0").label("before_loyalty_sum"),
            literal_column("0").label("discount"),
            literal_column("0").label("bonuses_redeemed"),
            literal_column("0").label("discount_and_bonuses_redeemed"),
            literal_column("0").label("total_sum"),
            literal_column("0").label("tips_sum"),
            literal_column("0").label("sum_to_pay"),
            literal_column("0").label("payer_fee"),
            literal_column("0").label("paid_sum"),
            literal_column("0").label("menu_in_store_id"),
            literal_column("NULL").label("menu_in_store_comment"),
            CRMTicket.title,
            literal_column("NULL").label("mark"),
            CRMTicket.title.label("items_text"),
            literal_column("NULL").label("last_message_text"),
            literal_column("NULL").label("last_message_content_type"),
            literal_column("NULL").label("last_message_media_url"),
            literal_column("NULL").label("last_message_media_mime_type"),
            literal_column("NULL").label("last_message_content"),
            literal_column("NULL").label("is_read"),
            literal_column("NULL").label("read_by_user_id"),
            CRMTicket.internal_comment.label("comment"),
            CRMTicket.time_created,
        )

    if operation != "count" or (params and params.search_text):
        stmt = stmt.join(User, CRMTicket.user_id == User.id)

    stmt = stmt.join(Group, CRMTicket.group_id == Group.id)
    stmt = stmt.outerjoin(ClientBot, CRMTicket.bot_id == ClientBot.id)
    stmt = stmt.where(Group.status == "enabled")

    stmt = crm_filter_by_inbox_cursor(
        stmt, schemas.InboxType.TICKET, inbox_cursor,
        CRMTicket.change_date, CRMTicket.id, inbox_status_sort
    )

    if for_inbox:
        stmt = stmt.where(CRMTicket.change_date >= datetime.utcnow() - INBOX_MAX_AGE)

    extra_search_conditions = []
    if params:
        if params.search_text:
            extra_search_conditions.extend(
                [
                    User.full_name.contains(params.search_text.strip()),
                    User.email.contains(params.search_text.strip()),
                    filter_by_phone(User.wa_phone, params.search_text)
                ]
            )
        if params.ticket_titles:
            stmt = stmt.where(CRMTicket.title.in_(params.ticket_titles))

    stmt = crm_scope_filter(
        stmt, "ticket", user_id,
        CRMTicket.id, is_platform_admin=is_platform_admin,
    )

    stmt = crm_params_filter(
        stmt, CRMTicket, params, cursor, operation, extra_search_conditions
    )
    return stmt


@db_func
def get_crm_ticket_list(
        user_id: int,
        params: schemas.CRMTicketListParams | None = None,
        operation: Operation = "list",
        cursor: IDCursor | None = None,
):
    stmt = get_crm_ticket_list_statement(user_id, params, operation, cursor)
    return get_crm_list(stmt, CRMTicket, operation, params, cursor)


@db_func
def get_last_crm_ticket_for_user(
        title: str,
        user_id: int,
        group_id: int,
        bot_id: int | None = None,
        statuses: Iterable[CRMTicketStatusEnum] | None = None
) -> CRMTicket | None:
    stmt = select(CRMTicket)
    stmt = stmt.where(CRMTicket.title == title)
    stmt = stmt.where(CRMTicket.user_id == user_id)
    stmt = stmt.where(CRMTicket.group_id == group_id)
    if bot_id:
        stmt = stmt.where(CRMTicket.bot_id == bot_id)
    else:
        stmt = stmt.where(CRMTicket.bot_id.is_(None))
    if statuses:
        stmt = stmt.where(
            CRMTicket.status.in_(statuses)
        )
    stmt = stmt.order_by(CRMTicket.time_created.desc())
    stmt = stmt.limit(1)
    return sess().scalar(stmt)
