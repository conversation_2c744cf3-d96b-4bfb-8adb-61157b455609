import re

from sqlalchemy import exists, select

from db import sess
from db.models import VirtualManager


def process_vm_name_id(name_id: str, ignore_vm_id: int | None = None):
    if not name_id.isascii():
        return None

    name_id = re.sub(r"[\s-]", "_", name_id)

    if name_id.isdecimal():
        name_id = f"VM{name_id}_"

    i = 0

    while True:
        test_name_id = f"{name_id}{i}" if i else name_id

        stmt = exists().where(VirtualManager.name_id == test_name_id)
        if ignore_vm_id is not None:
            stmt = stmt.where(VirtualManager.id != ignore_vm_id)
        stmt = stmt.where(VirtualManager.is_deleted.is_(False))

        if not sess().scalar(select(stmt)):
            return test_name_id

        i += 1
