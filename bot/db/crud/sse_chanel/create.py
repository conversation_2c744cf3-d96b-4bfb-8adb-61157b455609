from sqlalchemy import delete

from db import db_func, sess
from db.decorators.other import safe_deadlock_handler
from db.models import SSEChannel
from schemas import (
    SSEChannelTarget,
)


@safe_deadlock_handler
@db_func
def create_sse_channel(
        session_id: str, target: SSEChannelTarget,
        profile_id: int | None = None,
        key: str | None = None,
) -> SSEChannel:

    stmt = delete(SSEChannel).where(
        SSEChannel.session_id == session_id,
    )
    if profile_id:
        stmt = stmt.where(
            SSEChannel.profile_id == profile_id
        )
    if key:
        stmt = stmt.where(SSEChannel.key == key)

    sess().execute(stmt)

    sess().flush()

    """Створення нового SSE каналу"""
    channel = SSEChannel.create_sync(
        target=target,
        key=key,
        profile_id=profile_id,
        session_id=session_id,
    )
    sess().add(channel)
    sess().commit()
    return channel
