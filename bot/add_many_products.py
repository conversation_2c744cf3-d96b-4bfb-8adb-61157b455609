import asyncio

from db.models import StoreProduct, Store
from db import DBSession


async def add_many_products(brand_id: int, store_id: int, count: int):
    try:
        start = 0
        with DBSession() as db:
            store = await Store.get(store_id)
            print(f"*** store {store.name}")
            for i in range(count):
                print("*** product", start + i)
                product = StoreProduct(
                    brand_id=brand_id,
                    stores=[store],
                    categories=[],
                    position=1,
                    name=f"Product {start + i}",
                    price=100,
                    external_id=f"external_id_{start + i}",
                    product_id=f"product_id_{start + i}",
                )
                db.add(product)

            print("*** commit")
            db.commit()
    except Exception as e:
        print(e)


if __name__ == "__main__":
    asyncio.run(add_many_products(125, 171, 60000))
