import asyncio

from db import DBSession, crud
from db.models import OrderShipment, BrandCustomSettings, StoreOrder, Store


async def migrate():
    with DBSession() as db:
        order_shipments = db.query(OrderShipment).filter(OrderShipment.settings_id.is_(None)).all()
        not_found_count = 0
        found_count = 0
        total_count = 0
        for order_shipment in order_shipments:
            store_order = await StoreOrder.get(id=order_shipment.store_order_id)
            store = await Store.get(id=store_order.store_id)
            brand = await crud.get_brand_by_store(store.id)
            total_count += 1
            print(f"*** Started migration for order shipment {order_shipment.id}, {store.id}")
            if brand:
                if order_shipment.name:
                    shipment_settings = await BrandCustomSettings.get_list(_name=order_shipment.name, brand_id=brand.id)
                    if shipment_settings and len(shipment_settings):
                        found_count += 1
                        order_shipment.settings_id = shipment_settings[0].id
                        continue
                else:
                    shipment_settings = await BrandCustomSettings.get_list(custom_type="shipment", brand_id=brand.id)
                    if shipment_settings and len(shipment_settings):
                        found_count += 1
                        order_shipment.settings_id = shipment_settings[0].id
                        continue
                if brand.id == 89:
                    found_count += 1
                    order_shipment.settings_id = 84
                    continue
                elif brand.id == 94:
                    found_count += 1
                    order_shipment.settings_id = 86
                    continue

            not_found_count += 1
            print(
                f"*** Started migration for order shipment not found {order_shipment.name} {order_shipment.id},"
                f" {store.id},"
                f" {brand.id}"
            )

        db.commit()
        print(f"*** Migration finished. Total: {total_count}, Found: {found_count}, Not found: {not_found_count}")


if __name__ == '__main__':
    asyncio.run(migrate())
