from fastapi import APIRouter, Depends, Security

import schemas
from api.admin.router.profiles.profile.storage.service import StorageService

router = APIRouter(
    prefix="/storage"
)


@router.post("/{profile_id}/upload")
async def upload_media_to_storage(
        data: schemas.UploadMediaData = Depends(),
        profile_id: int | None = None,
        service: StorageService = Security(scopes=["me:write", "storage:edit"]),
) -> schemas.StorageSchema:
    return await service.upload_media(data, profile_id)
