from fastapi import Depends, Path

import schemas
from api.crm.base.item_service import CRMItemService
from core.auth.depend import Action, get_active_user
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError
from core.crm_ticket.functions import set_crm_ticket_status
from db import crud
from db.models import CRMTicket, ClientBot, Group, User
from schemas import CRMTicketStatusInitiatedByEnum


class TicketService(CRMItemService[CRMTicket]):

    @staticmethod
    async def get_item(
            ticket_id: int = Path(ge=1),
            action: str = Action(
                "crm_ticket:read",
                ("crm_ticket:read", "crm_ticket:edit"),
            ),
            user: User = Depends(get_active_user)
    ):
        if not (ticket := await crud.get_crm_ticket(ticket_id, action, user.id)):
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                action,
                {"ticket_id": ticket_id},
            )
        return ticket

    async def ticket_to_schema(self):
        ticket = self.item
        base_schema = schemas.CRMTicketBaseSchema.from_orm(ticket)

        group = await Group.get(ticket.group_id)
        bot = await ClientBot.get(ticket.bot_id)

        ticket_user = await User.get_by_id(ticket.user_id)

        return schemas.CRMTicketSchema(
            **base_schema.dict(),
            business_name=group.name,
            bot_name=bot.display_name if bot else None,
            profile_id=group.id,
            items_text=ticket.title,
            first_name=ticket_user.first_name,
            last_name=ticket_user.last_name,
            full_name=ticket_user.full_name,
            email=ticket_user.email,
            photo_url=ticket_user.photo_url,
            status_history=await crud.get_crm_ticket_status_history(ticket.id)
        )

    async def set_ticket_status(self, data: schemas.CRMChangeTicketStatusData):
        await set_crm_ticket_status(
            self.item, data.status,
            CRMTicketStatusInitiatedByEnum.MANAGER,
            initiated_by_user=self.user,
            header=data.header,
            message=data.message,
            internal_comment=data.internal_comment,
            notify_user=data.notify_user,
            ignore_session_id=self.auth_session_id,
        )
        return await self.ticket_to_schema()
