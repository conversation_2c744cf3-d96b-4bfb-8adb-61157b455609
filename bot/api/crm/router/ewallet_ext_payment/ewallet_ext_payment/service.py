from fastapi import Depends, Path

import schemas
from api.crm.base.item_service import CRMItemService
from core.auth.depend import Action, get_active_user
from core.ewallet.external_payment.functions import set_ewallet_ext_payment_status
from db import crud
from db.models import ClientBot, EWallet, EWalletExternalPayment, Group, User
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError


class EwalletExtPaymentService(CRMItemService[EWalletExternalPayment]):
    @staticmethod
    async def get_item(
            ewallet_ext_payment_id: int = Path(ge=1),
            action: str = Action(
                "crm_ewallet_ext_payment:read",
                ("crm_ewallet_ext_payment:read", "crm_ewallet_ext_payment:edit"),
            ),
            user: User = Depends(get_active_user)
    ):
        if not (ewallet_ext_payment := await crud.get_ewallet_ext_payment(
                ewallet_ext_payment_id, action, user.id
        )):
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                action,
                {"ewallet_ext_payment_id": ewallet_ext_payment_id},
            )
        return ewallet_ext_payment

    async def to_schema(self):
        group = await Group.get(self.item.profile_id)
        ewallet = await EWallet.get(self.item.ewallet_id)
        bot = await ClientBot.get(ewallet.bot_id)

        payment_user: User = await User.get_by_id(
            self.item.user_id
        )

        if self.item.payer_id:
            payer = await User.get_by_id(self.item.payer_id)
            payer_data = {
                "payer_name": payer.name,
                "payer_email": payer.email,
                "is_payer": payer.id == self.user.id,
            }
        else:
            payer_data = {
                "is_payer": False
            }

        schema = schemas.CRMEwalletExtPaymentSchema(
            **self.item.as_dict(True),
            **payer_data,
            crm_tag=self.item.crm_tag,
            profile_name=group.name,
            business_name=group.name,
            bot_name=bot.display_name if bot else None,
            first_name=payment_user.first_name,
            last_name=payment_user.last_name,
            full_name=payment_user.full_name,
            email=payment_user.email,
            photo_url=payment_user.photo_url,
            items_text="",
            change_date=self.item.time_updated,
            sum_to_pay=self.item.amount,
            status_history=await crud.get_ewallet_ext_payment_status_history(
                self.item.id
            )
        )

        if self.item.payer_id != self.user.id:
            schema.transfer_data = None

        return schema

    async def set_status(self, new_status: schemas.EWalletExternalPaymentStatus):
        await set_ewallet_ext_payment_status(
            self.item, self.user,
            new_status, self.lang
        )
