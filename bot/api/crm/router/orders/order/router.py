from fastapi import APIRouter, Depends, Security

import schemas
from .service import OrderService

router = APIRouter(
    prefix="/{order_id}"
)


@router.get("/")
async def get_order(
        service: OrderService = Depends(),
) -> schemas.CRMOrderSchema:
    return await service.get_order()


@router.post("/set-status")
async def set_order_status(
        data: schemas.CRMChangeOrderStatusData,
        service: OrderService = Security(scopes=["me:write", "crm_order:edit"]),
) -> schemas.CRMOrderSchema:
    return await service.set_order_status(data)
