from fastapi import APIRouter, Depends, Query, Security

import schemas
from schemas import (
    TaskCreateResultResponse, TaskListSchema, TaskStatusEnum, TaskTypeTaskEnum,
)
from .service import TasksService

router = APIRouter()


@router.get("/")
async def get_tasks(
        statuses: list[TaskStatusEnum] = Query(None, description="array of statuses"),
        exclude: list[int] | None = Query(None, description="array of task ids to exclude"),
        include: list[int] | None = Query(None, description="array of task ids to select only in response"),
        offset: int | None = Query(None),
        limit: int = Query(10, description="limit products. Max: 100"),
        object_id: int | None = Query(None, description="object id"),
        type_tasks: list[TaskTypeTaskEnum] = Query(None, description="array of types of task"),
        service: TasksService = Depends(),
) -> list[TaskListSchema]:
    return await service.get_tasks(statuses, offset, limit, include, exclude, False, object_id, type_tasks)


@router.get("/count")
async def get_tasks_count(
        statuses: list[TaskStatusEnum] = Query(None, description="array of statuses"),
        exclude: list[int] | None = Query(None, description="array of task ids to exclude"),
        include: list[int] | None = Query(None, description="array of task ids to select only in response"),
        service: TasksService = Depends(),
) -> int:
    return await service.get_tasks(statuses, None, None, include, exclude, True)


@router.post("/")
async def create_task(
        data: schemas.CreateTaskSchema,
        service: TasksService = Security(scopes=["me:write", "task:edit"]),
) -> TaskCreateResultResponse:
    return await service.create_task(data)


@router.post("/cancel")
async def cancel_tasks(
        data: list[int] | None = Query(None, description="array of task ids to cancel"),
        service: TasksService = Security(scopes=["me:write", "task:edit"]),
) -> list[schemas.TaskSchemaProduct]:
    return await service.update_tasks(schemas.TaskStatusEnum.CANCELED, data)


@router.post("/delete")
async def delete_tasks(
        data: list[int],
        service: TasksService = Security(scopes=["me:write", "task:edit"]),
) -> list[schemas.TaskSchemaProduct]:
    return await service.update_tasks(schemas.TaskStatusEnum.DELETED, data)
