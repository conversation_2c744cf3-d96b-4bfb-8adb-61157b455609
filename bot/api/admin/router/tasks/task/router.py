from fastapi import APIRouter, Depends, Security

import schemas
from schemas import OkResponse, TaskSchema

from .service import TaskService

router = APIRouter(
    prefix="/{task_id}"
)


@router.get("/")
async def get_task(
        service: TaskService = Depends(),
) -> TaskSchema:
    return await service.get_task()


@router.patch("/cancel")
async def cancel_task(
        service: TaskService = Security(scopes=["me:write", "task_id:edit"])
) -> OkResponse:
    return await service.cancel_task()


@router.delete("/")
async def delete_task(
        service: TaskService = Security(scopes=["me:write", "task_id:edit"])
) -> schemas.OkResponse:
    return await service.delete_task()
