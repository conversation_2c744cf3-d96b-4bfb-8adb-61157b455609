from fastapi import APIRouter, Depends, Security

import schemas
from api.admin.router.billing.service import AdminBillingService
from api.depends import lang_query_param_depend
from config import (
    BILLING_ANNUAL_FREE_MOUNTS_COUNT,
    BILLING_FIRST_YEAR_SALE_PERCENT, DEFAULT_TRIAL_PERIOD_DAYS,
)
from schemas import BillingProductCode
from utils.scopes_map import ACTION_NONE
from . import verification_documents

router = APIRouter(
    prefix="/{profile_id}/billing",
    tags=["billing"],
)
router.include_router(verification_documents.router)


@router.get("/check_subscription")
async def check_profile_subscription(
        service: AdminBillingService = Security(scopes=["profile:read"])
) -> schemas.BillingCheckProfileSubscriptionResponse:
    return schemas.BillingCheckProfileSubscriptionResponse(
        is_subscribed=await service.is_subscribed()
    )


@router.get(
    "/current_tariff_plan",
    dependencies=[Depends(lang_query_param_depend)],
)
async def get_profile_current_tariff_plan(
        service: AdminBillingService = Depends(),
) -> schemas.BillingCurrentTariffPlanResponse:
    return schemas.BillingCurrentTariffPlanResponse(
        tariff_plan=await service.get_current_tariff_plan(),
        params=schemas.BillingTariffPlanParams(
            has_subscription_before=await service.has_subscription_before(),
            first_year_sale_percent=BILLING_FIRST_YEAR_SALE_PERCENT,
            billing_annual_free_mounts_count=BILLING_ANNUAL_FREE_MOUNTS_COUNT,
            default_trial_days=DEFAULT_TRIAL_PERIOD_DAYS,
        )
    )


@router.get("/usages")
async def get_profile_billing_products_usages(
        params: schemas.GetBillingProductsUsagesParams = Depends(),
        service: AdminBillingService = Depends(),
) -> schemas.BillingProductsUsagesData:
    return schemas.BillingProductsUsagesData(
        products=await service.get_products_usages(params),
    )


@router.get("/tax-ids-info")
async def get_profile_tax_ids_info(
        service: AdminBillingService = Depends(),
) -> schemas.TaxIdsInfo:
    return await service.get_tax_ids_info()


@router.post("/subscribe")
async def create_profile_billing_subscriptions(
        data: schemas.BillingSubscribeData,
        service: AdminBillingService = Security(scopes=["me:write"]),
) -> schemas.BillingCurrentTariffPlanSchema:
    return await service.subscribe(data)


@router.post("/activate")
async def activate_billing_for_profile(
        service: AdminBillingService = Security(scopes=["platform:admin", "me:write"]),
) -> schemas.OkResponse:
    return await service.activate_billing_for_profile()


@router.post("/subscriptions/{subscription_id}/update-payment-method")
async def update_payment_method(
        subscription_id: int,
        data: schemas.BillingSubscriptionUpdatePaymentMethodData,
        service: AdminBillingService = Security(scopes=["me:write"]),
) -> schemas.BillingCurrentTariffPlanSchema:
    return await service.update_subscription_payment_method(subscription_id, data)


@router.patch('/subscriptions/items/{item_id}')
async def update_billing_subscription_item(
        item_id: int,
        data: schemas.UpdateBillingSubscriptionItemData,
        service: AdminBillingService = Security(scopes=["me:write"]),
) -> schemas.BillingSubscriptionItemSchema:
    return await service.update_subscription_item(item_id, data)


@router.post("/make_setup_payment_method_session")
async def make_setup_payment_method_session(
        data: schemas.MakeSetupPaymentMethodSessionData,
        service: AdminBillingService = Security(scopes=["me:write"])
) -> schemas.StripeCheckoutSessionClientSecretResponse:
    return await service.make_setup_payment_method_session(data)


@router.get("/payment_methods")
async def get_profile_billing_payment_methods(
        service: AdminBillingService = Depends(),
) -> list[schemas.BillingPaymentMethodSchema]:
    return await service.get_payment_methods()


@router.delete("/payment_methods/{payment_method_id}")
async def detach_profile_billing_payment_method(
        payment_method_id: str,
        service: AdminBillingService = Security(scopes=["me:write"]),
) -> schemas.BillingPaymentMethodSchema:
    return await service.detach_payment_method(payment_method_id)


@router.post("/make_billing_portal_session")
async def make_billing_portal_session(
        service: AdminBillingService = Security(scopes=["me:write"]),
) -> schemas.StripeBillingPortalSessionResponse:
    return await service.make_billing_portal_session()


@router.get(
    "/{product_code}/available-units",
    dependencies=[Depends(lang_query_param_depend)]
)
async def get_available_billing_units(
        product_code: BillingProductCode,
        params: schemas.BillingAvailableUnitsParams = Depends(),
        service: AdminBillingService = Security(scopes=[ACTION_NONE]),
) -> schemas.BillingAvailableUnitsResponse:
    return await service.get_available_units(product_code, params)
