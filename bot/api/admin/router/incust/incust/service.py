import logging

from fastapi import <PERSON><PERSON><PERSON><PERSON>xception
from incust_client_api_client import ApiException as ClientApiException
from incust_client_api_client.api.loyalty_api import LoyaltyApi
from incust_terminal_api_client.api.settings_api import SettingsApi

import schemas
from api.admin.router.incust.incust.exceptions import (
    IncustServerApiError, LoyaltyIdError, UnknownValidateIncustError,
    WhitelabelError,
)
from api.admin.router.incust.incust.funcs import convert_incust_settings_to_schema
from core.auth.services.scopes_checker import ScopesCheckerService
from core.loyalty import run_with_incust_client_api, run_with_incust_terminal_api
from db import crud
from db.models import Brand, Store, User
from db.models.store.loyalty_settings import LoyaltySettings


class IncustAdminService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_incust_settings(
            self,
            brand: Brand | None = None
    ) -> schemas.AdminIncustSettingsSchema:
        if not brand:
            brand = await Brand.get(group_id=self.profile_id)

        incust_settings = await crud.get_brand_settings_by_type(
            brand.id, 'incust_'
        )

        return await convert_incust_settings_to_schema(incust_settings, brand.id)

    async def update_admin_incust_settings(
            self,
            data: schemas.AdminIncustSettingsSchema | None,
    ) -> schemas.AdminIncustSettingsSchema:

        brand = await Brand.get(group_id=self.profile_id)

        if any([data.incust_loyalty_id, data.incust_loyalty_id, data.incust_term_api]):
            await self.validate_incust_settings(data)

        await crud.update_brand_settings(brand.id, data, 'incust_')

        return await self.get_incust_settings(brand)

    async def validate_incust_settings(
            self,
            data: schemas.AdminIncustSettingsSchema | None,
    ) -> schemas.AdminIncustValidateSchema:

        if not data.incust_server_api:
            logging.error(f"{data.incust_server_api=}")
            raise IncustServerApiError()

        brand = await Brand.get(group_id=self.profile_id)

        try:
            loyalty_settings = LoyaltySettings(
                loyalty_id=data.incust_loyalty_id,
                server_url=data.incust_server_api,
                white_label_id=data.incust_white_label_id or None
            )
            await run_with_incust_client_api(
                loyalty_settings, None,
                LoyaltyApi.incust_controllers_client_client_loyalty,
                loyalty_id=data.incust_loyalty_id,
                lang=self.lang
            )
        except ClientApiException as e:
            if e.status == 400 and 'white label' in str(e.body).lower():
                raise WhitelabelError()
            elif e.status == 404 and 'loyalty' in str(e.body).lower():
                raise LoyaltyIdError()
            else:
                raise UnknownValidateIncustError(str(e))
        except Exception as e:
            logging.error(e, exc_info=True)
            raise UnknownValidateIncustError(str(e))

        # Перевірка terminal_api_key через новий клієнт
        try:
            # Створюємо LoyaltySettings для перевірки ключа
            terminal_loyalty_settings = LoyaltySettings(
                terminal_api_key=data.incust_term_api,
                server_url=data.incust_server_api,
                white_label_id=data.incust_white_label_id or None
            )
            # Отримуємо налаштування терміналу
            terminal_settings = await run_with_incust_terminal_api(
                terminal_loyalty_settings,
                SettingsApi.incust_controllers_term_api_settings
            )
            data.incust_terminal_id = terminal_settings.id
            # Якщо є налаштування для магазинів — оновлюємо їх
            if data.stores_settings:
                for store_settings in data.stores_settings:
                    if store_settings.terminal_api_key:
                        store_loyalty_settings = LoyaltySettings(
                            terminal_api_key=store_settings.terminal_api_key,
                            server_url=data.incust_server_api,
                            white_label_id=data.incust_white_label_id or None
                        )
                        store_terminal_settings = await run_with_incust_terminal_api(
                            store_loyalty_settings,
                            SettingsApi.incust_controllers_term_api_settings
                        )
                        store = await Store.get(store_settings.store_id)
                        if store:
                            await store.update(incust_terminal_id=store_terminal_settings.id)
        except Exception as e:
            logging.error(e, exc_info=True)
            raise HTTPException(status_code=500, detail="Terminal API key validation failed")

        return schemas.AdminIncustValidateSchema(result='success')
