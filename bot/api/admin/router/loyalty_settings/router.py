from typing import Optional

from fastapi import APIRouter, Query, Security, status, Depends

from schemas.admin.loyalty_settings import (
    LoyaltySettingsCopySchema,
    LoyaltySettingsCreateSchema,
    LoyaltySettingsListSchema,
    LoyaltySettingsResponseSchema,
    LoyaltySettingsUpdateSchema,
    LoyaltySettingsValidateSchema,
)
from .service import LoyaltySettingsAdminService

router = APIRouter(
    prefix="/{profile_id}/loyalty-settings",
    tags=["loyalty-settings"]
)


@router.get("/", response_model=list[LoyaltySettingsListSchema] | None)
async def get_loyalty_settings_list(
    service: LoyaltySettingsAdminService = Depends(),
    store_id: Optional[int] = Query(None, description="Filter by store ID"),
    invoice_template_id: Optional[int] = Query(None, description="Filter by invoice template ID"),
    product_id: Optional[int] = Query(None, description="Filter by product ID"),
    is_enabled: Optional[bool] = Query(None, description="Filter by enabled status"),
    limit: int = Query(100, ge=1, le=1000, description="Limit"),
    offset: int = Query(0, ge=0, description="Offset"),
) -> list[LoyaltySettingsResponseSchema] | None:
    return await service.get_loyalty_settings_list(
        store_id=store_id,
        product_id=product_id,
        invoice_template_id=invoice_template_id,
        is_enabled=is_enabled,
        limit=limit,
        offset=offset
    )


@router.get("/{loyalty_settings_id}", response_model=LoyaltySettingsResponseSchema)
async def get_loyalty_settings(
    loyalty_settings_id: int,
    service: LoyaltySettingsAdminService = Depends()
) -> LoyaltySettingsResponseSchema:
    return await service.get_loyalty_settings_by_id(loyalty_settings_id)


@router.post("/", response_model=LoyaltySettingsResponseSchema, status_code=status.HTTP_201_CREATED)
async def create_loyalty_settings(
    data: LoyaltySettingsCreateSchema,
    service: LoyaltySettingsAdminService = Security(scopes=["me:write", "profile:edit"])
) -> LoyaltySettingsResponseSchema:
    return await service.create_loyalty_settings(data)


@router.patch("/{loyalty_settings_id}", response_model=LoyaltySettingsResponseSchema)
async def update_loyalty_settings(
    loyalty_settings_id: int,
    data: LoyaltySettingsUpdateSchema,
    service: LoyaltySettingsAdminService = Security(scopes=["me:write", "profile:edit"])
) -> LoyaltySettingsResponseSchema:
    return await service.update_loyalty_settings(loyalty_settings_id, data)


@router.delete("/{loyalty_settings_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_loyalty_settings(
    loyalty_settings_id: int,
    service: LoyaltySettingsAdminService = Security(scopes=["me:write", "profile:edit"])
):
    await service.delete_loyalty_settings(loyalty_settings_id)
    return None


@router.post("/copy", response_model=LoyaltySettingsResponseSchema, status_code=status.HTTP_201_CREATED)
async def copy_loyalty_settings(
    data: LoyaltySettingsCopySchema,
    service: LoyaltySettingsAdminService = Security(scopes=["me:write", "profile:edit"])
) -> LoyaltySettingsResponseSchema:
    return await service.copy_loyalty_settings(data)


@router.post("/validate", response_model=LoyaltySettingsValidateSchema)
async def validate_loyalty_settings(
    data: LoyaltySettingsCreateSchema,
    service: LoyaltySettingsAdminService = Depends()
) -> LoyaltySettingsValidateSchema:
    """Валідувати налаштування лояльності"""
    return await service.validate_loyalty_settings(data)
