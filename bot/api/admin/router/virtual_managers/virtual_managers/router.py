from fastapi import APIRouter, Depends, Security

import schemas
from .service import VMsService

router = APIRouter()


@router.get("/")
async def get_virtual_manager_list(
        params: schemas.AdminListParams = Depends(),
        service: VMsService = Depends()
) -> list[schemas.AdminVirtualManagerListSchema]:
    return await service.get_list(params)


@router.post("/")
async def create_virtual_manager(
        data: schemas.AdminCreateVirtualManagerData,
        service: VMsService = Security(scopes=["vm:create", "me:write"])
) -> schemas.AdminVirtualManagerSchema:
    return await service.create_vm(data)


@router.get("/steps/{vm_step_id}")
async def get_virtual_manager_step(
        vm_step_id: int,
        service: VMsService = Depends()
) -> schemas.AdminVirtualManagerStepSchema:
    return await service.get_virtual_manager_step(vm_step_id)
