from fastapi import APIRouter, Depends, Query

import schemas
from .service import WebhookJournalService

router = APIRouter(
    prefix="/journal/{webhook_id}",
)


@router.get("/")
async def get_webhook_journal_list(
    params: schemas.AdminWebhookJournalParams = Depends(),
    service: WebhookJournalService = Depends()
) -> list[schemas.AdminWebhookJournalListSchema]:
    return await service.get_list(params)


@router.post("/resend")
async def resend_event(
    data: schemas.AdminResendWebhookEventData,
    service: WebhookJournalService = Depends()
) -> schemas.OkResponse:
    return await service.resend_event(data)
