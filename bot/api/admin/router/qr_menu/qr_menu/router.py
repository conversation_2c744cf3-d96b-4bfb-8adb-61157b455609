from fastapi import APIRouter, Depends, Security

import schemas
from core.api.depends import build_form_data_depend

from .service import QrMenuService

router = APIRouter(
    prefix="/{qr_menu_id}"
)


@router.get("/")
async def get_menu(
    service: QrMenuService = Depends()
) -> schemas.AdminQrMenuSchema:
    return await service.get_menu()


@router.patch("/")
async def update_menu(
    data: schemas.AdminUpdateQrMenuData,
    service: QrMenuService = Security(scopes=["me:write", "qr_menu:edit"])
) -> schemas.AdminQrMenuSchema:
    return await service.update_menu(data)


@router.delete("/")
async def delete_menu(
    service: QrMenuService = Security(scopes=["me:write", "qr_menu:edit"])
) -> schemas.OkResponse:
    return await service.delete_menu()


@router.get("/qr_media")
async def get_menu_qr_media_objects(
    service: QrMenuService = Depends()
) -> list[schemas.QrMediaObjectSchema]:
    return await service.get_qr_media_objects()


@router.post("/qr_media")
async def create_menu_qr_media_object(
    form_data: schemas.CreateQrMediaObject = Depends(
        build_form_data_depend(
            schemas.CreateQrMediaObject, ["additional_media"], {
                "additional_media": ["name", "qr_media_file"]
            }
        )
    ),
    service: QrMenuService = Security(scopes=["me:write", "qr_menu:edit"])
) -> schemas.QrMediaObjectSchema:
    return await service.create_qr_media_object(form_data)


@router.patch("/qr_media/{qr_media_id}")
async def update_menu_qr_media_object(
    qr_media_id: int,
    form_data: schemas.UpdateQrMediaObject = Depends(
        build_form_data_depend(
            schemas.UpdateQrMediaObject, ["additional_media"], {
                "additional_media": ["name", "qr_media_file", "id"]
            }
        )
    ),
    service: QrMenuService = Security(scopes=["me:write", "qr_menu:edit"])
) -> schemas.QrMediaObjectSchema:
    return await service.update_qr_media_object(form_data, qr_media_id)


@router.delete("/qr_media/{qr_media_id}")
async def delete_menu_qr_media_object(
    qr_media_id: int,
    service: QrMenuService = Security(scopes=["me:write", "qr_menu:edit"])
) -> schemas.OkResponse:
    return await service.delete_qr_media_object(qr_media_id)


@router.delete("/qr_media/{qr_media_id}/{qr_additional_media_id}")
async def delete_menu_qr_additional_media_object(
    qr_media_id: int,
    qr_additional_media_id: int,
    service: QrMenuService = Security(scopes=["me:write", "qr_menu:edit"])
) -> schemas.OkResponse:
    return await service.delete_qr_media_additional_object(qr_media_id, qr_additional_media_id)
