from fastapi import APIRouter, Depends, Query, Security

import schemas
from .service import CharacteristicsService

router = APIRouter()


@router.get("/")
async def get_characteristics_list(
        search_text: str | None = Query(None),
        exclude: list[int] | None = Query(None, description="array of characteristic ids to exclude"),
        include: list[int] | None = Query(None, description="array of characteristics to include only in the response"),
        offset: int | None = Query(None),
        limit: int = Query(10, description="limit products. Max: 100"),
        exclude_from_product_group_id: int | None = Query(None),
        service: CharacteristicsService = Depends()
) -> list[schemas.AdminCharacteristicListSchema]:
    return await service.get_characteristics_list(
        search_text, exclude, include, offset, limit, exclude_from_product_group_id
    )


@router.get("/total_count")
async def get_characteristics_total_count(
        search_text: str | None = Query(None),
        service: CharacteristicsService = Depends()
) -> int:
    return await service.get_characteristics_total_count(search_text)


@router.post("/")
async def create_characteristic(
        data: schemas.AdminCreateCharacteristicData,
        add_to_product_group_id: int | None = Query(None),
        service: CharacteristicsService = Security(scopes=["me:write", "menu:create"])
) -> schemas.AdminCharacteristicSchema:
    return await service.create_characteristic(data, add_to_product_group_id)
