from fastapi import APIRouter, Security

import schemas
from .service import CategoriesActionsService

router = APIRouter()


@router.delete("/action_delete_categories")
async def mass_delete_categories(
        data: schemas.AdminCategoriesActionPayloadSchema,
        service: CategoriesActionsService = Security(scopes=["me:write", "category:edit"])
) -> schemas.OkResponse:
    return await service.mass_delete_categories(data)


@router.post("/action_up_categories_to_store/{store_id}")
async def mass_up_categories_to_store(
        store_id: int,
        data: schemas.AdminCategoriesActionPayloadSchema,
        service: CategoriesActionsService = Security(scopes=["me:write", "category:edit"])
) -> schemas.OkResponse:
    return await service.mass_up_categories_to_store(data, store_id)


@router.post("/action_remove_categories_from_store/{store_id}")
async def mass_remove_categories_from_store(
        store_id: int,
        data: schemas.AdminCategoriesActionPayloadSchema,
        service: CategoriesActionsService = Security(scopes=["me:write", "category:edit"])
) -> schemas.OkResponse:
    return await service.mass_remove_categories_from_store(data, store_id)
