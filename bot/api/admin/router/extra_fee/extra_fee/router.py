from fastapi import APIRouter, Depends, Security

import schemas
from .service import ExtraFeeService

router = APIRouter(
    prefix="/{extra_fee_id}"
)


@router.get("/")
async def get_extra_fee(
        service: ExtraFeeService = Depends()
) -> schemas.AdminExtraFeeSchema:
    extra_fee = await service.get_extra_fee()
    return await service.extra_fee_to_schema(extra_fee)


@router.patch("/")
async def update_extra_fee(
        data: schemas.AdminUpdateExtraFeeData,
        service: ExtraFeeService = Security(scopes=["me:write", "extra_fee:edit"])
) -> schemas.AdminExtraFeeSchema:
    return await service.update_extra_fee(data)


@router.delete("/")
async def delete_extra_fee(
        service: ExtraFeeService = Security(scopes=["me:write", "extra_fee:edit"])
) -> schemas.OkResponse:
    return await service.delete_extra_fee()
