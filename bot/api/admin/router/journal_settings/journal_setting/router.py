from fastapi import APIRouter, Depends, Security

import schemas
from .service import JournalSettingService

router = APIRouter(
    prefix="/{journal_setting_id}",
)


@router.get('/')
async def get_journal_setting(
        service: JournalSettingService = Depends()
) -> schemas.AdminJournalSettingOneSchema:
    journal_setting = await service.get_journal_setting()
    return service.journal_setting_to_schema(journal_setting)


@router.patch('/')
async def update_journal_setting(
        data: schemas.AdminJournalSettingUpdateSchema,
        service: JournalSettingService = Security(
            scopes=["me:write", "profile:edit"]
        )
) -> schemas.AdminJournalSettingOneSchema:
    return await service.update_journal_setting(data)


@router.delete('/')
async def delete_journal_setting(
        service: JournalSettingService = Security(
            scopes=["me:write", "profile:edit"]
        )
) -> schemas.OkResponse:
    return await service.delete_journal_setting()
