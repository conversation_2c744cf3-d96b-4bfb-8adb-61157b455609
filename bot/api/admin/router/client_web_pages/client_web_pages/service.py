import schemas
from api.exceptions import APIListLimitError
from core.auth.services.scopes_checker import ScopesCheckerService
from core.client_web_pages.functions import check_and_create_default_pages
from core.helpers import create_url_prefix_from_text
from db import crud
from db.models import (
    Group, InvoiceTemplate, Store, User,
)
from ..functions import (
    client_web_page_to_admin_list_schema, client_web_page_to_admin_schema,
)


class ClientWebPagesService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_client_web_pages(
            self,
            store_ids: list[int] | None = None,
            invoice_templates_ids: list[int] | None = None,
            search_text: str | None = None,
            types: list[str] | None = None,
            offset: int | None = None,
            limit: int = 10,
    ) -> list[schemas.AdminClientWebPageListSchema]:
        profile = await Group.get(self.profile_id)
        if not limit or limit > 100:
            raise APIListLimitError(1, 100, 10)

        client_web_pages = await crud.get_admin_client_web_pages(
            profile, self.user.id,
            store_ids=store_ids if store_ids and len(store_ids) > 1 else None,
            invoice_templates_ids=invoice_templates_ids,
            search_text=search_text,
            types=types,
            offset=offset,
            limit=limit,
        )

        if not offset:
            result = await check_and_create_default_pages(client_web_pages, profile)

            if result:
                client_web_pages = await crud.get_admin_client_web_pages(
                    profile, self.user.id,
                    store_ids=store_ids if store_ids and len(store_ids) > 1 else None,
                    invoice_templates_ids=invoice_templates_ids,
                    search_text=search_text,
                    types=types,
                    offset=offset,
                    limit=limit,
                )

        return [
            await client_web_page_to_admin_list_schema(web_page)
            for web_page in client_web_pages
        ]

    async def is_client_web_page_slug_exist(
            self, slug: str | None = None, exclude_web_page_id: int | None = None
    ) -> bool:
        if not slug:
            return False
        return await crud.is_client_web_page_slug_exist(
            slug, self.profile_id, exclude_web_page_id
        )

    async def generate_client_web_page_slug(self, string: str) -> str:
        slug = await create_url_prefix_from_text(string, self.lang)

        counter = 1
        base_slug = slug

        while await self.is_client_web_page_slug_exist(slug):
            slug = f"{base_slug}-{counter}"
            counter += 1

        return slug

    async def create_client_web_page(
            self, data: schemas.AdminClientWebPageCreateData
    ) -> schemas.AdminClientWebPageOneSchema:
        profile = await Group.get(self.profile_id)

        allowed_stores_list = await crud.get_and_validate_access_on_objects(
            "store", Store, data.stores,
            self.user.id, self.profile_id,
            scope_name="edit",
        )

        invoice_templates = await crud.get_and_validate_access_on_objects(
            "category", InvoiceTemplate, data.invoice_templates,
            self.user.id, self.profile_id,
            # have to be read, because there is no edit access required for
            # connecting product to category
            scope_name="read",
        )

        stores_ids = [
            store.id for store in allowed_stores_list or []
        ]
        invoice_templates_ids = [
            invoice_template.id for invoice_template in invoice_templates or []
        ]

        # check if there are any previous special pages and disable them or remove
        # from objects
        await crud.check_and_disable_previous_special_pages(
            data.type, profile.id, data.show_in_profile, stores_ids,
            invoice_templates_ids
        )

        client_web_page = await crud.create_client_web_page(
            profile, data, allowed_stores_list, invoice_templates,
        )

        return await client_web_page_to_admin_schema(client_web_page)
