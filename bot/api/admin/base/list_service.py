from typing import Awaitable, Generic, Protocol, Type, TypeVar

from pydantic import BaseModel

from api.admin.helpers import check_object_access
from api.exceptions import APIListLimitError
from core.auth.services.scopes_checker import ScopesCheckerService
from db.models import User
from db.types.operation import Operation


class ParamsProtocol(Protocol):
    offset: int | None
    limit: int | None
    search_text: str | None


class GetListFunc(Protocol):
    async def __call__(
            self,
            profile_id: int,
            user_id: int,
            params: ParamsProtocol,
            operation: Operation = "list",
    ) -> list | int | bool:
        ...


SchemaT = TypeVar("SchemaT", bound=BaseModel)


class AdminListService(Generic[SchemaT]):
    object_name: str
    schema_type: Type[SchemaT]
    get_objects_func: GetListFunc

    def __init_subclass__(cls, **kwargs):
        if not getattr(cls, "get_objects_func", None):
            raise TypeError(
                "get_objects_func is not defined. Please, define it either as class "
                "variable or method"
            )

        cls.get_objects_func = staticmethod(cls.get_objects_func)

        super().__init_subclass__(**kwargs)

    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,
                "profile_id",
            )
    ):
        self.profile_id: int = scopes.data.profile_id
        self.user: User = scopes.user
        self.lang: str = scopes.lang

    @classmethod
    def validate_params(cls, params: ParamsProtocol):
        if not params.limit or params.limit > 100:
            raise APIListLimitError()

    async def object_to_schema(self, obj) -> SchemaT | Awaitable[SchemaT]:
        schema = self.schema_type.from_orm(obj)

        if hasattr(schema, "edit_allowed") and not hasattr(obj, "edit_allowed"):
            schema.edit_allowed = await check_object_access(
                self.object_name,
                obj.id,
                self.profile_id, self.user.id,
                scope_name="edit",
            )
        if hasattr(schema, "read_allowed") and not hasattr(obj, "read_allowed"):
            schema.read_allowed = getattr(schema, "edit_allowed", False) or (
                await check_object_access(
                    self.object_name,
                    obj.id,
                    self.profile_id, self.user.id,
                    scope_name="read",
                )
            )

        return schema

    async def map_objects(self, objects: list) -> list[SchemaT]:
        if not hasattr(self, "schema_type"):
            raise TypeError(
                "schema_type is not defined. "
                "Please, either define it as class variable or redefine map_objects "
                "method"
            )

        return [await self.object_to_schema(obj) for obj in objects]

    async def get_list(
            self, params: ParamsProtocol,
    ) -> list[SchemaT]:
        self.validate_params(params)

        db_objects = await self.get_objects_func(
            self.profile_id, self.user.id, params, "list"
        )

        return await self.map_objects(db_objects)
