import logging
from typing import Literal

from fastapi import Depends, Security
from psutils.exceptions import ErrorWithTextVariable
from psutils.receipts import Receipts<PERSON>hecker
from psutils.receipts.schemas import Pattern, Receipt as ReceiptSchema

import schemas
from core.api.depends import get_lang
from core.auth.depend import get_active_user
from core.external_coupon import send_coupon_info
from core.loyalty.incust_client_adapter import (
    run_with_incust_terminal_api,
    get_or_create_incust_customer,
)
from core.incust.helpers import get_currency_from_store_or_brand
from core.messangers_adapters import Bot as BotAdapter
from core.receipt.functions import receipt_to_schema
from core.store.depends import get_current_brand
from core.store.functions.brand import get_brand_scan_receipts_settings
from db import crud
from db.models import Brand, ClientBot, Receipt, User
from db.models.store.loyalty_settings import LoyaltySettings
from utils.numbers import format_currency
from utils.text import f, html_to_markdown
from incust_terminal_api_client.models.check import Check as IncustCheck
from .exceptions import (
    ScannerServiceInvalidBinCodeError, ScannerServiceInvalidCountryError,
    ScannerServiceNoReceiptAndReceiptIdError,
    ScannerServiceNoReceiptError, ScannerServiceReceiptCreateError,
    ScannerServiceReceiptExistError,
    ScannerServiceReceiptFinanceSystemError, ScannerServiceReceiptLoyaltyError,
    ScannerServiceReceiptProcessCheckError,
    ScannerServiceScanError,
)

debugger = logging.getLogger('debugger.scan_receipt')


class ScannerServiceBase:
    def __init__(
            self,
            lang: str = Depends(get_lang),
            brand: Brand = Depends(get_current_brand),
    ):
        self.scanner: ReceiptsChecker = ReceiptsChecker()
        self.pattern: Pattern | None = None
        self.lang: str = lang
        self.brand: Brand = brand
        self.user: User | None = None
        self.receipt: Receipt | None = None
        self.settings: schemas.BrandScanReceiptsSettings | None = None
        self.receipt_schema: ReceiptSchema | None = None
        self.loyalty_settings: LoyaltySettings | None = None

    async def scan_receipt(self, receipt_data: str) -> ReceiptSchema:
        try:
            pattern = await self.scanner.get_pattern(receipt_data)
            self.pattern = pattern
            scanned_receipt = await self.scanner.check(receipt_data)
            self.receipt_schema = scanned_receipt
        except ErrorWithTextVariable as ex:
            err_text = await f(ex.text_variable, self.lang, **ex.text_kwargs)
            raise ScannerServiceScanError(err_text)

        if not scanned_receipt:
            raise ScannerServiceNoReceiptError()

        await self.__validate_receipt(scanned_receipt)

        return scanned_receipt

    async def _process_loyalty(
            self, store_id: int | None = None
    ) -> IncustCheck | None:
        from db.models.store.loyalty_settings import LoyaltySettings
        from core.incust.schema_converter import convert_payload_to_check
        
        if not await self.brand.is_incust:
            raise ScannerServiceReceiptLoyaltyError()

        # Отримуємо налаштування лояльності
        loyalty_settings = await LoyaltySettings.get_loyalty_settings_for_context(
            brand_id=self.brand.id,
            store_id=store_id,
            group_id=self.brand.group_id,
        )
        
        if not loyalty_settings:
            raise ScannerServiceReceiptLoyaltyError()
        
        self.loyalty_settings = loyalty_settings
        
        # Синхронізуємо користувача з InCust якщо він є
        if self.user:
            await get_or_create_incust_customer(
                user=self.user,
                settings=loyalty_settings,
                lang=self.lang
            )

        check = await self.__receipt_to_incust_check(store_id)
        
        # Конвертуємо стару схему в нову
        check_data = convert_payload_to_check(check, user=self.user)
        
        # Використовуємо новий клієнт
        from incust_terminal_api_client.api.check_transactions_api import CheckTransactionsApi
        
        result_check = await run_with_incust_terminal_api(
            loyalty_settings,
            CheckTransactionsApi.incust_controllers_term_api_terminal_process_check,
            body=check_data,
        )
        
        return result_check

    async def __receipt_to_incust_check(
            self, store_id: int | None = None
    ) -> schemas.IncustCheckPayloadSchema:
        if self.receipt:
            items = await self.receipt.get_items()
        else:
            items = self.receipt_schema.items

        currency = await get_currency_from_store_or_brand(
            store_id=store_id,
            group_id=self.brand.group_id,
            exception=ScannerServiceReceiptFinanceSystemError
        )

        check = schemas.IncustCheckPayloadSchema(
            amount=self.receipt.total_price if self.receipt else
            self.receipt_schema.total_price,
            payment_type="currency",
            payment_id=currency,
            check_items=[
                schemas.CheckItem(
                    title=item.name,
                    code=item.name if not item.product_code else item.product_code,
                    price=item.price,
                    quantity=item.quantity,
                    amount=item.price * item.quantity,
                    category="uncategorized",
                    quantity_decimal_digits=3,
                )
                for item in items
            ],
        )

        return check

    async def __validate_receipt(self, receipt: ReceiptSchema):
        settings = await self._get_scan_settings()

        if settings.demo_mode:
            return

        if settings.country:
            if not self.__validate_country(settings.country):
                raise ScannerServiceInvalidCountryError()
        else:
            raise ScannerServiceInvalidCountryError()

        if str(receipt.organisation.ico_code) not in settings.bin_codes:
            debugger.debug(f"{receipt.organisation.ico_code=}")
            raise ScannerServiceInvalidBinCodeError()

        exist_receipt = await Receipt.get_by_receipt_id(receipt.receipt_id)
        if exist_receipt:
            raise ScannerServiceReceiptExistError()

    async def _get_scan_settings(self) -> schemas.BrandScanReceiptsSettings:
        if self.settings:
            return self.settings
        self.settings = await get_brand_scan_receipts_settings(self.brand.id)
        return self.settings

    def __validate_country(self, country_code: Literal["kz", "uz", "sk"]) -> bool:
        if not self.pattern:
            return False

        match country_code:
            case "kz":
                if self.pattern.processor_name in (
                        "Kazakhstan-oofd", "Kazakhstan-kofd", "Kazakhstan-ofd1"):
                    return True
            case "ua":
                if self.pattern.processor_name == "Ukraine":
                    return True
            case "uz":
                if self.pattern.processor_name == "Uzbekistan-ofd":
                    return True
            case "sk":
                if self.pattern.processor_name == "Slovakian":
                    return True
            case _:
                return False

        return False


class ScannerServiceUnAuth(ScannerServiceBase):
    def __init__(
            self,
            lang: str = Depends(get_lang),
            brand: Brand = Depends(get_current_brand),
    ):
        super().__init__(lang, brand)

    async def get_scanned_receipt(
            self, receipt_data: str, store_id: int | None = None
    ) -> schemas.ScanResponseUnAuthSchema:
        receipt = await self.scan_receipt(receipt_data)

        processed_check = await self._process_loyalty(store_id)
        if not processed_check:
            raise ScannerServiceReceiptProcessCheckError()

        # Конвертуємо Check у старий формат для зворотної сумісності з фронтендом
        from core.incust.schema_converter import convert_check_to_schema
        check_schema = convert_check_to_schema(processed_check) if processed_check else None
        
        return schemas.ScanResponseUnAuthSchema(
            receipt=receipt,
            check=check_schema,
        )


class ScannerService(ScannerServiceBase):
    def __init__(
            self,
            user: User = Security(get_active_user),
            lang: str = Depends(get_lang),
            brand: Brand = Depends(get_current_brand),
    ):
        super().__init__(lang, brand)
        self.user: User = user

    async def process(
            self,
            receipt_data: str | None = None,
            with_make_check: bool = False,
            order_id: int | None = None,
            receipt_schema: ReceiptSchema | None = None,
            store_id: int | None = None,
            with_bot_notification: bool = False,
    ) -> schemas.ScanResponseSchema:
        if receipt_data:
            scanned_receipt = await self.scan_receipt(receipt_data)
        elif receipt_schema:
            scanned_receipt = receipt_schema
        else:
            raise ScannerServiceNoReceiptError()

        await self.__create_receipt(scanned_receipt, order_id)
        processed_check = await self._process_loyalty(store_id)
        if not processed_check:
            raise ScannerServiceReceiptProcessCheckError()

        if with_make_check:
            res = await self.process_with_make_check(
                processed_check=processed_check, store_id=store_id
            )
        else:
            # Конвертуємо Check у старий формат для зворотної сумісності з фронтендом
            from core.incust.schema_converter import convert_check_to_schema
            check_schema = convert_check_to_schema(processed_check) if processed_check else None
            
            res = schemas.ScanResponseSchema(
                receipt_id=self.receipt.id,
                check=check_schema,
            )

        if with_bot_notification:
            if self.user.chat_id or self.user.wa_phone:
                try:
                    bot = await ClientBot.get(group_id=self.brand.group_id)
                    if bot:
                        coupons = []
                        if self.receipt.incust_transaction:
                            res.check.transaction = schemas.TerminalTransaction(
                                **self.receipt.incust_transaction
                            )
                            if res.check.transaction and res.check.transaction.id:
                                # Отримуємо купони через новий сервіс
                                coupons = await self._get_coupons_from_check(processed_check)
                        
                        msg_text = await self.get_rewards_text(
                            self.receipt.id, res.check, bot, store_id
                        )
                        bot_adapter = BotAdapter(bot=bot)
                        await bot_adapter.send_message(
                            self.user.chat_id or self.user.wa_phone, msg_text
                        )
                        if coupons:
                            for coupon in coupons:
                                try:
                                    await send_coupon_info(
                                        coupon,
                                        self.user,
                                        bot,
                                        self.lang,
                                        self.brand.id,
                                        store_id=store_id,
                                    )
                                except Exception as ex:
                                    logging.error(ex, exc_info=True)
                except Exception as ex:
                    logging.error(ex, exc_info=True)

        return res

    async def process_with_make_check(
            self, receipt_id: str | None = None,
            processed_check: IncustCheck | None = None,
            store_id: int | None = None,
    ):
        if not receipt_id and not self.receipt:
            raise ScannerServiceNoReceiptAndReceiptIdError()
        if receipt_id:
            receipt = await Receipt.get_by_receipt_id(receipt_id)
            if not receipt:
                raise ScannerServiceNoReceiptError()
            self.receipt = receipt

        if not processed_check:
            processed_check = await self._process_loyalty(store_id)
            if not processed_check:
                raise ScannerServiceReceiptProcessCheckError()

        if not self.loyalty_settings:
            from db.models.store.loyalty_settings import LoyaltySettings
            self.loyalty_settings = await LoyaltySettings.get_loyalty_settings_for_context(
                brand_id=self.brand.id,
                store_id=store_id,
                group_id=self.brand.group_id,
            )

        if not self.loyalty_settings:
            raise ScannerServiceReceiptLoyaltyError()

        # Встановлюємо skip_message для оригінального Check об'єкта
        if hasattr(processed_check, 'skip_message'):
            processed_check.skip_message = True
        
        # Робимо чек через новий Terminal API
        from incust_terminal_api_client.api.check_transactions_api import CheckTransactionsApi
        
        transaction = await run_with_incust_terminal_api(
            self.loyalty_settings,
            CheckTransactionsApi.incust_controllers_term_api_make_check_by_rules,
            rules_type="by-all-rules",
            check=processed_check
        )
        
        # Конвертуємо в старий формат для збереження
        from core.incust.schema_converter import convert_check_to_schema
        check_schema = convert_check_to_schema(processed_check)
        
        await crud.update_receipt_loyalty_data(
            self.receipt.id, check_schema.dict(), transaction.dict()
        )

        return schemas.ScanResponseSchema(
            receipt_id=self.receipt.id,
            check=check_schema,
        )

    async def get_receipt(
            self, receipt_id: int, store_id: int | None = None
    ) -> schemas.ReceiptSchema:
        receipt = await Receipt.get(receipt_id)
        if not receipt:
            raise ScannerServiceNoReceiptError()

        return await receipt_to_schema(
            receipt, self.user, self.brand, self.lang, store_id=store_id
        )

    async def get_rewards_text(
            self, receipt_id: int,
            check: IncustCheck,
            bot: ClientBot,
            store_id: int | None = None,
    ) -> str:
        if not self.receipt:
            receipt = await self.get_receipt(int(receipt_id), store_id)
            self.receipt = receipt

        currency = await get_currency_from_store_or_brand(
            group_id=self.brand.group_id,
            exception=ScannerServiceReceiptFinanceSystemError,
            store_id=store_id,
        )

        text = ""
        if bot.bot_type == "telegram":
            text = await f(
                "web app scan receipt success process header", self.lang,
                receipt_id=self.receipt.receipt_id,
                receipt_sum=format_currency(
                    self.receipt.total_price, currency, locale=self.lang
                ),
            ) + "\n\n"
        elif bot.bot_type == "whatsapp":
            text = await f(
                "web app wa scan receipt success process header", self.lang,
                receipt_id=self.receipt.receipt_id,
                receipt_sum=format_currency(
                    self.receipt.total_price, currency, locale=self.lang
                ),
            ) + "\n\n"
            text = html_to_markdown(text)

        if hasattr(check, 'special_accounts_charges') and check.special_accounts_charges and len(check.special_accounts_charges) > 0:
            accounts = ""
            special_mapping = await self._get_special_accounts_mapping()
            for account in check.special_accounts_charges:
                title = getattr(account, 'title', None)
                if not title:
                    account_id = getattr(account, 'id', None)
                    if account_id:
                        mapped_account = special_mapping.get(account_id, None)
                        if mapped_account:
                            # Використовуємо оригінальну схему SpecialAccount з InCust клієнта
                            title = getattr(mapped_account, 'public_title', None) or getattr(mapped_account, 'title', None)
                amount = getattr(account, 'amount', 0)
                accounts += f"\n{title}: {amount}"
            text += f"{accounts}"

        if hasattr(check, 'bonuses_added_amount') and check.bonuses_added_amount and check.bonuses_added_amount > 0:
            text += (f"\n{await f('check loyalty bonuses text', self.lang)} "
                     f"{check.bonuses_added_amount}")

        if hasattr(check, 'emitted_coupons') and check.emitted_coupons and len(check.emitted_coupons) > 0:
            text += (
                f"\n{await f('check loyalty vouchers text', self.lang)} "
                f"{len(check.emitted_coupons)}"
            )

        return text

    async def __create_receipt(
            self, receipt: ReceiptSchema,
            order_id: int | None = None,
    ):
        settings = await self._get_scan_settings()
        if settings.demo_mode:
            exist_receipt = await Receipt.get_by_receipt_id(receipt.receipt_id)
            if exist_receipt:
                self.receipt = exist_receipt
                return

        receipt = await crud.create_receipt(
            receipt, self.user, self.brand, order_id,
        )
        if not receipt:
            raise ScannerServiceReceiptCreateError()
        self.receipt = receipt

    async def _get_coupons_from_check(self, check: IncustCheck) -> list[schemas.CouponShowData]:
        """Отримує купони з чека через новий API"""
        try:
            if not check.emitted_coupons or not self.loyalty_settings:
                return []
            
            from core.invoice_loyalty.coupons_service import InvoiceLoyaltyCouponsService
            coupons_service = InvoiceLoyaltyCouponsService()
            
            coupons = []
            for coupon_obj in check.emitted_coupons:
                # Використовуємо оригінальну схему Coupon з InCust terminal API
                coupon_dict = {
                    'id': str(coupon_obj.id) if coupon_obj.id else None,
                    'code': coupon_obj.code,
                    'title': coupon_obj.title,
                    'description': coupon_obj.description,
                    'type': coupon_obj.type if coupon_obj.type else None,
                    'image': coupon_obj.image,
                }
                
                coupon = await coupons_service._get_coupon_data_for_show(
                    brand=self.brand,
                    loyalty_settings=self.loyalty_settings,
                    user=self.user,
                    coupon_data=coupon_dict,
                    lang=self.lang,
                    need_pdf_file=True,
                    base_url=self.loyalty_settings.server_url
                )
                if coupon:
                    coupons.append(coupon)
            
            return coupons
            
        except Exception as e:
            logging.error(f"Помилка отримання купонів з чека: {e}", exc_info=True)
            return []

    async def _get_special_accounts_mapping(self) -> dict:
        """Отримує маппінг спеціальних акаунтів через новий API"""
        try:
            if not self.loyalty_settings:
                return {}
            
            from incust_terminal_api_client.api.business_api import BusinessApi
            
            special_accounts = await run_with_incust_terminal_api(
                self.loyalty_settings,
                BusinessApi.incust_controllers_term_api_special_accounts,
                scope="retail"
            )
            
            if special_accounts:
                return {account.id: account for account in special_accounts}
            return {}
            
        except Exception as e:
            logging.error(f"Помилка отримання маппінгу спеціальних акаунтів: {e}", exc_info=True)
            return {}
