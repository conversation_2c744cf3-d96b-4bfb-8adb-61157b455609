import time

from fastapi import Depends, Security
from incust_terminal_api_client.models.check import Check
from psutils.exceptions import ErrorWithTextVariable
from psutils.exceptions.exception_handlers import UnknownErrorHandler

from api.client_api_router.store.routes.funcs import (
    check_products_is_available,
    process_store_order_full_bonuses_payment,
)
from core.api.depends import get_anonymous_user, get_lang
from core.auth.depend import get_user_optional, get_user_or_token_data_safe
from core.billing.functions import check_billing_transaction_limit
from core.ext.adapters.get_order.get_order_funcs import send_order_to_get_order
from core.ext.adapters.poster import send_order_to_poster
from core.ext.types import ExternalType
from core.incust.functions import validate_special_account_exist
from core.invoice.exception import (
    BaseInvoiceError, CreateOrderGiftFloatingSumError, CreateOrderGiftProductsTypeError,
    CreateOrderNeedAuthProductsInCartError, CreateOrderNoIncustCheckFor<PERSON><PERSON>ust<PERSON>rand<PERSON>rror,
    CreateOrderNoIncustDataForTopupError, CreateOrderNoIncustPayConfigForTopupError,
    CreateOrderNotFoundSpecialAccountForTopupError,
    CreateOrderNotMatchAccountForTopupError, CreateOrderProductWithTypeNotFoundError,
    CreateOrderTopupProductsTypeError,
    CreateOrderTypeProductsQtyError, StoreCurrencyError,
)
from core.invoice.functions import calc_extra_fee
from core.invoice_loyalty.service import InvoiceLoyaltyService
from core.loyalty.incust_client_adapter import run_with_incust_terminal_api
from core.payment.funcs import get_or_create_full_bonuses_payment_settings
from core.store.depends import get_current_brand
from core.store.functions.cart import convert_cart_to_order_products
from core.store.functions.order import order_to_schema
from core.store.order.service import change_store_order_status
from core.user.functions import create_guest_user
from db import crud
from db.models import (
    Brand, BrandCustomSettings, Customer, Group, Invoice, LoyaltySettings,
    ShipmentPrice, Store,
    StoreCart,
    StoreOrder, StoreProduct, User,
)
from loggers import JSONLogger
from schemas import (
    CreateOrderSchema, OrderFieldSettingEnum, OrderSchema, OrderShippingStatusEnum,
)
from utils.date_time import utcnow
from .exceptions import *
from .functions import (
    validate_billing_address, validate_order_personal_data,
    validate_order_shipment_methods,
)
from ...functions import get_cart_from_token

handle_error = UnknownErrorHandler(CreateOrderUnknownError, ErrorWithTextVariable)


async def validate_prohibit_redeeming_bonuses(incust_check: Check, loyalty_settings: LoyaltySettings):
    """Валідує чи дозволено списання бонусів через налаштування лояльності."""
    if loyalty_settings and loyalty_settings.prohibit_redeeming_bonuses:
        if incust_check.bonuses_redeemed_amount:
            from core.incust.exceptions import IncustProhibitRedeemingBonusesError
            raise IncustProhibitRedeemingBonusesError()


async def validate_prohibit_redeeming_coupons(incust_check: Check, loyalty_settings: LoyaltySettings):
    """Валідує чи дозволено списання купонів через налаштування лояльності.""" 
    if loyalty_settings and loyalty_settings.prohibit_redeeming_coupons:
        if incust_check.redeemed_coupons:
            from core.incust.exceptions import IncustProhibitRedeemingCouponsError
            raise IncustProhibitRedeemingCouponsError()


class CreateOrderService:
    def __init__(
            self,
            data: CreateOrderSchema,
            brand: Brand = Depends(get_current_brand),
            user: User | None = Security(get_user_optional),
            token_data: dict = Depends(get_user_or_token_data_safe),
            lang: str = Depends(get_lang),
    ):
        self.data = data
        self.brand = brand
        self.user = user
        self.token_data = token_data
        self.store_id: int = data.store_id
        self.lang = lang
        self.store: Store | None = None
        self.brand: Brand | None = brand
        self.group: Group | None = None
        self.store_order: StoreOrder | None = None
        self.is_full_bonuses_payment: bool | None = None
        self.currency: str | None = None

        self.cart: StoreCart | None = None

        self.shipment_method: BrandCustomSettings | None = None

        self.logger = JSONLogger(
            "CreateOrderService",
            {
                "data": self.data,
                "brand": self.brand,
                "user": self.user,
                "token_data": self.token_data,
                "lang": self.lang,
            }
        )

    async def _set_user(self):
        if not self.user:
            if (
                    self.data.email and
                    not await User.is_exists(
                        email=self.data.email, is_guest_user=False
                    )
            ):
                self.user = await create_guest_user(
                    self.data.email,
                    self.data.first_name,
                    self.data.last_name,
                    self.lang,
                    is_accepted_agreement=self.data.is_accepted_agreement,
                )
            else:
                self.user = await get_anonymous_user()

        self.logger.add_data({"user": self.user})

    async def validate_user_and_set(self):
        if not self.data.is_accepted_agreement and not (
                self.user and self.user.is_accepted_agreement):
            raise CreateOrderAgreementIsNotAcceptedError()

        await self._set_user()

        if not self.user.is_accepted_agreement:
            await self.user.set_agreement(True)

        customer = await Customer.get(user_id=self.user.id, profile_id=self.group.id)
        marketing_cons = False if self.data.marketing_consent is None else (
            self.data.marketing_consent)
        if not customer:
            customer = await crud.create_customer(
                self.lang, self.user.id, self.brand.group_id,
                marketing_cons, True
            )
        else:
            if self.data.marketing_consent is not None:
                marketing_cons = self.data.marketing_consent
            else:
                marketing_cons = customer.marketing_consent

            await customer.update(
                marketing_consent=marketing_cons,
                is_accept_agreement=True,
                updated_date=utcnow()
            )

        self.logger.add_data({"customer": customer})

    async def validate_order_type(self):
        product = None
        if self.data.type in ("gift", "topup"):
            if not self.data.products or len(self.data.products) > 1:
                raise CreateOrderTypeProductsQtyError(types="gift, topup")
            product = await StoreProduct.get(self.data.products[0].product_id)
            if not product:
                raise CreateOrderProductWithTypeNotFoundError()

        if self.data.type == "gift":
            if product.type != "gift":
                raise CreateOrderGiftProductsTypeError()

            order_product = self.data.products[0]
            if (
                    order_product and
                    order_product.floating_sum and
                    not product.floating_sum_enabled
            ):
                raise CreateOrderGiftFloatingSumError()
        elif self.data.type == "topup":
            if product.type != "topup":
                raise CreateOrderTopupProductsTypeError()
            order_product = self.data.products[0]
            if not order_product.incust_account:
                raise CreateOrderNoIncustDataForTopupError()

            loyalty_settings = await LoyaltySettings.get_loyalty_settings_for_context(
                brand_id=self.brand.id,
                product_id=product.id,
                store_id=self.store_id,
                group_id=self.brand.group_id,
            )
            
            if not loyalty_settings:
                raise CreateOrderNoIncustPayConfigForTopupError()

            # Перевіряємо відповідність рахунку, якщо у продукту є topup_account_id
            if (hasattr(product, 'topup_account_id') and product.topup_account_id and 
                product.topup_account_id != order_product.incust_account.id):
                raise CreateOrderNotMatchAccountForTopupError()

            from incust_terminal_api_client.api.business_api import BusinessApi
            
            special_accounts = await run_with_incust_terminal_api(
                loyalty_settings,
                BusinessApi.incust_controllers_term_api_special_accounts,
                scope="retail"
            )
            
            if not any(
                    account.id == order_product.incust_account.id for account in
                    special_accounts
            ):
                raise CreateOrderNotFoundSpecialAccountForTopupError()

            group = await Group.get(self.brand.group_id)
            owner = await User.get_by_id(group.owner_id)
            amount = round(
                order_product.floating_sum / 100, 2
            ) if order_product.floating_sum else (round(
                (product.price *
                 order_product.quantity) / 100, 2
            ))
            await validate_special_account_exist(
                self.brand, self.lang, loyalty_settings.terminal_api_key,
                loyalty_settings.server_url,
                order_product.incust_account.id, amount,
                owner, is_owner=True,
            )

    async def validate_need_auth_products_in_cart(self):
        for product in self.data.products:
            db_product = await StoreProduct.get(product.product_id)
            if db_product.need_auth:
                raise CreateOrderNeedAuthProductsInCartError()

    async def validate_data(self):
        self.store = await Store.get(self.store_id)
        self.group = await Group.get(self.brand.group_id)

        self.logger.add_data(
            {
                "store": self.store,
                "brand": self.brand,
                "group": self.group,
            }
        )

        self.currency = await self.check_currency()
        await check_billing_transaction_limit(self.group.id, self.currency)

        await self.validate_user_and_set()
        await self.validate_order_type()

        self.logger.add_data({"currency": self.currency})

        self.logger.debug("Detecting products")

        is_guest = False
        if not self.data.products:
            if (
                    self.token_data and
                    isinstance(self.token_data, dict) and
                    (
                            self.token_data.get("type") == "cart" or
                            self.token_data.get("is_cart", False) is True
                    )
            ):
                self.cart = await get_cart_from_token(self.token_data, self.store_id)
                if not self.user.is_anonymous and self.cart.user_id != self.user.id:
                    await crud.set_user_to_cart(self.cart, self.user.id)
                is_guest = True
            else:
                self.cart = await StoreCart.get_by_user(self.user.id, self.store_id)

            if not self.cart:
                self.logger.error("Cart not found")
                raise CreateOrderCartError()

            self.logger.add_data({"cart": self.cart})
            self.data.products = await convert_cart_to_order_products(self.cart.id)

        if not self.data.products:
            self.logger.error("No products to create order")
            raise CreateOrderNotProductDataError()

        self.logger.debug("Products loaded")

        if is_guest:
            await self.validate_need_auth_products_in_cart()

        start_time = time.time()
        if not_available_products := await check_products_is_available(
                [product.product_id for product in self.data.products],
                self.brand.id, self.store_id,
                self.store.external_type,
                self.store.external_id
        ):
            raise CreateOrderNotInStockDataError(not_available_products)
        self.logger.debug(f"check_product_in_stock: {time.time() - start_time}")

        self.shipment_method = \
            await validate_order_shipment_methods(self.store, self.lang, self.data)

        await validate_order_personal_data(
            self.data, self.shipment_method, self.user, self.brand
        )

        billing_address = self.data.billing_address
        await validate_billing_address(self.store, billing_address)

        if (not self.data.comment and self.brand.order_comment_mode ==
                OrderFieldSettingEnum.REQUIRED.value):
            raise CreateOrderCommentRequiredError()

    async def create_order(self):
        shipment_price = None
        if self.data.price:
            shipment_price = await ShipmentPrice.get(self.data.price.id)

        if not self.data.email:
            if self.user:
                self.data.email = self.user.email

        self.store_order, billing_address, shipment = await crud.create_order_object(
            self.user, self.store, self.data, shipment_price,
            self.shipment_method, timezone=self.group.timezone,
            cart_id=self.cart.id if self.cart else None,
        )
        self.is_full_bonuses_payment = False

        # Обробку лояльності перенесено після створення Invoice

        extra_fee_result = await calc_extra_fee(
            self.group.id,
            self.store_order.total_sum,
            self.currency,
            None,
            self.store_order.id,
            is_topup=True if self.store_order and self.store_order.type == "topup"
            else False,
        )
        if extra_fee_result and extra_fee_result.total_extra_fee:
            self.store_order.sum_to_pay = (
                    self.store_order.total_sum + extra_fee_result.total_extra_fee +
                    self.store_order.tips_sum
            )
            self.store_order.total_sum_with_extra_fee = (
                    self.store_order.total_sum +
                    extra_fee_result.total_extra_fee
            )
        else:
            self.store_order.sum_to_pay = (self.store_order.total_sum +
                                           self.store_order.tips_sum)
            self.store_order.total_sum_with_extra_fee = self.store_order.total_sum

        if (self.is_full_bonuses_payment and self.store_order.total_sum_with_extra_fee
                != 0):
            self.is_full_bonuses_payment = False

        self.store_order = await crud.save_store_order(
            self.store_order, billing_address, shipment
        )

        if not self.store_order:
            raise CreateOrderCreateError()

        if extra_fee_result:
            await crud.add_extra_fee_to_journal(
                extra_fee_result.journal_entries, order_id=self.store_order.id
            )

        # Створюємо Invoice для замовлення одразу після збереження Order
        # щоб лояльність працювала правильно
        if not self.store_order.invoice_id:
            from core.store.functions.order import make_invoice_for_order
            bot = await crud.get_bot_by_brand(self.brand.id)
            await make_invoice_for_order(self.store_order, self.lang, bot)

        # Тепер обробляємо лояльність через Invoice
        start_incust = time.time()
        try:
            if self.data.incust_check:
                # Конвертуємо стару схему в оригінальну схему Check з нових клієнтів
                from core.incust.schema_converter import convert_payload_to_check
                incust_check_converted = convert_payload_to_check(self.data.incust_check, self.user)
                
                # Отримуємо налаштування лояльності для замовлення
                loyalty_settings = await LoyaltySettings.get_loyalty_settings_for_context(
                    brand_id=self.brand.id,
                    store_id=self.store_id,
                    group_id=self.brand.group_id,
                )

                if loyalty_settings:
                    init_loyalty_time = time.time()

                    if self.store_order.type == "regular":
                        await validate_prohibit_redeeming_bonuses(
                            incust_check_converted, loyalty_settings
                        )
                        await validate_prohibit_redeeming_coupons(
                            incust_check_converted, loyalty_settings,
                        )

                    prohibitions_validations_time = time.time()
                    self.logger.debug(
                        "loyalty_prohibitions_validations_time",
                        str(prohibitions_validations_time - init_loyalty_time)
                    )

                    # Резервуємо лояльність через InvoiceLoyaltyService
                    if self.store_order.invoice_id:
                        invoice = await Invoice.get(self.store_order.invoice_id)
                        if invoice:
                            invoice_loyalty_service = InvoiceLoyaltyService()
                            transaction_id = await invoice_loyalty_service.reserve_invoice_loyalty(
                                invoice, loyalty_settings, self.data.incust_check.dict()
                            )

                            open_transaction_time = time.time()
                            self.logger.debug(
                                "loyalty_open_transaction_time",
                                str(open_transaction_time - prohibitions_validations_time)
                            )

                            if self.data.incust_check.amount_to_pay == 0:
                                self.is_full_bonuses_payment = True

                            if not transaction_id:
                                self.logger.error("Failed to reserve loyalty transaction")
                                # Не кидаємо помилку, дозволяємо створити замовлення без лояльності
                        else:
                            self.logger.error(f"Invoice {self.store_order.invoice_id} not found")
                    else:
                        self.logger.error("No invoice created for order with loyalty")
                else:
                    self.logger.debug(f"No loyalty settings found for order")
            else:
                # Перевіряємо чи потрібна лояльність для цього бренду
                loyalty_settings = await LoyaltySettings.get_loyalty_settings_for_context(
                    brand_id=self.brand.id,
                    store_id=self.store_id,
                    group_id=self.brand.group_id,
                )

                if (loyalty_settings and self.store_order.type == "regular"
                    and not self.user.is_guest_user and not self.user.is_anonymous):
                    self.logger.error(f"*** missing_incust_check")
                    raise CreateOrderNoIncustCheckForIncustBrandError(
                        "missing_incust_check"
                    )
        except BaseInvoiceError as err:
            raise err
        except Exception as err:
            # Помилки лояльності
            self.logger.error(f"Loyalty processing error: {err}", exc_info=True)
            raise CreateOrderLoyaltyError("loyalty_processing_failed")
        finally:
            end_incust = time.time()
            self.logger.debug(
                "create_order", "process_incust", str(end_incust - start_incust)
            )

    async def export_order(self, is_full_bonuses_payment: bool | None = None) -> None:
        if self.store.external_type not in (
                ExternalType.POSTER.value, ExternalType.GET_ORDER.value):
            return
        if self.store_order.payment_method == 'online' and not is_full_bonuses_payment:
            return

        if self.store.external_type == ExternalType.GET_ORDER.value:
            await send_order_to_get_order(self.store_order, self.store)
        elif self.store.external_type == ExternalType.POSTER.value:
            await send_order_to_poster(self.store_order, self.store)

    async def end_process(self, start_main_time: time):
        if self.is_full_bonuses_payment:
            start_time = time.time()
            self.store_order = await process_store_order_full_bonuses_payment(
                self.store_order, self.lang, self.brand.id,
            )
            self.logger.debug(
                f"process_store_order_full_bonuses_payment: {time.time() - start_time}"
            )

        if self.store_order.type == "regular" and self.is_full_bonuses_payment:
            bonuses_payment = await get_or_create_full_bonuses_payment_settings(
                self.brand.id
            )
            if bonuses_payment:
                await crud.create_store_order_payment(
                    order_id=self.store_order.id,
                    payment_method="bonuses",
                    name=bonuses_payment.name,
                    payment_settings_id=bonuses_payment.id,
                )
            order_status = OrderShippingStatusEnum.PAYED.value
        else:
            order_status = OrderShippingStatusEnum.CLOSED.value if (
                    self.store_order.type == "gift" or
                    (self.is_full_bonuses_payment
                     and self.store_order.type != "regular")) \
                else OrderShippingStatusEnum.NEW.value

        if not order_status == OrderShippingStatusEnum.NEW.value:
            start_time = time.time()
            await change_store_order_status(
                self.store_order,
                order_status,
                "user",
                None,
                "order_api",
                initiated_by_user=self.user,
                is_full_bonuses_payment=self.is_full_bonuses_payment,
            )
            self.logger.debug(f"change_store_order_status: {time.time() - start_time}")

            start_time = time.time()
            await self.export_order(
                is_full_bonuses_payment=self.is_full_bonuses_payment
            )
            self.logger.debug(f"export_order: {time.time() - start_time}")

            self.logger.debug(f"total time: {time.time() - start_main_time}")
        else:
            await self.store_order.update(_status=OrderShippingStatusEnum.NEW.value)

    @handle_error
    async def process(self) -> OrderSchema:
        self.logger.debug("Started")
        start_main_time = time.time()

        start_time = time.time()
        await self.validate_data()
        self.logger.debug(
            "Data validated", {
                "time": time.time() - start_time,
            }
        )

        start_time = time.time()
        await self.create_order()
        self.logger.debug(f"create_order: {time.time() - start_time}")

        start_time = time.time()
        order_schema = await order_to_schema(
            self.store_order, self.lang, with_token=True
        )
        self.logger.debug(f"order_to_schema: {time.time() - start_time}")

        if order_schema.token:
            await crud.save_store_order_token(self.store_order.id, order_schema.token)

        await self.end_process(start_main_time)

        return order_schema

    async def check_currency(self):
        if not self.store.currency:
            raise StoreCurrencyError(self.store_id)

        return self.store.currency
