import logging
import re
from dataclasses import dataclass

from fastapi import HTT<PERSON>Exception
from incust_client_api_client.api.loyalty_api import LoyaltyApi
from starlette.requests import Request

from config import P4S_API_URL
from core.incust.exceptions import IncustError
from core.loyalty import run_with_incust_client_api
from db.models import Brand, Group, InvoiceTemplate, Store, StoreProduct
from db.models.store.loyalty_settings import LoyaltySettings
from utils.text import f

DEFAULT_IMAGE = f"{P4S_API_URL}/static/images/7loc-com-white-square-preview.jpg"
DEFAULT_TITLE = "7loc | AI-powered mobile ordering"
DEFAULT_ICON = f"{P4S_API_URL}/static/images/7loc-favicon-1024.png"


@dataclass
class MetaTags:
    image: str
    title: str
    description: str
    icon: str


async def calculate_for_shop(
        request: Request,
        origin_url_path: str,
):
    match = re.match(r"^/s/(\d+)", origin_url_path)
    if match:
        store_id = int(match.group(1))
        store = await Store.get(store_id)
    else:
        store = None

    product: StoreProduct | None = None
    invoice_template: InvoiceTemplate | None = None

    if store and (product_id := request.query_params.get("product_id")):
        product = await StoreProduct.get(product_id)
    elif match := re.match(r"^/fastpay/(\d+)", origin_url_path):
        invoice_template_id = int(match.group(1))
        invoice_template = await InvoiceTemplate.get(invoice_template_id)

    image = await store.image_media_url if store else None

    if product:
        if product_image := await product.media_url:
            image = product_image

        title = product.name
        description = product.description
    elif invoice_template:
        if invoice_template.photo_url:
            image = invoice_template.photo_url
        title = invoice_template.title
        description = invoice_template.description
    elif store:
        title = store.name
        description = store.description
    else:
        title = None
        description = None

    return image, title, description


async def calculate_for_referral(
        brand: Brand,
        request: Request,
        origin_url_path: str,
):
    try:
        match = re.match(r"^/s/(\d+)", origin_url_path)
        if match:
            store_id = int(match.group(1))
        else:
            store_id = None

        group = await Group.get(brand.group_id)
        lang = group.lang

        loyalty_settings = await LoyaltySettings.get_loyalty_settings_for_context(
            brand_id=brand.id,
            store_id=store_id
        )
        if not loyalty_settings:
            raise HTTPException(404, "Loyalty settings not found")

        loyalty_data = await run_with_incust_client_api(
            loyalty_settings,
            None,
            LoyaltyApi.incust_controllers_client_client_loyalty,
            loyalty_id=loyalty_settings.loyalty_id,
            lang=lang
        )

        image = None
        if loyalty_data and hasattr(loyalty_data, "referral_program") and loyalty_data.referral_program:
            if getattr(loyalty_data.referral_program, "referral_logo", None):
                image = loyalty_data.referral_program.referral_logo

        if not image:
            image = DEFAULT_IMAGE

        title = await f("incust loyalty accept invitation header", lang)
        if loyalty_data and hasattr(loyalty_data, "referral_program") and loyalty_data.referral_program:
            if getattr(loyalty_data.referral_program, "referral_title", None):
                title = loyalty_data.referral_program.referral_title

        description = await f("incust loyalty accept invitation description", lang)
        if loyalty_data and hasattr(loyalty_data, "referral_program") and loyalty_data.referral_program:
            if getattr(loyalty_data.referral_program, "referral_description", None):
                description = loyalty_data.referral_program.referral_description

        return image, title, description

    except IncustError as ex:
        logging.error(ex, exc_info=True)
        return await calculate_for_shop(request, origin_url_path)


async def calculate_meta_tags(
        request: Request,
        brand: Brand | None = None,
):
    icon = None

    origin_url_path: str = request.headers.get("x-custom-url-path") or ""

    if "/share_and_earn" in origin_url_path and brand:
        image, title, description = await calculate_for_referral(
            brand, request, origin_url_path
        )
    else:
        image, title, description = await calculate_for_shop(request, origin_url_path)

    if brand:
        if not title:
            title = brand.name

        if not image and (image_url := await brand.image_url):
            image = image_url
        if logo_url := await brand.logo_url:
            icon = logo_url

    if image is None:
        image = DEFAULT_IMAGE
    if title is None:
        title = DEFAULT_TITLE
    if description is None:
        description = ""
    if icon is None:
        icon = DEFAULT_ICON

    return MetaTags(image, title, description, icon)
