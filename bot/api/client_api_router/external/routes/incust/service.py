import asyncio
import logging
import uuid

from incust_client_api_client.models.news import News
from itsdangerous import BadTimeSignature, SignatureExpired

from config import INCUST_SERVER_API
from core.incust.message_sender import IncustMessageSender
from core.incust.message_sender.schemas import InCustMessage
from core.invoice_loyalty.coupons_service import InvoiceLoyaltyCouponsService
from core.loyalty import run_with_incust_client_api
from core.whatsapp.functions import send_delayed_wa_menu
from core.whatsapp.keyboards import get_wa_menu_keyboard
from db.models import Brand, ClientBot, Group, LoyaltySettings, User
from utils.platform_admins import send_message_to_platform_admins
from .funcs import (
    del_incust_push_token,
    get_customers_from_tokens,
    push_token_validate,
    renew_invalid_push_token,
)

debugger = logging.getLogger('debugger.incust.message')


class IncustMessageProcessor:
    """Processes incoming Incust messages, handling token validation, renewal,
    duplicate removal, and message sending."""

    def __init__(self, data: InCustMessage):
        self.data = data
        self.errors = []
        self.tokens_for_delete = []
        self.tokens_for_delete_with_data = []  # Токени для видалення з додатковими даними
        self.tokens_for_renewal = []
        self.valid_users = []
        self.final_user_data = []  # Зберігає всю необхідну інформацію для відправки повідомлень
        self.processed_key_tracker = set()  # Для відстеження вже оброблених комбінацій (user_id, brand_id)

    async def process_message(self) -> dict:
        """Основний метод для обробки повідомлень Incust."""
        try:
            debugger.info(f"Початок обробки повідомлень Incust. Кількість токенів: {len(self.data.to)}")
            
            # Валідація та класифікація токенів
            await self._validate_and_classify_tokens()
            
            # Оновлення токенів та отримання інформації користувачів
            # await self._prepare_user_data()

            # Отримання необхідних даних для відправки повідомлень (за один запит)
            if self.valid_users:
                await self._fetch_and_validate_user_data()
            
                # Відправка повідомлень користувачам
                await self._send_messages_to_users()

        except Exception as ex:
            error_msg = f"Помилка обробки повідомлень: {ex}"
            debugger.error(error_msg, exc_info=True)
            self.errors.append(error_msg)

        finally:
            # Видалення невалідних токенів
            await self._cleanup_invalid_tokens()
            
            return self._build_response()

    async def _validate_and_classify_tokens(self):
        """Валідує токени та класифікує їх як валідні або для оновлення/видалення."""
        customers = await get_customers_from_tokens(self.data.to)
        customers_dict = {(c.user_id, c.brand_id): c for c in customers}
        
        for token in self.data.to:
            try:
                result = await push_token_validate(token)
                if result and result.get("user_id") and result.get("brand_id"):
                    # Валідний токен
                    key = (result["user_id"], result["brand_id"])

                    if key not in self.processed_key_tracker:
                        self.processed_key_tracker.add(key)
                        self.valid_users.append({**result, "push_token": token})
                        debugger.debug(f"Валідний токен: {token[:10]}... для користувача {result.get('user_id')}, brand_id={result.get('brand_id')}")

                    customer = customers_dict.get(key)
                    debugger.debug(f"key: {key}, customer: {customer}")

                    if customer:
                        # Якщо токен відповідає запису в БД - залишаємо його
                        if customer.push_token == token:
                            debugger.debug(f"Валідний токен, що відповідає БД: {token[:10]}... для користувача {result.get('user_id')}, brand_id={result.get('brand_id')}")
                        else:
                            # Якщо токен відрізняється від запису в БД - видаляємо його
                            self.tokens_for_delete.append(token)
                            debugger.debug(f"Токен відрізняється від запису в БД: {token[:10]}... для користувача {result.get('user_id')}, brand_id={result.get('brand_id')}")
                    else:
                        # Якщо немає запису в БД - видаляємо токен
                        self.tokens_for_delete.append(token)
                        debugger.debug(f"Немає запису в БД: {token[:10]}... для користувача {result.get('user_id')}, brand_id={result.get('brand_id')}")
                else:
                    # Потрібне оновлення
                    self.tokens_for_renewal.append(token)
                    debugger.debug(f"Токен потребує оновлення: {token[:10]}...")
            except (SignatureExpired, BadTimeSignature):
                # Термін дії токена минув або підпис недійсний
                self.tokens_for_renewal.append(token)
                debugger.debug(f"Термін дії токена минув або підпис недійсний: {token[:10]}...")
            except Exception as e:
                # Інші помилки валідації
                debugger.error(f"Помилка валідації токена {token[:10]}...: {e}", exc_info=True)
                self.tokens_for_delete.append(token)

    async def _prepare_user_data(self):
        """Підготовка даних користувачів, включаючи оновлення токенів."""
        if not self.tokens_for_renewal and not self.valid_users:
            debugger.warning("Немає валідних або токенів для оновлення")
            return

        # Оновлення невалідних токенів
        if self.tokens_for_renewal:
            customers_for_renewal = await get_customers_from_tokens(self.tokens_for_renewal)
            debugger.info(f"Знайдено {len(customers_for_renewal)} IncustCustomer для оновлення токенів")

            # Унікальні записи для оновлення
            unique_customers = {}
            for customer in customers_for_renewal:
                key = (customer.user_id, customer.brand_id)
                # Перевірка чи користувач не вже валідний
                if key not in self.processed_key_tracker:
                    unique_customers[key] = customer

            # Оновлення і додавання до валідних
            for customer in unique_customers.values():
                brand = await Brand.get(customer.brand_id)

                loyalty_settings = await LoyaltySettings.get_loyalty_settings_for_context(
                    brand_id=brand.id
                )
                
                if not loyalty_settings:
                    debugger.warning(f"Налаштування лояльності не знайдені для бренду {brand.id}")

                    self.tokens_for_delete_with_data.append((customer.push_token, {
                        "brand_id": customer.brand_id,
                        "user_id": customer.user_id,
                        "white_label_id": customer.push_token_wl_id,
                        "api_url": loyalty_settings.server_url if loyalty_settings else INCUST_SERVER_API,
                        "user_token": customer.token,
                        "version": 1,
                    }))
                    continue
                
                try:
                    renewed_user = await renew_invalid_push_token(customer)
                    self.valid_users.append(renewed_user)
                    self.processed_key_tracker.add((customer.user_id, customer.brand_id))
                    debugger.info(f"Успішно оновлено токен для користувача {customer.user_id} та бренду {customer.brand_id}")
                except Exception as e:
                    debugger.error(f"Помилка оновлення токена: {e}", exc_info=True)
                    self.errors.append(str(e))

                    # Отримуємо налаштування лояльності для збереження правильного API URL
                    brand_loyalty_settings = await LoyaltySettings.get_loyalty_settings_for_context(
                        brand_id=customer.brand_id
                    )

                    self.tokens_for_delete_with_data.append((customer.push_token, {
                        "brand_id": customer.brand_id,
                        "user_id": customer.user_id,
                        "white_label_id": customer.push_token_wl_id,
                        "api_url": brand_loyalty_settings.server_url if brand_loyalty_settings else "https://api.incust.com",
                        "user_token": customer.token,
                        "version": 1,
                    }))

        # Отримання необхідних даних для відправки повідомлень (за один запит)
        if self.valid_users:
            await self._fetch_and_validate_user_data()

    async def _fetch_and_validate_user_data(self):
        """Отримання та валідація всіх необхідних даних для відправки повідомлень за мінімальну кількість запитів."""
        user_ids = list(set([user_data["user_id"] for user_data in self.valid_users]))
        brand_ids = list(set([user_data["brand_id"] for user_data in self.valid_users]))

        users = {user.id: user for user in await User.get_by_ids(user_ids)}
        list_brands = await Brand.get_by_ids(brand_ids)
        brands = {brand.id: brand for brand in list_brands}
        
        # Отримуємо групи для усіх брендів
        group_ids = list(set([brand.group_id for brand in brands.values() if brand.group_id]))
        groups = {group.id: group for group in await Group.get_by_ids(group_ids, status="enabled")}

        group_bots = {bot.group_id: bot for bot in
                  await ClientBot.get_by_in_field("group_id", group_ids, status="enabled",
                      # is_started=True
                  )
                      }

        debugger.info(f"Отримано дані: {len(users)} користувачів, {len(brands)} брендів, {len(group_bots)} ботів")

        # Валідація та підготовка даних для відправки
        for user_data in self.valid_users:
            user_id, brand_id = user_data["user_id"], user_data["brand_id"]
            user = users.get(user_id)
            brand = brands.get(brand_id)
            
            # Перевірка валідності користувача та бренду
            if not user or user.status == "d":
                debugger.warning(f"Користувач {user_id} не знайдений або деактивований")
                self.tokens_for_delete.append(user_data["push_token"])
                continue
                
            if not brand:
                debugger.warning(f"Бренд {brand_id} не знайдений")
                self.tokens_for_delete.append(user_data["push_token"])
                continue
                
            # Перевірка наявності налаштувань лояльності
            loyalty_settings = await LoyaltySettings.get_loyalty_settings_for_context(
                brand_id=brand.id
            )
            
            if not loyalty_settings:
                debugger.warning(f"Налаштування лояльності не знайдені для бренду {brand_id}")
                self.errors.append(f"brand not connected to loyalty system. {brand.id=}, {user.id=}, {user.full_name=}")
                self.tokens_for_delete.append(user_data["push_token"])
                continue
                
            # Перевірка групи
            group = groups.get(brand.group_id)
            if not group:
                debugger.warning(f"Група {brand.group_id} не знайдена або не активна")
                self.tokens_for_delete.append(user_data["push_token"])
                continue
                
            # Перевірка бота
            group_bot = group_bots.get(group.id)
            
            # Перевірка наявності каналу зв'язку
            if not any([user.chat_id, user.wa_phone, user.email]):
                self.errors.append(
                    f"User {user.id=}, {user.full_name=} not have any chanel for send.\n{user.chat_id=},"
                    f" {user.wa_phone=}, {user.email=}"
                )
                debugger.warning(f"Користувач {user.id} не має жодного каналу для надсилання повідомлень")
                self.tokens_for_delete.append(user_data["push_token"])
                continue
                
            # Додавання валідних даних для відправки (включаючи loyalty_settings)
            self.final_user_data.append((user_data, user, brand, group, group_bot, loyalty_settings))

    async def _send_messages_to_users(self):
        """Відправка повідомлень користувачам."""
        if not self.final_user_data:
            debugger.warning("Немає валідних користувачів для відправки повідомлень")
            return
            
        debugger.info(f"Відправка повідомлень для {len(self.final_user_data)} користувачів")

        wa_menu_tasks = []
        
        for user_data, user, brand, group, group_bot, loyalty_settings in self.final_user_data:
            try:
                lang = await user.get_lang(group_bot.id if group_bot else None)
                sender = IncustMessageSender(brand, group_bot, user, lang)

                is_need_wa_menu = bool(group_bot and group_bot.bot_type == "whatsapp" and user.wa_phone)
                is_wa_menu_sent = None
                wa_keyboard = None

                if is_need_wa_menu:
                    wa_keyboard = await get_wa_menu_keyboard(user, group_bot, lang)

                if self.data.data.ic_type == "coupon":
                    # Використовуємо новий сервіс для обробки купонів
                    coupon = await self._get_coupon_with_new_service(
                        self.data.data.ic_id, user, brand, lang, loyalty_settings
                    )
                    if coupon:
                        await sender.send_coupon(coupon)
                        debugger.info(f"Відправлено купон {self.data.data.ic_id} користувачу {user.id}")
                    else:
                        debugger.error(f"Не вдалося отримати купон {self.data.data.ic_id} для користувача {user.id}")

                elif self.data.data.ic_type == "news":
                    news = await self._get_news_with_new_service(
                        self.data.data.ic_id, user, brand, lang, loyalty_settings
                    )
                    if news:
                        news.description_extended = news.description_extended.replace("\n", "<br>")
                        coupons_for_show = await self._get_news_coupons_new(user, brand, lang, loyalty_settings, news)

                        is_wa_menu_sent = await sender.send_notification(
                            news, coupons=coupons_for_show, wa_keyboard=wa_keyboard if
                            wa_keyboard and not coupons_for_show else None
                        )
                        debugger.info(f"Відправлено новину {self.data.data.ic_id} користувачу {user.id}")

                        for coupon_for_show in coupons_for_show:
                            await sender.send_coupon(coupon_for_show, True)
                    else:
                        debugger.error(f"Не вдалося отримати новину {self.data.data.ic_id} для користувача {user.id}")
                else:
                    self.data.notification.body = self.data.notification.body.replace("\n", "<br>")
                    is_wa_menu_sent = await sender.send_notification(self.data.notification,
                                                                      wa_keyboard=wa_keyboard)
                    debugger.info(f"Відправлено повідомлення користувачу {user.id}")

                if is_need_wa_menu and wa_keyboard and not is_wa_menu_sent:
                    to = user.wa_phone
                    bot_token = group_bot.token
                    whatsapp_from = group_bot.whatsapp_from

                    # Додаємо завдання до списку для масового запуску
                    wa_menu_tasks.append(
                        send_delayed_wa_menu(bot_token, lang, to, wa_keyboard, whatsapp_from, 4)
                    )
                    debugger.info(f"Заплановано відправку WA меню для користувача {user.id}")

            except Exception as ex:
                err_message = f"Помилка відправки повідомлення користувачу {user.id}: {ex}"
                debugger.error(err_message, exc_info=True)
                self.errors.append(err_message)

        if wa_menu_tasks:
            debugger.info(f"Запуск {len(wa_menu_tasks)} завдань відправки WA меню")
            await asyncio.gather(*wa_menu_tasks, return_exceptions=True)

    async def _cleanup_invalid_tokens(self):
        """Видалення невалідних токенів з Incust API."""
        return
        delete_tasks = []

        if self.tokens_for_delete:
            for token in self.tokens_for_delete:
                try:
                    result_token = await push_token_validate(token)
                    if not result_token:
                        continue
                        
                    task = del_incust_push_token(token, result_token)
                    delete_tasks.append(task)
                except Exception as e:
                    debugger.error(f"Помилка декодування токена {token[:10]}...: {str(e)}")

        if self.tokens_for_delete_with_data:
            for token, data in self.tokens_for_delete_with_data:
                task = del_incust_push_token(token, data)
                delete_tasks.append(task)

        if delete_tasks:
            try:
                tasks = [asyncio.create_task(task) for task in delete_tasks]

                results = await asyncio.gather(*tasks, return_exceptions=True)

                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        debugger.error(f"Помилка видалення токена {i}: {str(result)}")
                        
            except Exception as e:
                debugger.error(f"Помилка при виконанні завдань видалення: {str(e)}", exc_info=True)

    async def _get_coupon_with_new_service(self, coupon_id: str, user: User, brand: Brand, lang: str, loyalty_settings: LoyaltySettings):
        """Отримання купона через новий сервіс лояльності з PDF збереженням."""
        try:
            if not loyalty_settings:
                debugger.warning(f"Немає налаштувань лояльності для бренду {brand.id}")
                return None
            
            # Використовуємо новий клієнт для отримання купона
            coupons_service = InvoiceLoyaltyCouponsService()

            from incust_client_api_client.api.coupon_api import CouponApi

            coupon_response = await run_with_incust_client_api(
                loyalty_settings,
                user,
                CouponApi.incust_controllers_client_client_coupon_get,
                coupon_id=coupon_id,
                lang=lang
            )
            
            if not coupon_response:
                debugger.warning(f"Не вдалося отримати купон {coupon_id} через новий API")
                return None

            coupon_data = {
                "id": str(coupon_response.id) if coupon_response.id else coupon_id,
                "code": coupon_response.code or coupon_id,
                "title": coupon_response.title or "",
                "description": coupon_response.description or "",
                "type": coupon_response.type_ or "",
                "image": coupon_response.image,
            }
            
            # Викликаємо метод для отримання даних купона з PDF збереженням
            coupon_show_data = await coupons_service._get_coupon_data_for_show(
                brand=brand,
                loyalty_settings=loyalty_settings,
                user=user,
                coupon_data=coupon_data,
                lang=lang,
                need_pdf_file=True,  # Потрібен PDF файл
                base_url=loyalty_settings.server_url,
            )
            return coupon_show_data
                
        except Exception as e:
            debugger.error(f"Помилка отримання купона {coupon_id} через новий сервіс: {e}", exc_info=True)
            return None

    async def _get_news_with_new_service(self, news_id: str, user: User, brand: Brand, lang: str, loyalty_settings: LoyaltySettings):
        """Отримання новини через новий сервіс лояльності."""
        try:
            from incust_client_api_client.api.news_api import NewsApi
            
            if not loyalty_settings:
                debugger.warning(f"Немає налаштувань лояльності для бренду {brand.id}")
                return None
            
            news_response = await run_with_incust_client_api(
                loyalty_settings,
                user,
                NewsApi.incust_controllers_client_client_news,
                news_id=news_id,
                lang=lang
            )
            
            return news_response
                
        except Exception as e:
            debugger.error(f"Помилка отримання новини {news_id} через новий сервіс: {e}", exc_info=True)
            return None

    async def _get_news_coupons_new(self, user: User, brand: Brand, lang: str, loyalty_settings: LoyaltySettings, news: News):
        """Отримання купонів для новин через новий сервіс."""
        coupons_for_show = []
        coupons_service = InvoiceLoyaltyCouponsService()
        
        try:
            
            # Отримуємо coupon_batches з News об'єкта
            coupon_batches = news.coupon_batches if news.coupon_batches is not None else []
            
            for coupon_batch in coupon_batches:
                try:
                    # Формуємо дані купона з batch з перевіркою None
                    coupon_data = {
                        "code": coupon_batch.code if coupon_batch.code is not None else None,
                        "title": coupon_batch.title if coupon_batch.title is not None else None,
                        "description": coupon_batch.description if coupon_batch.description is not None else None,
                        "type": coupon_batch.type_.value if coupon_batch.type_ is not None and coupon_batch.type_ else None,
                        "image": coupon_batch.image if coupon_batch.image is not None else None,
                    }
                    
                    # Отримуємо дані купона для відображення
                    coupon_show_data = await coupons_service._get_coupon_data_for_show(
                        brand=brand,
                        loyalty_settings=loyalty_settings,
                        user=user,
                        coupon_data=coupon_data,
                        lang=lang,
                        need_pdf_file=True,
                        base_url=loyalty_settings.server_url,
                    )
                    
                    if coupon_show_data:
                        coupons_for_show.append(coupon_show_data)
                        
                except Exception as e:
                    batch_code = coupon_batch.code if coupon_batch.code is not None else "unknown"
                    debugger.error(f"Помилка обробки купона {batch_code}: {e}", exc_info=True)
                    continue
                        
        except Exception as e:
            debugger.error(f"Помилка отримання купонів для новини: {e}", exc_info=True)
        
        return coupons_for_show

    def _build_response(self) -> dict:
        """Формування відповіді на основі статусу обробки."""
        if self.errors:
            error_message_for_admins = "Помилки надсилання повідомлень InCust:\n\n" + ("\n\n".join(self.errors))
            asyncio.create_task(send_message_to_platform_admins(error_message_for_admins))
            error_detail = "Під час обробки повідомлень виникли помилки. Детальні помилки занесені в журнал і надіслані адміністраторам."
            debugger.error(error_detail)
            return {"id": uuid.uuid4().hex, "message": error_detail, "errors": self.errors}

        return {"id": uuid.uuid4().hex, "message": "Повідомлення успішно оброблені та надіслані."}


async def send_message(data: InCustMessage) -> dict:
    """Method to send incust messages to users, uses IncustMessageProcessor class."""
    processor = IncustMessageProcessor(data)
    return await processor.process_message()