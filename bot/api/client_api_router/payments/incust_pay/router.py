import logging

from fastapi import APIRouter, Depends, Query, Security

import schemas
from core.payment.incust_pay.service import IncustPayService

router = APIRouter(
    prefix="/incustPay",
)

debugger = logging.getLogger("debugger")


@router.get("/card/{card_number:path}")
async def get_incust_card_info(
        card_number: str,
        service: IncustPayService = Depends(),
        payment_settings_id: int | None = Query(None),
        product_id: int | None = Query(None),
        store_id: int | None = Query(None),
        object_payment_settings_id: int | None = Query(None),
) -> schemas.IncustPayCardInfo:
    return await service.get_incust_card_info(
        card_number, payment_settings_id, product_id, store_id, object_payment_settings_id
    )


@router.post("/pay")
async def incust_pay_pay(
        data: schemas.IncustPayPayData,
        service: IncustPayService = Security(scopes=["me:write"]),
) -> schemas.IncustPaySuccess:
    return await service.pay(data)
