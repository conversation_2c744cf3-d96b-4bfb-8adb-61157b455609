import logging

from fastapi import (<PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException)
from starlette import status
from starlette.responses import RedirectResponse

import schemas
from config import MESSANGERS_MEDIA_SIZE_LIMITS, MESSANGERS_MEDIA_TYPES
from core.api.depends import get_lang
from core.auth.depend import get_active_user_optional, get_user_or_token_data_safe
from core.bot.ewallet_handlers import process_single_ewallet
from core.invoice.functions import invoice_template_to_schema, invoice_to_schema
from core.media_manager import media_manager
from core.payment.exceptions import (
    PaymentInvoiceNotFoundError, PaymentInvoiceTemplateNotFoundError,
    PaymentSendToFriendError,
    PaymentStoreOrderCanceledError,
    PaymentStoreOrderClosedError, PaymentStoreOrderPayedError,
)
from core.payment.payment_processor.providers.liqpay import get_image_ex
from core.store.depends import get_current_bot_with_brand
from db.models import (
    <PERSON><PERSON><PERSON>ot, EWallet, Invoice, InvoiceTemplate, Payment, StoreOrder,
    User,
)
from schemas import (
    EWalletUserAccountInfo, FriendInvoiceData, InvoiceSchema,
)
from utils.exceptions import ErrorWithHTTPStatus
from . import payment_status, webhooks
from .service import PaymentsService
from .service_ import (set_friend_to_invoice_)
from ..store.functions import get_order_from_token

debugger = logging.getLogger("debugger.payments")
router = APIRouter(tags=['payments'])

router.include_router(payment_status.router)
router.include_router(webhooks.router)


@router.post("/save_payment_method/{object_type}/{object_id}")
async def save_payment_method(
        data: schemas.NewPrePaymentData,
        payments_service: PaymentsService = Depends()
) -> schemas.OkResponse:
    await payments_service.pre_payment(data)
    return schemas.OkResponse()


@router.post("/make_payment/{object_type}/{object_id}")
async def make_payment(
        data: schemas.MakePaymentData,
        payments_service: PaymentsService = Depends()
) -> schemas.PaymentCheckoutProviderData:
    return await payments_service.make_provider_payment(data)


@router.get('/images/payment/{payment_method:path}/{image_path:path}')
async def get_image(payment_method: str, image_path: str):
    return await get_image_ex(payment_method, image_path)


@router.api_route(
    '/client_redirect/{payment_id:int}',
    description='Method for redirect client from callback payment provider',
    methods=["GET", "POST"],
    include_in_schema=False
)
async def client_redirect(
        payment_id: str,
):
    debugger.debug(f"@router.api_route\n{payment_id=}")
    payment = await Payment.get(payment_id)
    if not payment:
        raise HTTPException(
            status.HTTP_404_NOT_FOUND, detail=f"payment {payment_id=} not found"
        )

    if not payment.return_url:
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail=f"return_url for {payment_id=} is empty"
        )

    is_order_created = ""
    debugger.debug(f"{payment.id=} {payment.status=}")
    if payment.status == "payed":
        invoice = await Invoice.get(payment.invoice_id)
        if invoice:
            store_order = await StoreOrder.get(invoice_id=invoice.id)
            if store_order:  # and store_order.status_pay == "payed":
                is_order_created = "&is_order_created=true"

    return RedirectResponse(
        url=payment.return_url + is_order_created, status_code=status.HTTP_303_SEE_OTHER
    )


async def get_ewallet_user_account_info(
        user: User, ewallet_id: int, lang: str
) -> EWalletUserAccountInfo | None:
    ewallet = await EWallet.get(id=ewallet_id, is_enabled=True, is_deleted=False)
    if not ewallet:
        return None
    res = await process_single_ewallet(ewallet, user, lang)
    return EWalletUserAccountInfo(
        title=res.ewallet.name,
        message=res.message,
        used_credit=res.used_credit,
        available_amount=res.available_amount,
        credit_limit=res.credit_limit,
    )


@router.get("/invoiceTemplate/{invoice_template_id}")
async def get_invoice_template_by_id(
        invoice_template_id: int,
        lang: str = Depends(get_lang),
        ewallet_id: int | None = None,
        user: User | None = Depends(get_active_user_optional),
) -> schemas.InvoiceTemplateSchema:
    invoice_template = await InvoiceTemplate.get(invoice_template_id)
    if not invoice_template:
        raise PaymentInvoiceTemplateNotFoundError(invoice_template_id)
    ewallet_user_account_info = None
    if ewallet_id and user:
        ewallet_user_account_info = await get_ewallet_user_account_info(user, ewallet_id, lang)

    return await invoice_template_to_schema(
        invoice_template, lang, ewallet_user_account_info=ewallet_user_account_info
    )


@router.post(
    "/set_friend",
    description="Method to set friend to invoice",
)
async def set_friend_to_invoice(
        friend_data: FriendInvoiceData = Depends(),
        token_data: User | dict | None = Depends(get_user_or_token_data_safe),
        bot: ClientBot | None = Depends(get_current_bot_with_brand),
        lang: str = Depends(get_lang),
) -> InvoiceSchema:

    friend_id = friend_data.friend_id
    friend_comment = friend_data.friend_comment
    order_id = friend_data.order_id
    invoice_id = friend_data.invoice_id

    order = None
    if order_id:
        order = await get_order_from_token(order_id, token_data)

        if order.status == "canceled":
            raise PaymentStoreOrderCanceledError(order.id)

        if order.status == "closed":
            raise PaymentStoreOrderClosedError(order.id)

        if order.status_pay in ("payed", "processing"):
            raise PaymentStoreOrderPayedError(order.id)

        if not order.invoice_id:
            logging.error(f"No order.invoice_id {friend_data = }")
            raise PaymentInvoiceNotFoundError()

        invoice_id = order.invoice_id

    if not invoice_id:
        logging.error(f"No invoice_id for {friend_data = }")
        raise PaymentInvoiceNotFoundError(f"{invoice_id = }, {order_id = }")

    try:
        if friend_data.friend_comment_media:
            friend_comment_media = await media_manager.save_from_upload_file(
                friend_data.friend_comment_media,
                max_size=MESSANGERS_MEDIA_SIZE_LIMITS,
                allowed_types=MESSANGERS_MEDIA_TYPES,
            )
        else:
            friend_comment_media = None

        invoice_ = await Invoice.get(invoice_id)
        invoice_ = await set_friend_to_invoice_(
            invoice_, order, bot, friend_id, friend_comment, friend_comment_media
        )
    except ErrorWithHTTPStatus as ex:
        raise ex
    except Exception as ex:
        logging.error(f"set friend to invoice FAILED. {str(ex)}", exc_info=True)
        raise PaymentSendToFriendError("")

    return await invoice_to_schema(invoice_, lang)
