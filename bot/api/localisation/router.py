import logging
from fastapi import APIRouter, HTTPException, Query, Depends
from psutils.translator.async_translation import hardfix_language_code
from starlette import status

from config import PLATFORM_MAIN_LANGS, PLATFORM_ADD_GOOGLE_LANGS
from core.api.depends import get_lang
from utils.locales import locales
from utils.locales.schemas import GetLocaleResult
from utils.translator import Translator
from utils.text import fd
import schemas
from db.models import ClientBot, Group, Brand
from core.store.depends import get_current_brand, get_current_bot_with_brand

router = APIRouter()


@router.post(
    "/dataSet",
    tags=["localise"],
    response_description="Returns localisation for set of data",
)
async def localise_data_set(
        data: schemas.LocaliseDataSet,
        headers_lang: str = Depends(get_lang),
) -> dict[str, dict[str, str]]:
    if not data.lang:
        data.lang = headers_lang

    if not data.data:
        return {}

    result = {}

    for key, variables in data.data.items():
        result[key] = await fd(variables, data.lang)

    return result


@router.post(
    "/list",
    tags=["localise"],
    response_description="Method to get localisation list by list of variables",
    description="Returns localisation list",
)
async def localise_list(
        data: schemas.LocaliseListData,
        headers_lang: str = Depends(get_lang),
) -> dict[str, str]:
    if not data.lang:
        data.lang = headers_lang

    if not data.variables:
        return {}

    return await fd(data.variables, data.lang)


@router.get(
    "/available_languages",
    tags=["available langs list"],
    response_description="Method to get available languages",
    description="Returns languages list",
)
async def get_available_languages(
        lang: str = Depends(get_lang),
        _: int | None = Query(None, alias="brand_id", description="Brand id"),
        brand: Brand | None = Depends(get_current_brand),
        bot: ClientBot | str = Depends(get_current_bot_with_brand),
) -> list[schemas.LanguageSchema]:
    group: Group | None
    if brand:
        group = await Group.get_by_brand(brand.id)
    elif bot:
        group = await Group.get_by_bot(bot.id)
    else:
        group = None

    if group:
        main_langs = group.get_langs_list()
        add_google_langs = group.is_translate and group.allow_all_google_langs
    else:
        main_langs = PLATFORM_MAIN_LANGS
        add_google_langs = PLATFORM_ADD_GOOGLE_LANGS

    langs_list = main_langs.copy()

    try:
        if add_google_langs:
            google_langs = await Translator.get_supported_languages("en") or []
            for google_lang in google_langs:
                if google_lang not in langs_list:
                    langs_list.append(google_lang)

    except Exception as e:
        logging.error(e, exc_info=True)

    async def get_lang_name(lang_code: str, on_lang: str | None = None):
        lang_code = hardfix_language_code(lang_code)

        if on_lang is None:
            on_lang = lang_code
        else:
            on_lang = hardfix_language_code(on_lang)

        try:
            return await Translator.get_language_name(on_lang, lang_code)
        except Exception as err:
            logging.error(err, exc_info=True)
            return None

    return [
        schemas.LanguageSchema(
            code=code,
            english_name=(english_name := await get_lang_name(code, "en")),
            original_name=(original_name := await get_lang_name(code)),
            current_name=(current_name := await get_lang_name(code, lang)),
            display_name=(current_name or original_name or english_name or code).capitalize(),
            is_auto_translate=code not in main_langs,
        )
        for code in langs_list
    ]


@router.get("/locale/{locale}")
async def get_locale(
        locale: str,
        current_checksum: str | None = None,
) -> GetLocaleResult:
    if not locales.check_locale(locale, True):
        raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail="Invalid locale")
    return await locales.get(locale, current_checksum)
