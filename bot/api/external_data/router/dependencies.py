from datetime import datetime

from core.external_data import schemas

from fastapi import Query


def get_filters(
        date_from: datetime | None = Query(None, description="start date in iso format for load data"),
        date_to: datetime | None = Query(None, description="end date in iso format for load data"),
        store_id: int | None = Query(None, description="id store for load data"),
        offset: int = Query(0, description="offset for load data"),
        limit: int = Query(100, description="limit for load data (maximum 100)"),
) -> schemas.ExternDataFilters:
    return schemas.ExternDataFilters(
        date_from=date_from,
        date_to=date_to,
        store_id=store_id,
        offset=offset,
        limit=limit,
    )
