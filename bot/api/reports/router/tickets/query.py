from db.models import Brand, CRMTicket, CRMTicketStatus, Group, User
from db.query_builder.query_builder import DBQueryBuilder
from schemas import (
    ExtraFilterData, FilterType, QueryBuilderObjConf, QueryBuilderSettings,
    ReportsTicketRowsSchema, SortData,
)

tickets_query = DBQueryBuilder(
    QueryBuilderSettings(
        objects={
            'ticket': QueryBuilderObjConf(
                model=CRMTicket,
                fields=[
                    "id",
                    "source",
                    'status',
                    'change_date',
                    'title',
                    "bot_id",
                    'group_id',
                    'user_id',
                    'internal_comment',
                ],
                default_fields={
                    "id",
                    "source",
                    'status',
                    'change_date',
                },
                select_from=True,
                default_obj=True,
            ),
            "ticket_status": QueryBuilderObjConf(
                model=CRMTicketStatus,
                fields=[
                    "id",
                    "status",
                    "initiated_by",
                    "initiated_by_user_id",
                    "ticket_id",
                    "header",
                    "message",
                    "internal_comment",
                ],
                default_fields={
                    "id",
                    "status",
                },
                join_expr=CRMTicketStatus.ticket_id == CRMTicket.id,
            ),
            "user": QueryBuilderObjConf(
                model=User,
                fields=[
                    "id",
                    "uuid_token",
                    "incust_external_id",
                    "date_joined",
                    "birth_date",
                    "name",
                    "first_name",
                    "last_name",
                    "full_name",
                    "is_guest_user",
                    "email",
                    "chat_id",
                    "username",
                    "wa_phone",
                    "wa_name",
                    "photo_url",
                ],
                default_fields={
                    "id",
                    "name",
                    "date_joined",
                },
                join_expr=CRMTicket.user_id == User.id,
            ),
            "group": QueryBuilderObjConf(
                model=Group,
                fields=[
                    "id",
                    "name",
                    "status",
                    "lang",
                ],
                default_fields={
                    "id",
                    "lang",
                },
                join_expr=CRMTicket.group_id == Group.id,
                depends_on=("ticket",),
            ),
            "brand": QueryBuilderObjConf(
                model=Brand,
                fields=[
                    "id",
                    "name",
                    "domain",
                ],
                default_fields={
                    "id",
                },
                join_expr=Group.id == Brand.group_id,
                depends_on=("group",),
            ),
        },
        extra_filters=[
            ExtraFilterData(
                type=FilterType.ONE_OF,
                field="group.id",
                name="profile_id",
            ),
            ExtraFilterData(
                type=FilterType.EQUAL,
                field="group.status",
                name="profile_status",
            )
        ],
        default_sort=[
            SortData(field="ticket.id", desc=True),
        ],
        row_data_model=ReportsTicketRowsSchema
    )
)
