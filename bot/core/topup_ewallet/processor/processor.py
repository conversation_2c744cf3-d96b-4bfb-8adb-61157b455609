import logging
import math
from typing import Literal

from pydantic import BaseModel

import exceptions
import schemas
from config import NO_CENT_CURRENCIES
from core import messangers_adapters as ma
from core.invoice import InvoiceService, show_invoice
from core.invoice.functions import calc_extra_fee
from core.topup_ewallet.keyboards import get_yes_or_no_keyboard
from core.topup_ewallet.processor.state import TopUpEWalletState
from db.models import (
    Brand, ClientBot, EWallet, Group, Invoice, User,
)
from utils.message import send_tg_message, send_wa_message
from utils.numbers import format_currency
from utils.text import c, f, html_to_markdown

debugger = logging.getLogger('debugger.fastpaybot')


class TopUpEWalletData(BaseModel):
    ewallet_id: int | None = None
    group_id: int | None = None
    invoice_id: int | None = None
    entered_amount: float | None = None
    bot_id: int | None = None
    bot_type: Literal["whatsapp", "telegram"] = "telegram"


class TopUpEWalletProcessor:
    def __init__(
            self,
            messanger_obj: ma.Message | ma.ButtonQuery,
            state: ma.<PERSON>ontex<PERSON>,
            user: User,
            lang: str,
            data: TopUpEWalletData | None = None,
    ):
        self.messanger_obj: ma.Message | ma.ButtonQuery = messanger_obj
        self.state = state
        self.user: User = user
        self.lang: str = lang
        self._data: TopUpEWalletData | None = data
        self._invoice: Invoice | None = None
        self._group: Group | None = None
        self._brand: Brand | None = None
        self._ewallet: EWallet | None = None

    @property
    def answer_obj(self):
        return ma.detect_answer_obj(self.messanger_obj)

    async def update_data(self, new_data: TopUpEWalletData | None = None, **kwargs):
        if not new_data:
            new_data = await self.data

        for key, value in kwargs.items():
            setattr(new_data, key, value)

        await self.state.update_data(ewallet_data=new_data.dict(exclude_none=True))
        self._data = new_data
        return new_data

    async def reload_data(self):
        state_data = await self.state.get_data()
        try:
            self._data = TopUpEWalletData(**state_data.get("ewallet_data"))
        except Exception as e:
            raise exceptions.FastPayInvalidStateDataError() from e
        return self._data

    @property
    async def data(self):
        if not self._data:
            await self.reload_data()
        return self._data

    @property
    async def invoice(self):
        if not self._invoice:
            data = await self.data
            if data.invoice_id:
                self._invoice = await Invoice.get(
                    data.invoice_id,
                )
                if not self._invoice:
                    raise exceptions.InvoiceNotFoundError(data.invoice_id)

        return self._invoice

    @property
    async def group(self):
        if not self._group:
            data = await self.data
            self._group = await Group.get(
                data.group_id,
                status="enabled",
            )
            if not self._group:
                raise exceptions.ProfileNotFoundError(data.group_id)

        return self._group

    @property
    async def brand(self):
        if not self._brand:
            data = await self.data
            self._brand = await Brand.get(group_id=data.group_id)
        return self._brand

    @property
    async def ewallet(self):
        if not self._ewallet:
            data = await self.data
            self._ewallet = await EWallet.get(data.ewallet_id, is_enabled=True, is_deleted=False)
        return self._ewallet

    @classmethod
    async def setup(
            cls,
            messanger_obj: ma.Message | ma.ButtonQuery,
            state: ma.FSMContext,
            user: User,
            bot: ClientBot,
            lang: str,
            data: TopUpEWalletData | None = None,
    ):
        processor = cls(messanger_obj, state, user, lang, data)

        # first time init
        if data and data.ewallet_id:
            await processor.update_data(
                ewallet_id=data.ewallet_id,
                group_id=data.group_id,
                bot_id=bot.id,
                bot_type=bot.bot_type,
            )

        data = await processor.data
        debugger.debug(f"{data=}")

        return processor

    async def send_enter_amount_menu(self, ):
        bot = await ClientBot.get_current()
        ewallet = await self.ewallet
        footer_text = await f("fast pay enter amount text", self.lang)

        text = await f(
            "CLIENT_BOT_TOPUP_SPECIAL_ACCOUNT_HEADER", self.lang,
            special_account_title=ewallet.name
        ) + f"\n\n<i>{footer_text}</i>"
        await TopUpEWalletState.EnterAmount.set()

        match bot.bot_type:
            case "telegram":
                await send_tg_message(
                    self.answer_obj.chat.id,
                    content_type="text",
                    text=text,
                    photo=None,
                )
            case "whatsapp":
                text = html_to_markdown(text)

                await send_wa_message(
                    self.answer_obj.user.phone_number,
                    content_type="text",
                    bot_token=bot.token,
                    wa_from=bot.whatsapp_from,
                    text=text,
                    image=None,
                )

    async def send_state_menu(self):
        cur_state = await self.state.get_state()
        data = await self.data

        match cur_state:
            case TopUpEWalletState.EnterAmount.state:
                await self.send_enter_amount_menu()
            case TopUpEWalletState.Confirmation.state:
                await self.send_confirmation_menu(data.bot_type, self.lang)
            case _:
                raise exceptions.FastPayInvalidStateError(cur_state)

    async def get_or_create_invoice(self):
        invoice = await self.invoice
        data = await self.data
        if invoice and invoice.total_sum == int(data.entered_amount * 100):
            return invoice

        if (await self.ewallet).currency in NO_CENT_CURRENCIES:
            entered_amount = math.ceil(data.entered_amount)
        else:
            entered_amount = round(data.entered_amount, 2)

        invoice_service = InvoiceService(
            group=await self.group,
            user=self.user,
        )

        self._invoice = await invoice_service.create_invoice(
            schemas.InvoiceTypeEnum.TOPUP_ACCOUNT,
            "entered_amount",
            invoice_template=None,
            creator=self.user,
            count=1,
            entered_amount=entered_amount,
            first_name=self.user.first_name,
            last_name=self.user.last_name,
            email=self.user.email,
            phone=self.user.wa_phone,
            # incust_check=incust_check.dict(),
            qr_mode="web",
            ewallet_id=data.ewallet_id,
            bot=await ClientBot.get_current(),
        )
        await self.update_data(invoice_id=self._invoice.id)
        return self._invoice

    async def send_confirmation_menu(
            self, bot_type: Literal["telegram", "whatsapp"], lang: str
    ):
        keyboad = await get_yes_or_no_keyboard(bot_type, lang)
        await TopUpEWalletState.Confirmation.set()
        text = await self.format_payment_message()
        await self.messanger_obj.answer(
            text if bot_type == "telegram" else html_to_markdown(text),
            reply_markup=keyboad
        )

    async def confirm_topup_ewallet(self):
        invoice = await self.get_or_create_invoice()
        payload = c("pay_for_invoice", invoice_id=invoice.id)
        await show_invoice(invoice, payload, lang=self.lang)

    async def format_payment_message(self) -> str:
        """Форматує повідомлення з сумами"""

        data = await self.data
        group = await self.group
        ewallet = await self.ewallet
        base_amount = data.entered_amount

        lines = [await f(
            "fast pay entered amount text",
            self.lang,
            amount=format_currency(
                data.entered_amount, ewallet.currency,
                group.lang, group.country_code
            ),
        )]

        amounts = {}

        # Націнка
        extra_fee = await calc_extra_fee(
            group.id,
            int(base_amount * 100),
            ewallet.currency,
        )
        total_extra_fee = extra_fee.total_extra_fee / 100 if (extra_fee and
                                                              extra_fee.total_extra_fee) else 0

        amount_after_extra_fee = base_amount + total_extra_fee

        amounts.update(
            {
                "total_extra_fee": total_extra_fee,
                "total_extra_fee_formatted": format_currency(
                    total_extra_fee, ewallet.currency,
                    group.lang, group.country_code
                ),
                "amount_after_extra_fee": amount_after_extra_fee,
                "amount_after_extra_fee_formatted": format_currency(
                    amount_after_extra_fee,
                    ewallet.currency,
                    group.lang, group.country_code
                )
            }
        )

        # Фінальна сума
        final_amount = amount_after_extra_fee

        if ewallet.currency in NO_CENT_CURRENCIES:
            final_amount = math.ceil(final_amount)

        amounts["final_amount"] = final_amount
        amounts["final_amount_formatted"] = format_currency(
            final_amount, ewallet.currency,
            group.lang, group.country_code
        )

        amounts.update(
            {
                "total_extra_fee": total_extra_fee,
                "total_extra_fee_formatted": format_currency(
                    total_extra_fee, ewallet.currency,
                    group.lang, group.country_code
                ),
                "amount_after_extra_fee": amount_after_extra_fee,
                "amount_after_extra_fee_formatted": format_currency(
                    amount_after_extra_fee,
                    ewallet.currency,
                    group.lang, group.country_code
                )
            }
        )

        # Націнка
        if amounts.get("total_extra_fee"):
            lines.append(
                await f("admin extra fees title", self.lang) +
                f": {amounts['total_extra_fee_formatted']}"
            )

        # Сума до сплати
        lines.append("")
        lines.append(
            "<b>" + await f(
                "fast pay amount to pay text",
                self.lang,
                amount=amounts["final_amount_formatted"]
            ) + "</b>"
        )

        return "\n".join(lines)
