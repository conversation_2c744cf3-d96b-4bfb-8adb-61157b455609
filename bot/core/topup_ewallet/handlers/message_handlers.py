from psutils.convertors import str_to_float

from core import messangers_adapters as ma
from core.fast_pay.processor.helpers import HandleFormatError
from core.topup_ewallet.processor.processor import TopUpEWalletProcessor
from core.topup_ewallet.processor.state import TopUpEWalletState
from db.models import ClientBot, User


@ma.handler.message()
async def handle_ewallet_enter_amount(
        message: ma.Message,
        state: ma.FSMContext,
        user: User,
        bot: ClientBot,
        lang: str,
):
    processor = await TopUpEWalletProcessor.setup(message, state, user, bot, lang)

    async with HandleFormatError(message, lang):

        entered_amount = str_to_float(
            message.text or message.caption or "",
            only_positive=True
        )
        await processor.update_data(
            entered_amount=entered_amount,
            invoice_id=None,
        )
        await TopUpEWalletState.Confirmation.set()

        await processor.send_state_menu()



def register_topup_ewallet_message_handlers(dp: ma.DispatcherType):
    handle_ewallet_enter_amount.setup(
        dp,
        state=[TopUpEWalletState.EnterAmount],
    )
