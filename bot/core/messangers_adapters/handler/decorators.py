from collections.abc import Callable

from utils.type_vars import P, RT
from .adapter import HandlerAdapter


def message() -> Callable[[Callable[P, RT]], HandlerAdapter[P, RT]]:
    return HandlerAdapter.decorate(
        {
            "telegram": "message",
            "whatsapp": "message",
        }
    )


def button(
        *wa_handlers: str,  # "reply", "list" | default reply
) -> Callable[[Callable[P, RT]], HandlerAdapter[P, RT]]:
    if not wa_handlers:
        wa_handlers = "reply_query"
    else:
        wa_handlers = tuple(
            "list_reply_query" if el == "list" else f"{el}_query" for el in wa_handlers
        )

    return HandlerAdapter.decorate(
        {
            "telegram": "callback_query",
            "whatsapp": wa_handlers,
        }
    )
