from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, Literal, Optional, Union

from config import FREQUENCY_OF_SENDING_INTEREST_POSTS
from db.models import User, ClientBot

from utils.message import send_tg_message
from utils.text import f

from .base import Status
from .models import InterestSetting, InterestPost, InterestStatistic
from .keyboards import get_interests_keyboard


@dataclass
class StatisticData:
    user: str
    status: str
    datetime: datetime
    interests: list = None


def create_interest_post(
    text: str,
    user_chat_id: int,
    interests: List[str]
) -> InterestPost:
    user_id = User.get(user_chat_id)
    post = InterestPost.create(text, user_id, interests)
    return post


def get_interest_post(
    interest_post_id: Union[Literal["all"], int] = "all"
) -> Union[List[InterestPost], InterestPost]:
    post = InterestPost.get(interest_post_id)
    return post


async def get_interest_post_text(post_id: int, lang: str) -> str:
    post = InterestPost.get(post_id)
    text = "\n".join([
        post.text,
        await f("posted by text", lang, publisher_name=post.owner.full_name),
        await f("select interest to filter offers", lang),
    ])
    return text


async def update_user_statistic(
        bot_id: Optional[int], user_chat_id: int,
        interest_post_id: int, interest: str = None,
        status: Status = Status.NEUTRAL
) -> bool:
    if not bot_id:
        bot_id = ClientBot.get_current_bot_id()
    user_id = (await User.get(user_chat_id)).id
    statistic = InterestStatistic.get_user_statistic(bot_id, user_id, interest_post_id)
    if not statistic:
        statistic = InterestStatistic.create(bot_id, user_id, interest_post_id)
    return statistic.update(interest, int(status))


def get_bots_from_settings() -> List[int]:
    return InterestSetting.get_bots()


def get_setting_frequency() -> int:
    return FREQUENCY_OF_SENDING_INTEREST_POSTS


def get_posts_for_users(bot_id: int) -> Dict[int, int]:
    result = {}
    users_posts = InterestPost.get_posts_for_users(bot_id)
    for user_chat_id, post in users_posts:
        result[user_chat_id] = post
    return result


async def send_post_for_user(bot_id: int, user_chat_id: int, post: "InterestPost"):
    client_bot = await ClientBot.get(bot_id)
    lang = await (await User.get(user_chat_id)).get_lang(bot_id)

    keyboard = await get_interests_keyboard(post.id, post.get_interests(), lang)
    text = await get_interest_post_text(post.id, lang)

    post_message_kwargs = {"text": text}

    await send_tg_message(user_chat_id, bot_token=client_bot.token, keyboard=keyboard, **post_message_kwargs)
