from __future__ import annotations

from typing import Literal

from pydantic import BaseModel, Field

from schemas import ProductTypeLiteral
from schemas.payment_settings.liqpay import UnitName
from .characteristic import CharacteristicValue, CharacteristicValueAPI
from .translations import ValueAndTranslationAPI


class BaseProductAPI(BaseModel):
    id: int | None = None
    product_id: str | None = None
    product_group_id: str | None = None
    external_id: str | None = None

    is_available: bool = True

    position: int

    image_url: str | None = Field(None, description="product image in the catalog")
    gallery_items: list[str] | None = Field(None, description="product images and videos")

    price: float | int
    old_price: float | int = 0

    buy_min_quantity: int = 1

    spots_prices: dict[str, ProductSpotPrice] | None = None

    is_weight: bool = False
    weight_unit: str | None = None

    characteristics: list[CharacteristicValue] | None = None

    stores_external_ids: list[str] | None = None
    attribute_groups_external_ids: list[str]
    categories_external_ids: list[str] | None = None
    floating_sum_value: str | None = None

    product_type_info_custom_fields: list[ProductCustomField] | None = None
    product_type_liqpay_custom_fields: list[ProductCustomField] | None = None
    type: ProductTypeLiteral = "goods"
    pti_info_text: str | None = None
    pti_info_link: str | None = None

    liqpay_id: str | None = None
    liqpay_unit_name: UnitName | None = None
    liqpay_codifier: str | None = None
    liqpay_tax_list: str | None = None

    """ unit_name — (обов'язковий) найменування одиниці вимірювання. Повинно бути вказано корректне значення з Довідника.
        codifier — (обов'язковий тільки для певних типів податків — акцизу) значення з довідника УКТВЕД. Повинно містити тільки цифри, не повинно складатися тільки з 0
        tax_list — (обов'язковий) список літер оподаткування, розділених комою (А - без ПДВ 0%, Б - ПДВ 20%, В - ПДВ 7%, Г - акциз 5%)"""

    need_auth: bool = False
    floating_qty_enabled: bool = False

    excel_position: int | None = None
    is_skip_download_images: bool | None = False


class ProductAPI(BaseProductAPI):
    name: ValueAndTranslationAPI
    description: ValueAndTranslationAPI | None = None

    characteristics: list[CharacteristicValueAPI] | None = None

    pti_info_text: ValueAndTranslationAPI | None = None


class Product(BaseProductAPI):
    name: str
    description: str | None = None

    characteristics: list[CharacteristicValue] | None = None

    pti_info_text: str | None = None

    floating_sum_enabled: bool | None = False
    floating_sum_max: float | None = None
    floating_sum_min: float | None = None
    floating_sum_options: str | None = None
    floating_sum_user_sum_enabled: bool | None = False


class ProductSpotPrice(BaseModel):
    id: int | None = None
    product_external_id: str
    store_external_id: str
    price: int | float
    old_price: int | float | None


class FloatingSumSettings(BaseModel):
    is_enabled: bool
    min: float | None = None
    max: float | None = None
    options: list[float] | None = None
    user_sum_enabled: bool


class ProductCustomField(BaseModel):
    name: str
    value: str | None = None
    type: Literal["product_type_info", "product_type_liqpay"]


Product.update_forward_refs()
ProductAPI.update_forward_refs()
