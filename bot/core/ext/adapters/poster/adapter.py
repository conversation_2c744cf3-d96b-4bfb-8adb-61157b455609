import logging
import re

from config import POSTER_REGEX_TO_SKIP_CATEGORY
from core.ext import schemas
from core.ext.api.poster.client_poster import PosterApiClient
from db.crud.group.read import get_poster_settings
from db.models import Brand, DataPorter
from .schemas import (
    PosterApiCategoryData,
    PosterApiProductData,
    PosterApiProductType,
    PosterInstance,
)
from ..base import BaseAdapter
from ...types import ExternalType


class PosterAdapter(BaseAdapter):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        self.poster_instances = None
        self.categories_data: dict[str, list[PosterApiCategoryData]] = {}
        self.products_data: dict[str, list[PosterApiProductData]] = {}

        self.stores: list[schemas.Store] = []
        self.categories: dict[str, schemas.Category] = {}
        self.products: dict[str, schemas.Product] = {}
        self.attribute_groups: dict[str, schemas.AttributeGroup] = {}

        self.cnt_attributes: int = 0
        self.poster_user_domain = None
        self.poster_regex_to_skip_category = kwargs.get(
            "poster_regex_to_skip_category"
        ) or POSTER_REGEX_TO_SKIP_CATEGORY
        self.poster_skip_desc_product = kwargs.get("poster_skip_desc_product") or False
        self.poster_tips_sku = kwargs.get("poster_tips_sku") or None
        self.skip_stores = kwargs.get("skip_stores") or None
        self.poster_api_token = kwargs.get("poster_api_token") or None
        self.poster_client = PosterApiClient(self.brand_id, self.poster_api_token)
        self.logger = logging.getLogger("debugger")

    async def convert_store_data(self, poster_instances: list[PosterInstance]):
        for poster_instance in poster_instances:
            store = poster_instance.store
            restaurant = {
                "external_id": store.spot_id,
                "external_type": ExternalType.POSTER.value,
                "brand_id": self.brand_id,
                "name": store.spot_name,
                "currency": "UAH",
            }

            if store.lat and store.lng:
                restaurant["latitude"] = str(store.lat) if store.lat else None
                restaurant["longitude"] = str(store.lng) if store.lng else None

            restaurant["custom_fields"] = [
                {
                    "name": "address",
                    "value": store.spot_adress if store.spot_adress else "",
                },
            ]

            converted_store = schemas.Store(**restaurant)

            self.stores.append(converted_store)

            self.categories_data[converted_store.external_id] = [
                item.category for item in poster_instance.categories
            ]

            self.products_data[converted_store.external_id] = [
                item.products for item in poster_instance.categories
            ]

    def convert_categories_data(
        self, categories: list[PosterApiCategoryData], store_id
    ):
        sorted_categories = sorted(categories, key=lambda x: (x.sort_order is None, x.sort_order))
        for index, category in enumerate(sorted_categories):
            try:
                if self.poster_regex_to_skip_category and re.search(
                        self.poster_regex_to_skip_category, category.category_name
                ):
                    continue
            except Exception as err:
                logging.error(
                    f"error use regexp for skip poster category, {self.poster_regex_to_skip_category=}, {err=}",
                    exc_info=True
                )

            external_id = category.category_id

            if external_id in self.categories:
                converted_category = self.categories[external_id]
                if store_id not in converted_category.stores_external_ids:
                    converted_category.stores_external_ids.append(store_id)
            else:
                image_url = ""
                if category.category_photo:
                    if category.category_photo.startswith("http"):
                        image_url = category.category_photo
                    elif self.poster_user_domain:
                        image_url = self.poster_user_domain + category.category_photo

                converted_category = schemas.Category(
                    position=index,
                    name=category.category_name,
                    image_url=image_url,
                    external_id=category.category_id,
                    filters=[],
                    stores_external_ids=[store_id],
                    father_category_id=category.parent_category
                    if category.parent_category != "0"
                    else None,
                )
                self.categories[external_id] = converted_category

    def get_products_data(
        self, store_external_id: str, category_id: str
    ) -> list[PosterApiProductData]:
        products_data = []
        for products in self.products_data[store_external_id]:
            for product in products:
                if product.menu_category_id == category_id:
                    products_data.append(product)
        return products_data

    def get_attributes(
        self,
        product: PosterApiProductData,
        converted_product: schemas.Product,
    ):
        try:
            min_ = max_ = 1  # product attributes/modifications can be only one selected
            converted_attribute_group = schemas.AttributeGroup(
                attribute_group_id="{}_{}".format(
                    ExternalType.POSTER.value,
                    converted_product.product_id,
                ),
                name="[{}]".format(product.product_name),
                min=min_,
                max=max_,
                external_id="{}_{}".format(
                    ExternalType.POSTER.value,
                    converted_product.product_id,
                ),
                attributes=[],
                position=0,
            )
        except Exception as e:
            print(e, product.dict())
            return

        converted_product.attribute_groups_external_ids.append(
            converted_attribute_group.attribute_group_id
        )
        if (
                converted_attribute_group.attribute_group_id
                not in self.attribute_groups.keys()
        ):
            self.attribute_groups[
                converted_attribute_group.attribute_group_id
            ] = converted_attribute_group
        is_default_set = False
        for modifier in product.modifications or []:
            self.cnt_attributes += 1

            selected_by_default = False
            if int(modifier.spots[0].get("price")) == 0 and not is_default_set:
                selected_by_default = True
                is_default_set = True

            converted_attribute = schemas.Attribute(
                attribute_id="{}_{}".format(
                    converted_attribute_group.attribute_group_id,
                    modifier.modificator_id,
                ),
                name=modifier.modificator_name,
                max=1,
                # must be only kinds of product !!!
                selected_by_default=selected_by_default,
                min=0,
                price_impact=modifier.spots[0].get("price"),
                external_id="{}_{}".format(
                    ExternalType.POSTER.value, modifier.modificator_id
                ),
            )

            self.attribute_groups[
                converted_attribute_group.attribute_group_id
            ].attributes.append(converted_attribute)

    def get_dish_attributes(
        self,
        product: PosterApiProductData,
        converted_product: schemas.Product,
    ):

        for index, group_modification in enumerate(product.group_modifications or []):
            min_ = group_modification.num_min
            max_ = group_modification.num_max

            converted_attribute_group = schemas.AttributeGroup(
                attribute_group_id=group_modification.dish_modification_group_id,
                name=group_modification.name,
                min=min_,
                max=max_,
                attributes=[],
                external_id=group_modification.dish_modification_group_id,
                position=index,
            )

            converted_product.attribute_groups_external_ids.append(
                converted_attribute_group.attribute_group_id
            )
            if (
                    converted_attribute_group.attribute_group_id
                    in self.attribute_groups.keys()
            ):
                continue

            self.attribute_groups[
                converted_attribute_group.attribute_group_id
            ] = converted_attribute_group

            for modification in group_modification.modifications or []:
                self.cnt_attributes += 1

                converted_attribute = schemas.Attribute(
                    attribute_id="{}_{}".format(
                        converted_attribute_group.attribute_group_id,
                        modification.dish_modification_id,
                    ),
                    name=modification.name,
                    min=min_,
                    max=max_,
                    price_impact=modification.price * 100,
                    external_id=modification.dish_modification_id,
                )

                self.attribute_groups[
                    converted_attribute_group.attribute_group_id
                ].attributes.append(converted_attribute)

        return

    def convert_products_data(
        self, products: list[PosterApiProductData], store_id, category_id
    ):
        sorted_products = sorted(products, key=lambda x: (x.sort_order is None, x.sort_order))
        for index, product in enumerate(sorted_products):
            spots_prices: dict[str, schemas.ProductSpotPrice] = {}

            external_id = product.product_id
            if external_id in self.products:
                converted_product = self.products[external_id]
                if store_id not in converted_product.stores_external_ids:
                    converted_product.stores_external_ids.append(store_id)
                if category_id not in converted_product.categories_external_ids:
                    converted_product.categories_external_ids.append(category_id)
                if product.spots:
                    for spot_price in product.spots:
                        if spot_price.visible and store_id == spot_price.spot_id:
                            converted_product.spots_prices[
                                store_id
                            ] = schemas.ProductSpotPrice(
                                store_external_id=store_id,
                                product_external_id=external_id,
                                price=spot_price.price
                            )
                            break
                continue

            if product.spots:
                if product.spots[0].price > product.cost:
                    product.cost = product.spots[0].price
                for spot_price in product.spots:
                    if spot_price.visible and store_id == spot_price.spot_id:
                        spots_prices[store_id] = schemas.ProductSpotPrice(
                            store_external_id=store_id,
                            product_external_id=external_id,
                            price=spot_price.price
                        )
                        break

            elif product.modifications and len(product.modifications) > 0:
                product.modifications = [
                    mod
                    for mod in product.modifications
                    if mod.spots[0].get("price") is not None
                ]
                min_price_mod = min(
                    product.modifications,
                    key=lambda x: int(
                        x.spots[0].get("price") if x.spots[0].get("price") else "0"
                    ),
                )

                ind_min_price = product.modifications.index(min_price_mod)
                product.cost = int(min_price_mod.spots[0].get("price"))
                product.modifications[ind_min_price].spots[0]["price"] = 0
                for ind_mod, mod in enumerate(product.modifications):
                    if ind_mod == ind_min_price:
                        continue
                    mod.spots[0]["price"] = int(mod.spots[0]["price"]) - product.cost

            elif (
                    product.cost == 0
                    and product.spots[0].price == 0
                    and product.group_modifications
                    and product.group_modifications[0].num_max == 1
                    and product.group_modifications[0].modifications
                    and product.group_modifications[0].modifications[0].price > 0
            ):
                product.group_modifications[0].modifications = [
                    mod
                    for mod in product.group_modifications[0].modifications
                    if mod.price is not None
                ]

                min_price_mod = min(
                    product.group_modifications[0].modifications,
                    key=lambda x: int(x.price if x.price else 0),
                )

                ind_min_price = product.group_modifications[0].modifications.index(
                    min_price_mod
                )
                product.cost = min_price_mod.price * 100
                product.group_modifications[0].modifications[ind_min_price].price = 0

                for ind_mod, mod in enumerate(
                        product.group_modifications[0].modifications
                ):
                    if ind_mod == ind_min_price:
                        continue
                    mod.price = mod.price - product.cost / 100

            image_url = ""
            if product.photo_origin:
                if product.photo_origin.startswith("http"):
                    image_url = product.photo_origin
                elif self.poster_user_domain:
                    image_url = self.poster_user_domain + product.photo_origin
            is_available = 1
            if product.hidden:
                is_available = 0
            elif product.spots and product.spots[0].visible == 0:
                is_available = 0
            schemas.Product.update_forward_refs()

            if (
                    product.product_production_description
                    and not self.poster_skip_desc_product
            ):
                description = product.product_production_description
            else:
                description = ""

            converted_product = schemas.Product(
                product_id=product.barcode
                if self.poster_tips_sku and self.poster_tips_sku == product.barcode
                else product.product_id,
                name=product.product_name,
                price=product.cost,
                image_url=image_url,
                description=description,
                position=index,
                external_id=product.product_id,
                is_available=bool(is_available),
                is_weight=product.weight_flag,
                weight_unit="100 г" if product.weight_flag else None,
                characteristics=[],
                stores_external_ids=[store_id],
                attribute_groups_external_ids=[],
                categories_external_ids=[category_id],
                spots_prices=spots_prices if spots_prices else None,
                type="goods",
            )

            if not converted_product:
                continue

            if (
                    product.type.value == PosterApiProductType.product.value
                    and product.modifications
            ):
                self.get_attributes(product, converted_product)
            elif (
                    product.type.value == PosterApiProductType.dish.value
                    and product.group_modifications
            ):
                self.get_dish_attributes(product, converted_product)

            self.products[external_id] = converted_product

    @classmethod
    async def get_data_from_database(
        cls, brand: Brand, data_porter: DataPorter | None, lang: str, user_lang: str, **kwargs
    ):
        poster_settings = await get_poster_settings(brand.id)

        return dict(
            poster_regex_to_skip_category=poster_settings.poster_regex_to_skip_category,
            poster_skip_desc_product=poster_settings.poster_skip_desc_product,
            poster_tips_sku=poster_settings.poster_tips_sku,
            skip_stores=poster_settings.poster_skip_stores,
            poster_api_token=poster_settings.poster_api_token,
        )

    async def convert_data(self):
        await self.convert_store_data(self.poster_instances)

        for store in self.stores:
            self.convert_categories_data(
                self.categories_data[store.external_id], store.external_id
            )

            for category_id in self.categories:
                products = self.get_products_data(
                    store.external_id,
                    category_id,
                )
                self.convert_products_data(
                    products,
                    store.external_id,
                    category_id,
                )

        return schemas.AdapterResult(
            stores=self.stores,
            products=list(self.products.values()),
            product_groups=[],
            categories=list(self.categories.values()),
            attribute_groups=list(self.attribute_groups.values()),
            characteristics=[],
        )

    async def get_poster_data(self):
        self.logger.debug("\nSTART get_poster_data -> brand: {}".format(self.brand_id))
        self.poster_instances = await self.poster_client.get_poster_instances(self.skip_stores)
        poster_account_settings = await self.poster_client.get_settings()
        self.poster_user_domain = "https://{}.joinposter.com".format(
            poster_account_settings.get("response", {}).get("COMPANY_ID")
        )

    async def get_and_convert_data(self):
        await self.get_poster_data()
        return await self.convert_data()
