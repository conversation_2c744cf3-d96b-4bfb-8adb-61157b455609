import asyncio
import logging
from dataclasses import dataclass

from incust_client_api_client.api.coupon_api import Coupon<PERSON><PERSON>
from incust_client_api_client.exceptions import ApiException as ClientApiException
from incust_terminal_api_client.exceptions import ApiException as TerminalApiException
from psutils.date_time import localise_datetime
from psutils.mailing import GmailClient
from psutils.text import replace_html_symbols

import config as cfg
from config import (
    CRM_HOST, DATETIME_FORMAT_SECONDS, SERVICE_BOT_API_TOKEN,
    SERVICE_BOT_USERNAME,
)
from core import messangers_adapters as ma
from core.bot.ewallet_handlers import process_single_ewallet
from core.external_coupon import send_coupon_info
from core.invoice.functions import (
    get_invoice_comment_label,
)
from core.kafka.producer.functions import (
    add_push_notifications_for_action,
    add_telegram_notifications_for_action,
)
from core.kafka.producer.helpers import add_items_text, build_fcm_message
from core.loyalty.incust_client_adapter import run_with_incust_client_api
from core.payment.utils.extra_fees import (
    get_extra_fee_str, get_extra_fee_txt,
    get_formated_extra_fees,
)
from core.templater import templater
from core.whatsapp.functions import send_delayed_wa_menu
from core.whatsapp.keyboards import get_wa_menu_keyboard
from db import crud
from db.models import (
    Brand, ClientBot, EWallet, Group, Invoice, InvoiceTemplate, LoyaltySettings,
    MenuInStore, StoreOrderPayment, User, UserClientBotActivity,
)
from schemas import (
    AuthSourceEnum, IncustCheckInfoData, IncustInfoData,
    InvoiceEmailTemplate,
    InvoiceMessageData, InvoiceTypeEnum,
)
from utils.email_funcs import send_with_attachments
from utils.message import send_tg_message, send_wa_message
from utils.numbers import format_currency
from utils.platform_admins import send_message_to_platform_admins
from utils.redefined_classes import Bot
from utils.text import f, fd, html_to_markdown

debugger = logging.getLogger('debugger')


async def make_loyalty_check_info_message(
    invoice: Invoice,
    lang: str,
    group: Group,
    currency: str,
    show_awards: bool,
    show_redeemed: bool,
) -> str:
    """Створює повідомлення про лояльність зі збережених даних invoice."""
    try:
        # Перевіряємо наявність збережених даних лояльності
        if not (invoice.loyalty_bonuses_added or invoice.loyalty_discount_amount):
            return ""
        
        loyalty_text_parts = []
        
        # Бонуси, що нараховуються
        if show_awards and invoice.loyalty_bonuses_added and invoice.loyalty_bonuses_added > 0:
            bonuses_text = await f(
                "loyalty bonuses added text", lang,
                bonuses=format_currency(
                    invoice.loyalty_bonuses_added / 100, "bonus", 
                    locale=group.lang, territory=group.country_code
                )
            )
            loyalty_text_parts.append(bonuses_text)
        
        # Знижки
        if invoice.loyalty_discount_amount and invoice.loyalty_discount_amount > 0:
            discount_text = await f(
                "loyalty discount text", lang,
                discount=format_currency(
                    invoice.loyalty_discount_amount / 100, currency, 
                    locale=group.lang, territory=group.country_code
                )
            )
            loyalty_text_parts.append(discount_text)
        
        return "\n".join(loyalty_text_parts)
        
    except Exception as ex:
        logging.error(f"Error creating loyalty check info message: {ex}", exc_info=True)
        return ""


async def get_incust_check_info_message_data_to_user(
    invoice: Invoice,
    lang: str,
    currency: str,
    user: User,
) -> IncustInfoData | None:
    """Отримує дані повідомлення про перевірку для користувача зі збережених даних invoice."""
    try:
        # Перевіряємо чи є збережені дані лояльності для показу
        if not (invoice.loyalty_bonuses_added or invoice.loyalty_discount_amount):
            return None
            
        # Створюємо базові тексти для повідомлення
        percent = 0.0
        total_amount = invoice.loyalty_amount / 100 if invoice.loyalty_amount else invoice.total_sum / 100
        if total_amount and invoice.loyalty_discount_amount:
            percent = (invoice.loyalty_discount_amount / 100) / total_amount * 100
            
        discount_text = await f(
            "loyalty discount text", lang,
            discount=format_currency(
                invoice.loyalty_discount_amount / 100 if invoice.loyalty_discount_amount else 0, 
                currency, locale=user.lang or 'en'
            )
        ) if invoice.loyalty_discount_amount else ""
        
        bonuses_sum_text = await f(
            "loyalty bonuses added text", lang,
            bonuses=format_currency(
                invoice.loyalty_bonuses_added / 100 if invoice.loyalty_bonuses_added else 0, 
                "bonus", locale=user.lang or 'en'
            )
        ) if invoice.loyalty_bonuses_added else ""
        
        loyalty_discount_header = await f("loyalty discount header", lang)
        loyalty_info_text = await f("loyalty info text", lang)
        sum_amount_text = await f("sum amount text", lang)
        sum_to_pay_text = await f("sum to pay text", lang)
        payed_order_text = await f("payed order text", lang)
        invoice_payed_text = await f("invoice payed text", lang)
        invoice_payed_loyalty_message = await f("invoice payed loyalty message to user", lang)
        
        return IncustInfoData(
            percent=percent,
            discount_text=discount_text,
            bonuses_sum_text=bonuses_sum_text,
            loyalty_discount_header=loyalty_discount_header,
            loyalty_info_text=loyalty_info_text,
            sum_amount_text=sum_amount_text,
            sum_to_pay_text=sum_to_pay_text,
            payed_order_text=payed_order_text,
            invoice_payed_text=invoice_payed_text,
            invoice_payed_loyalty_message_to_user=invoice_payed_loyalty_message,
        )
        
    except Exception as ex:
        logging.error(f"Error getting incust check info message data: {ex}", exc_info=True)
        return None


async def get_coupons_data_from_invoice(
    invoice: Invoice,
    lang: str,
    need_pdf_file: bool,
    user: User,
) -> list:
    """Отримує дані купонів зі збережених даних invoice."""
    try:
        # Використовуємо збережені купони з invoice.loyalty_coupons_data
        if invoice.loyalty_coupons_data:
            from schemas.incust.base import CouponShowData
            result_coupons = []
            
            for coupon_dict in invoice.loyalty_coupons_data:
                coupon_data = CouponShowData(
                    title=coupon_dict.get('title', ''),
                    text=coupon_dict.get('description', ''),
                    code=coupon_dict.get('code', ''),
                    coupon_id=str(coupon_dict.get('id', '')),
                    description=coupon_dict.get('description', ''),
                    pdf=None,
                    pdf_direct_url=None,
                    share_allowed=0,
                    type=coupon_dict.get('type', None),
                    valid=True,
                    locked=False,
                    error=None,
                    coupon_link=None,
                    url=coupon_dict.get('url', None),
                    image=coupon_dict.get('image', None),
                    pdf_url=coupon_dict.get('pdf_url', None),
                )
                
                # Якщо потрібен PDF файл і є URL
                if need_pdf_file and coupon_dict.get('pdf_url'):
                    try:
                        import aiohttp
                        async with aiohttp.ClientSession() as session:
                            async with session.get(coupon_dict['pdf_url']) as response:
                                if response.status == 200:
                                    pdf_content = await response.read()
                                    from email.mime.base import MIMEBase
                                    pdf_mime = MIMEBase('application', 'pdf')
                                    pdf_mime.set_payload(pdf_content)
                                    coupon_data.pdf = pdf_mime
                                    coupon_data.pdf_direct_url = coupon_dict['pdf_url']
                    except Exception as pdf_ex:
                        logging.warning(f"Failed to get PDF for saved coupon {coupon_data.coupon_id}: {pdf_ex}")
                
                result_coupons.append(coupon_data)
            
            return result_coupons
        
        return []
        
    except Exception as ex:
        logging.error(f"Error getting coupons data from invoice: {ex}", exc_info=True)
        return []


async def make_incust_check_info_data_from_invoice(
    invoice: Invoice,
    group: Group,
    lang: str,
    currency: str,
    user: User,
) -> IncustCheckInfoData | None:
    """Створює дані інформації про перевірку зі збережених даних invoice."""
    try:
        # Перевіряємо чи є дані лояльності
        if not (invoice.loyalty_bonuses_added or invoice.loyalty_discount_amount or invoice.loyalty_coupons_data):
            return None
        
        # Отримуємо купони зі збережених даних invoice
        coupons = await get_coupons_data_from_invoice(invoice, lang, True, user)
        
        # Створюємо тексти
        bonuses_added_text = None
        if invoice.loyalty_bonuses_added and invoice.loyalty_bonuses_added > 0:
            bonuses_added_text = await f(
                "loyalty bonuses added text", lang,
                bonuses=format_currency(
                    invoice.loyalty_bonuses_added / 100, "bonus", 
                    locale=group.lang, territory=group.country_code
                )
            )
        
        added_bonuses_text = bonuses_added_text
        
        # Тексти для купонів
        added_coupons_text = None
        added_coupons_count = None
        if coupons:
            added_coupons_count = len(coupons)
            added_coupons_text = await f(
                "coupons added text", lang, count=added_coupons_count
            )
        
        by_check_issued = await f("by check issued", lang) if coupons else None
        
        # Повідомлення про нагороди
        award_info_parts = []
        if bonuses_added_text:
            award_info_parts.append(bonuses_added_text)
        if added_coupons_text:
            award_info_parts.append(added_coupons_text)
            
        award_info_message = "\n".join(award_info_parts) if award_info_parts else None
        
        # Обробляємо спеціальні рахунки зі збережених даних
        specials = None
        special_text = None
        if invoice.loyalty_special_accounts_data:
            specials = []
            special_parts = []
            
            for account_dict in invoice.loyalty_special_accounts_data:
                account_name = account_dict.get('name') or account_dict.get('title') or str(account_dict.get('id', 'Unknown'))
                amount = account_dict.get('amount', 0)
                
                special_data = {
                    'id': account_dict.get('id'),
                    'name': account_name,
                    'amount': amount
                }
                specials.append(special_data)
                
                try:
                    special_text_part = await f(
                        "special account charge text", lang,
                        account_name=account_name,
                        amount=format_currency(
                            amount, currency, locale=group.lang, territory=group.country_code
                        )
                    )
                    special_parts.append(special_text_part)
                except Exception as text_ex:
                    logging.warning(f"Error formatting special account text: {text_ex}")
                    special_parts.append(f"{account_name}: {amount}")
            
            special_text = "\n".join(special_parts) if special_parts else None
        
        # Текст для лояльності
        loyalty_awards_parts = []
        if invoice.loyalty_discount_amount and invoice.loyalty_discount_amount > 0:
            discount_text = await f(
                "loyalty discount text", lang,
                discount=format_currency(
                    invoice.loyalty_discount_amount / 100, currency,
                    locale=group.lang, territory=group.country_code
                )
            )
            loyalty_awards_parts.append(discount_text)
        
        if bonuses_added_text:
            loyalty_awards_parts.append(bonuses_added_text)
        
        loyalty_awards_text = "\n".join(loyalty_awards_parts) if loyalty_awards_parts else ""
        
        pre_check_text = await f("pre check text", lang)
        
        return IncustCheckInfoData(
            discount=invoice.loyalty_discount_amount / 100 if invoice.loyalty_discount_amount else None,
            sum_amount=invoice.loyalty_amount / 100 if invoice.loyalty_amount else invoice.total_sum / 100,
            sum_to_pay=invoice.loyalty_amount_to_pay / 100 if invoice.loyalty_amount_to_pay else invoice.total_sum / 100,
            bonuses_added=invoice.loyalty_bonuses_added / 100 if invoice.loyalty_bonuses_added else None,
            bonuses_added_text=bonuses_added_text,
            added_bonuses_text=added_bonuses_text,
            coupons=coupons,
            added_coupons_text=added_coupons_text,
            added_coupons_count=added_coupons_count,
            by_check_issued=by_check_issued,
            award_info_message=award_info_message,
            specials=specials,
            special_text=special_text,
            pre_check_text=pre_check_text,
            loyalty_awards_text=loyalty_awards_text,
        )
        
    except Exception as ex:
        logging.error(f"Error making incust check info data from invoice: {ex}", exc_info=True)
        return None


async def __get_invoice_text(
        invoice: Invoice,
        user: User,
        group: Group,
        lang: str,
        is_full_bonuses_payment: bool = False,
):
    invoice_payment = await StoreOrderPayment.get(invoice_id=invoice.id)

    menu_in_store_id = invoice.menu_in_store_id
    if menu_in_store_id:
        menu_in_store = await MenuInStore.get(menu_in_store_id)
        menu_in_store_comment = menu_in_store.comment
    else:
        menu_in_store_comment = ""

    total_sum_txt = await f(
        "manager history invoice total sum text", lang,
        total_sum=format_currency(
            (invoice.total_sum / 100), invoice.currency, locale=group.lang, territory=group.country_code
        ),
    )
    paid_sum = format_currency(
        (invoice.paid_sum / 100), invoice.currency, locale=group.lang, territory=group.country_code
    )
    loyalty_text = ""
    if invoice.loyalty_bonuses_added or invoice.loyalty_discount_amount:
        loyalty_text = await make_loyalty_check_info_message(
            invoice, lang, group, invoice.currency, True, True,
        )

    time_payed = None
    if invoice.status == 'payed':
        status_text_variable = "manager history payed invoice text"
        if invoice_payment:
            time_payed = (f''
            f'{localise_datetime(invoice_payment.confirmed_datetime, group.timezone, "utc"):%d.%m.%Y %H:%M:%S}')
    else:
        time_payed = ""
        status_text_variable = "manager history payment failed invoice text"

    time_created = localise_datetime(invoice.time_created, group.timezone, "utc")
    if time_payed is None:
        time_payed = time_created

    profile_name = group.name

    if invoice.user_comment:
        if invoice.invoice_template_id:
            invoice_template = await InvoiceTemplate.get(invoice.invoice_template_id)
        else:
            invoice_template = None
        user_comment_label = await get_invoice_comment_label(
            group.lang, invoice_template, group
        )
        user_comment_line = f"{user_comment_label}: {invoice.user_comment}\n"
    else:
        user_comment_line = ""

    text = await f(
        status_text_variable,
        lang,
        profile_name=profile_name,
        menu_in_store_comment=menu_in_store_comment,
        order_id=None,
        order_created_datetime=None,
        order_status=None,
        order_status_pay=None,
        fullname=user.full_name or user.first_name,
        invoice_id=invoice.id,
        time_created=f'{time_created:%d.%m.%Y %H:%M:%S}',
        time_payed=time_payed,
        total_sum_txt=total_sum_txt,
        amount=paid_sum,
        extra_fee_txt=await get_extra_fee_txt(group, invoice, invoice.currency, lang),
        user_comment_line=user_comment_line,
        payer_fee_txt=('\n\n' + await f(
            'check payment costs text', lang
        ) + ' ' + format_currency(
            (invoice.payer_fee / 100), invoice.currency, group.lang, territory=group.country_code
        )) if invoice.payer_fee else "\n",
    )
    if loyalty_text:
        text += f"\n\n{loyalty_text}"
    if invoice.check_url:
        text += '\n\n' + await f(
            'invoice check url', lang, check_url=invoice.check_url
        )
    if is_full_bonuses_payment:
        text += "\n" + await f(
            "service bot loyalty invoice paid by bonuses", lang,
            invoice_id=invoice.id
        )

    return text


async def add_invoice_service_bot_notifications(
        invoice: Invoice,
        group: Group,
        user: User | None = None,
        is_full_bonuses_payment: bool = False,
):
    async def get_sending_data(manager_user: User):
        manager_lang = manager_user.lang

        text = await __get_invoice_text(
            invoice, user, group, manager_lang,
            is_full_bonuses_payment=is_full_bonuses_payment,
        )

        return {
            "content_type": "text",
            "text": text,
        }

    try:
        await add_telegram_notifications_for_action(
            "service",
            SERVICE_BOT_USERNAME,
            SERVICE_BOT_API_TOKEN,
            action="crm_invoice:read",
            available_data={
                "profile_id": group.id,
                "invoice_id": invoice.id,
            },
            message=get_sending_data,
        )
    except Exception as e:
        logging.getLogger("error.invoice.add_invoice_service_bot_notifications").error(
            e, exc_info=True
        )
        await send_message_to_platform_admins(
            f"An error occurred while sending invoice notifications to service bot: {str(e)}\n"
            f"Invoice: #{invoice.id}\n"
            f"Profile: {group.name}({group.id})\n"
            f"Invoice user: {user.name if user else None}({invoice.user_id})\n"
        )


async def send_payment_notifications(
        group: Group,
        brand: Brand,
        invoice: Invoice,
        invoice_user: User | None = None,
        is_full_bonuses_payment: bool = False,
        terminal_key: str | None = None,
        lang: str | None = None,
):
    user = invoice_user if invoice_user else await User.get_by_id(invoice.user_id)
    payer = await User.get_by_id(
        invoice.payer_id
    ) if invoice.payer_id and invoice.payer_id != invoice.user_id else user
    sender_bot_id = invoice.payment_bot_menu_id or invoice.payment_bot_id or invoice.bot_id
    client_bot = await ClientBot.get(sender_bot_id) if sender_bot_id else None
    menu_in_store = await MenuInStore.get(
        invoice.menu_in_store_id
    ) if invoice.menu_in_store_id else None

    try:
        await __notify_user_about_invoice_payment(
            group, brand, invoice, user, payer, client_bot, menu_in_store, lang
        )
    except Exception as err:
        logging.error(f"send pmt message to user FAILED\n{str(err)}", exc_info=True)
        await send_message_to_platform_admins(
            f"send pmt message to user FAILED\n"
            f"{str(err)}\n\n"
            f"{invoice.id = }\n"
            f"{user.id = }\n"
            f"{payer.id = }\n"
            f"{invoice.email = }\n"
        )

    debugger.debug('Try to send manager pmt Ok messages')

    try:
        # send a message to manager
        await add_invoice_service_bot_notifications(
            invoice,
            group,
            user,
            is_full_bonuses_payment=is_full_bonuses_payment,
        )
    except Exception as err:
        logging.error(f'send pmt message to manager FAILED\n{str(err)}', exc_info=True)
        await send_message_to_platform_admins(
            f"send pmt message to SERVICE BOT FAILED\n"
            f"{str(err)}\n\n"
            f"{invoice.id = }\n"
            f"{user.id = }\n"
            f"{payer.id = }\n"
            f"{invoice.email = }\n"
        )

    await send_invoice_push_notifications(invoice, group, invoice_user)

    # Перевіряємо чи є збережені дані лояльності
    if invoice.incust_transaction_id and (invoice.loyalty_coupons_data or invoice.loyalty_special_accounts_data):
        # Використовуємо збережені дані напряму з invoice
        lang = await user.get_lang()
        
        # Відправляємо купони зі збережених даних invoice
        if invoice.loyalty_coupons_data and client_bot:
            await send_coupons_to_bot_from_invoice_data(
                invoice.loyalty_coupons_data, user, lang, brand.id, client_bot
            )


async def __notify_user_about_invoice_payment(
        group: Group,
        brand: Brand,
        invoice: Invoice,
        user: User,
        payer: User,
        client_bot: ClientBot | None,
        menu_in_store: MenuInStore | None = None,
        lang: str | None = None,
):
    if invoice.status != "payed":
        raise ValueError("Invoice must be payed for this function")

    lang = lang if lang else await user.get_lang(client_bot)

    if not menu_in_store and invoice.menu_in_store_id:
        menu_in_store = await MenuInStore.get(invoice.menu_in_store_id)

    message_data = await __get_invoice_message_data(
        invoice, group, user, lang, menu_in_store
    )

    loyalty_data = await __get_loyalty_data(invoice, group, user, lang)
    if loyalty_data.info_data:
        message_data.loyalty_info_data = loyalty_data.info_data
    if loyalty_data.check_info_data:
        message_data.loyalty_check_info_data = loyalty_data.check_info_data

    if client_bot:
        if payer.chat_id and client_bot.bot_type == "telegram":
            await __delete_invoice_message(
                payer.chat_id,
                client_bot.token,
                invoice.message_id,
            )

        await __notify_user_in_bot(
            user,
            client_bot, invoice,
            message_data,
            brand.id,
            loyalty_data,
            lang,
        )

        debugger.debug('__notify_user_in_bot Ok')

        if invoice.is_friend and user.id != payer.id:
            await __notify_user_in_bot(
                payer,
                client_bot, invoice,
                message_data,
                brand.id,
                loyalty_data,
                lang,
                True,
            )
            debugger.debug('__notify_payer_in_bot Ok')

    user_chat_id = user.chat_id if user else None
    client_bot_token = client_bot.token if client_bot else None
    await __notify_user_by_email(
        invoice,
        group,
        message_data,
        loyalty_data,
        lang,
        user_chat_id,
        client_bot_token,
    )
    debugger.debug('__notify_user_by_email Ok')

    if invoice.status == 'payed' and invoice.is_friend and user.id != payer.id and payer.email:
        await __notify_user_by_email(
            invoice,
            group,
            message_data,
            loyalty_data,
            lang,
            payer.chat_id,
            client_bot_token,
            True,
        )
        debugger.debug('__notify_payer_by_email Ok')


async def __get_invoice_message_data(
        invoice: Invoice,
        group: Group,
        user: User,
        lang: str,
        menu_in_store: MenuInStore | None = None
):
    title = group.name
    if menu_in_store:
        title += f" [{menu_in_store.comment}]"

    if invoice.status == "payed":
        status_text_variable = "invoice payed message to user"
    else:
        status_text_variable = "invoice payment failed message to user"

    type_payed_message = await f(
        'invoice type payed message', lang, invoice_id=invoice.id
    )

    if invoice.check_url:
        url_in_check_text = await f(
            "invoice check url text", lang, check_url=invoice.check_url
        )
    else:
        url_in_check_text = ""

    localised_created_date = localise_datetime(
        invoice.time_created, user.get_timezone(), "utc"
    )
    time_created = localised_created_date.strftime(DATETIME_FORMAT_SECONDS)

    if invoice.status == "payed":
        invoice_payment = await StoreOrderPayment.get(invoice_id=invoice.id)
        if invoice_payment:
            localised_confirmed_date = localise_datetime(
                invoice_payment.confirmed_datetime, user.get_timezone(), "utc"
            )
        else:
            localised_confirmed_date = localised_created_date
        time_payed = localised_confirmed_date.strftime(DATETIME_FORMAT_SECONDS)
    else:
        time_payed = ""

    if invoice.user_comment:
        if invoice.invoice_template_id:
            invoice_template = await InvoiceTemplate.get(invoice.invoice_template_id)
        else:
            invoice_template = None
        user_comment_label = await get_invoice_comment_label(
            lang, invoice_template, group
        )
        user_comment_line = f"{user_comment_label}: {invoice.user_comment}\n"
    else:
        user_comment_line = ""

    debugger.debug(f"making InvoiceMessageData. {time_created = }, {time_payed = }")

    extra_fees = await get_formated_extra_fees(invoice, invoice.currency, lang, group.country_code)
    extra_fee_str = await get_extra_fee_str(extra_fees)
    extra_fee_txt = await get_extra_fee_txt(group, invoice, invoice.currency, lang)
    ewallet_topup_info = await get_ewallet_topup_info(group, invoice, user, lang)

    return InvoiceMessageData(
        title=title,
        amount=format_currency((invoice.total_sum / 100), invoice.currency, group.lang),
        paid_sum=('\n<b>' + await f(
            "web store sum label text", lang
        ) + "</b> " + format_currency(
            invoice.converted_sums["paid_sum"], invoice.currency, group.lang
        )) if invoice.paid_sum else "",
        payer_fee=('\n' + await f(
            'check payment costs text', lang
        ) + ' ' + format_currency(
            invoice.converted_sums["payer_fee"], invoice.currency, group.lang
        ))
        if invoice.payer_fee else "",
        status_text_variable=status_text_variable,
        invoice_payment_mode=invoice.payment_mode,
        invoice_image_url=invoice.photo_url,
        type_payed_message=type_payed_message,
        url_in_check_text=url_in_check_text,
        time_created=time_created,
        time_payed=time_payed,
        user_comment_line=user_comment_line,
        extra_fee_str=extra_fee_str,
        extra_fees=extra_fees,
        extra_fee_txt=extra_fee_txt,
        ewallet_topup_info=ewallet_topup_info,
    )


async def get_ewallet_topup_info(group: Group, invoice: Invoice, user: User, lang: str):
    ewallet_topup_info = ""
    if invoice.ewallet_id:
        try:
            ewallet = await EWallet.get(invoice.ewallet_id, is_deleted=False, is_enabled=True)
            if ewallet:
                ewallet_response = await process_single_ewallet(ewallet, user, lang)
                if invoice.invoice_type == InvoiceTypeEnum.TOPUP_ACCOUNT:
                    ewallet_topup_info = (await f(
                        "special account topped text",
                        lang,
                        sp_account_name=ewallet.name,
                        amount=format_currency(
                            (invoice.before_loyalty_sum / 100), invoice.currency, group.lang,
                            group.country_code
                        ),
                    ) + "\n\n")
                ewallet_topup_info += ewallet_response.message
        except Exception as ex:
            logging.error(f"Error processing ewallet for notification {invoice.id}: {ex}", exc_info=True)
    return ewallet_topup_info


async def __get_loyalty_data(
        invoice: Invoice,
        group: Group,
        user: User,
        lang: str,
):
    # Використовуємо збережені дані з invoice замість incust_check
    if not (invoice.loyalty_bonuses_added or invoice.loyalty_discount_amount or invoice.loyalty_coupons_data):
        return LoyaltyData()

    # Створюємо info_data зі збережених даних
    info_data = None
    if invoice.loyalty_bonuses_added or invoice.loyalty_discount_amount:
        percent = 0.0
        total_amount = invoice.loyalty_amount / 100 if invoice.loyalty_amount else invoice.total_sum / 100
        if total_amount and invoice.loyalty_discount_amount:
            percent = (invoice.loyalty_discount_amount / 100) / total_amount * 100
            
        discount_text = await f(
            "loyalty discount text", lang,
            discount=format_currency(
                invoice.loyalty_discount_amount / 100 if invoice.loyalty_discount_amount else 0, 
                invoice.currency, locale=user.lang or 'en'
            )
        ) if invoice.loyalty_discount_amount else ""
        
        bonuses_sum_text = await f(
            "loyalty bonuses added text", lang,
            bonuses=format_currency(
                invoice.loyalty_bonuses_added / 100 if invoice.loyalty_bonuses_added else 0, 
                "bonus", locale=user.lang or 'en'
            )
        ) if invoice.loyalty_bonuses_added else ""
        
        loyalty_discount_header = await f("loyalty discount header", lang)
        loyalty_info_text = await f("loyalty info text", lang)
        sum_amount_text = await f("sum amount text", lang)
        sum_to_pay_text = await f("sum to pay text", lang)
        payed_order_text = await f("payed order text", lang)
        invoice_payed_text = await f("invoice payed text", lang)
        invoice_payed_loyalty_message = await f("invoice payed loyalty message to user", lang)
        
        info_data = IncustInfoData(
            percent=percent,
            discount_text=discount_text,
            bonuses_sum_text=bonuses_sum_text,
            loyalty_discount_header=loyalty_discount_header,
            loyalty_info_text=loyalty_info_text,
            sum_amount_text=sum_amount_text,
            sum_to_pay_text=sum_to_pay_text,
            payed_order_text=payed_order_text,
            invoice_payed_text=invoice_payed_text,
            invoice_payed_loyalty_message_to_user=invoice_payed_loyalty_message,
        )

    # Створюємо check_info_data зі збережених даних
    check_info_data = await make_incust_check_info_data_from_invoice(
        invoice, group, lang, invoice.currency, user
    )
    
    return LoyaltyData(
        info_data,
        check_info_data,
    )


async def __delete_invoice_message(
        user_chat_id: int,
        client_bot_token: str,
        invoice_message_id: int | None,
):
    if not invoice_message_id:
        return

    try:
        debugger.debug(f'Try to delete invoice message... {invoice_message_id = }')

        bot = Bot.get_current()
        with bot.with_token(client_bot_token):
            await bot.delete_message(user_chat_id, invoice_message_id)

    except Exception as err:
        logging.error(f'delete invoice message FAILED\n{str(err)}', exc_info=True)

    else:
        debugger.debug('Invoice message deleted Ok')


@dataclass
class LoyaltyData:
    info_data: IncustInfoData | None = None
    check_info_data: IncustCheckInfoData | None = None


async def __notify_user_in_bot(
        user: User,
        client_bot: ClientBot,
        invoice: Invoice,
        message_data: InvoiceMessageData,
        brand_id: int,
        loyalty_data: LoyaltyData,
        lang: str,
        is_payer: bool | None = None,
):
    user_to = ma.get_user_to(user, client_bot.bot_type)
    if not user_to:
        return debugger.debug(
            f"message not sent to user. User {user} has no {client_bot.bot_type} connected"
        )

    if client_bot.bot_type == "telegram":
        user_bot_activity = await UserClientBotActivity.get(
            user, client_bot, create=False,
        )
        if not user_bot_activity or not user_bot_activity.is_entered_bot:
            return debugger.debug(
                f"message not sent to user. User {user} have not entered the telegram yet"
            )

    debugger.debug(
        f'Try to send payed invoice message to user... {user.chat_id = }, {user.wa_phone = }'
    )

    try:

        text = await f(
            message_data.status_text_variable, lang,
            **message_data.dict(),
            loyalty_info=loyalty_data.info_data.loyalty_info_text if loyalty_data and loyalty_data.info_data else "",
        )
        if loyalty_data and loyalty_data.check_info_data and loyalty_data.check_info_data.loyalty_awards_text:
            text += "\n" + loyalty_data.check_info_data.loyalty_awards_text

        if invoice.is_friend:
            if is_payer:
                friend = await User.get_by_id(invoice.user_id)
                text += "\n\n" + await f(
                    "invoice payed message for friend", lang, friend_name=friend.name
                )
            else:
                friend = await User.get_by_id(invoice.payer_id)
                text += "\n\n" + await f(
                    "invoice payed message by friend", lang, friend_name=friend.name
                )

        text = replace_html_symbols(text)

        kwargs = {}
        if client_bot.bot_type == "telegram":
            func = send_tg_message
        else:
            func = send_wa_message
            kwargs["wa_from"] = client_bot.whatsapp_from
            text = html_to_markdown(text)
        try:
            if invoice.photo_url:
                try:
                    await func(
                        user_to,
                        "photo",
                        photo=invoice.photo_url,
                        bot_token=client_bot.token,
                        text=text,
                        **kwargs,
                    )
                except Exception as err:
                    logging.error("Sending pmt message with photo to user error.")
                    logging.error(err, exc_info=True)
                    await func(
                        user_to,
                        "text",
                        text=text,
                        bot_token=client_bot.token,
                        **kwargs,
                    )
        except Exception as e:
            logging.error("Sending message to user error")
            logging.error(e, exc_info=True)

            try:
                await func(
                    user_to,
                    "text",
                    bot_token=client_bot.token,
                    text=await f(
                        "sending payment message to user error",
                        lang, invoice_id=invoice.id,
                    ),
                )
            except Exception as e:
                logging.error(e, exc_info=True)
                is_user_notified_about_error = False
            else:
                is_user_notified_about_error = True

            client_bot_token = client_bot.token
            err_text = (
                "An error occurred while sending user successful payment MESSAGE\n"
                f"{is_user_notified_about_error = }\n"
                f"{invoice.id = }\n"
                f"{user_to = }\n"
                f"{client_bot_token = }\n"
                f"{brand_id = }\n"
                f"{lang = }\n"
            )

            debugger.debug(f"{err_text}\n{message_data = }\n{loyalty_data = }")

            await send_message_to_platform_admins(err_text)
        finally:
            if client_bot.bot_type == "whatsapp":
                to = user.wa_phone
                bot_token = client_bot.token
                whatsapp_from = client_bot.whatsapp_from
                wa_keyboard = await get_wa_menu_keyboard(user, client_bot, lang)

                asyncio.ensure_future(
                    send_delayed_wa_menu(
                        bot_token, lang, to, wa_keyboard, whatsapp_from, 4
                    )
                )

    except Exception as err:
        logging.error(f'send msg about pmt FAILED\n{str(err)}', exc_info=True)
    else:
        debugger.debug('send msg about pmt Ok')


async def __notify_user_by_email(
        invoice: Invoice,
        group: Group,
        message_data: InvoiceMessageData,
        loyalty_data: LoyaltyData,
        lang: str,
        user_chat_id: int | None,
        client_bot_token: str | None,
        is_payer: bool | None = None,
):
    if not is_payer and not invoice.email:
        return

    async def send():
        status_payment = await f("status payed text", lang) if invoice.status == 'payed' \
            else await f("status failed text", lang)

        friend_text = ""
        payer_email = ""
        if invoice.status == 'payed' and invoice.is_friend:
            if is_payer:
                friend = await User.get_by_id(invoice.user_id)
                friend_text = "\n\n" + await f(
                    "invoice payed message for friend", lang, friend_name=friend.name
                )
                payer_email = friend.email
            else:
                friend = await User.get_by_id(invoice.payer_id)
                friend_text = "\n\n" + await f(
                    "invoice payed message by friend", lang, friend_name=friend.name
                )

        template = InvoiceEmailTemplate(
            **message_data.dict(),
            for_header_text=await f("for header text", lang),
            on_amount_text=await f("on amount text", lang),
            sum_amount_header_text=await f(
                "service bot loyalty check sum header", lang
            ),
            invoice_created_text=await f("invoice created text", lang),
            invoice_payed_text=await f("invoice payed text", lang),
            header=await f("payed invoice", lang, invoice_number=invoice.id),
            invoice_id=invoice.id,
            status_payment=status_payment,
            friend_text=friend_text,
        )
        html = await templater.make_template(template, group.id)

        if (
                invoice.loyalty_coupons_data and
                loyalty_data and
                loyalty_data.check_info_data and
                loyalty_data.check_info_data.coupons and
                not payer_email
        ):
            attachments = [coupon.pdf for coupon in loyalty_data.check_info_data.coupons
                           if coupon.pdf]
        else:
            attachments = []

        gmail = GmailClient(cfg.LOC7_EMAIL_LOGIN, cfg.LOC7_EMAIL_PASSWORD)

        subject_type_payed_message = (
            template.type_payed_message
            .replace('<b>', '')
            .replace('</b>', '')
        )
        await send_with_attachments(
            gmail, destination=invoice.email if not payer_email else payer_email,
            subject=f"{subject_type_payed_message} {template.status_payment}",
            html=html, from_name=template.title, attachments=attachments
        )

    try:
        await send()
    except Exception as e:
        logging.error(e, exc_info=True)

        try:
            if user_chat_id and client_bot_token:
                await send_tg_message(
                    user_chat_id, "text", bot_token=client_bot_token, text=await f(
                        "sending success payment email error", lang,
                        email=invoice.email, invoice_id=invoice.id,
                    )
                )
                is_user_notified_about_error = True
            else:
                is_user_notified_about_error = False
        except Exception as e:
            logging.error(e, exc_info=True)
            is_user_notified_about_error = False

        debugger.debug(
            "An error occurred while sending user successful payment EMAIL\n"
            f"{is_user_notified_about_error = }\n"
            f"{invoice.id = }\n"
            f"{invoice.email = }\n"
            f"{message_data = }\n"
            f"{group = }\n"
            f"{loyalty_data = }\n"
            f"{lang = }"
        )

        await send_message_to_platform_admins(
            "An error occurred while sending user successful payment EMAIL\n"
            f"{is_user_notified_about_error = }\n"
            f"{invoice.id = }\n"
            f"{invoice.email = }\n"
            f"{group = }\n"
            f"{lang = }"
        )


async def send_coupons_to_bot_from_invoice_data(
        coupons_data: list,
        user: User, lang: str,
        brand_id: int,
        bot: ClientBot,
):
    """Відправляє купони використовуючи збережені дані з invoice.loyalty_coupons_data."""
    try:
        if not coupons_data:
            return
            
        from schemas.incust.base import CouponShowData
        
        for coupon_dict in coupons_data:
            # Створюємо CouponShowData зі збережених даних invoice
            coupon_data = CouponShowData(
                title=coupon_dict.get('title', ''),
                text=coupon_dict.get('description', ''),
                code=coupon_dict.get('code', ''),
                coupon_id=str(coupon_dict.get('id', '')),
                description=coupon_dict.get('description', ''),
                pdf=None,
                pdf_direct_url=coupon_dict.get('pdf_url', None),
                share_allowed=0,
                type=coupon_dict.get('type', None),
                valid=True,
                locked=False,
                error=None,
                coupon_link=None,
                url=coupon_dict.get('url', None),
                image=coupon_dict.get('image', None),
                pdf_url=coupon_dict.get('pdf_url', None),
            )
            
            await send_coupon_info(
                coupon_data,
                user,
                bot,
                lang,
                brand_id,
            )
            
    except Exception as ex:
        logging.error(f"Error sending coupons from invoice data: {ex}", exc_info=True)


async def send_invoice_push_notifications(
        invoice: Invoice,
        group: Group | None = None,
        invoice_user: User | None = None,
        ignore_session_id: int | None = None,
):
    try:
        assert not group or group.id == invoice.group_id, "Group does not match invoice group"
        assert not invoice_user or invoice_user.id == invoice.user_id, "User does not match invoice user"

        if not group:
            group = await Group.get(invoice.group_id)
        if not invoice_user:
            invoice_user = await User.get_by_id(invoice.user_id)

        async def get_message(user: User):
            texts = await fd(
                {
                    "title": {
                        "variable": (
                            "crm invoice read notification title"
                            if invoice.is_read else
                            "crm invoice paid notification title"
                            if invoice.status == "payed" else
                            "crm invoice new notification title"
                        ),
                        "text_kwargs": {
                            "invoice_id": invoice.id,
                            "total_sum": format_currency(
                                round(invoice.total_sum / 100, 2),
                                invoice.currency, group.lang, group.country
                            )
                        }
                    },
                    "body": {
                        "variable": f"crm invoice notification body",
                        "text_kwargs": {
                            "group_name": group.name,
                            "user_name": invoice_user.name,
                        }
                    },
                },
                user.lang,
            )

            title = texts["title"]
            body = texts["body"] + "\n"

            invoice_items = await crud.get_invoice_items(invoice.id)
            body = add_items_text(body, invoice_items, group.lang, group.currency)

            return build_fcm_message(
                "invoice",
                invoice.id,
                invoice.crm_tag,
                title,
                body,
                delete_notification=invoice.is_read,
                apns_priority="5" if invoice.is_read else "10",
                is_paid=invoice.status == "payed",
                add_data_texts=not invoice.is_read,
                link=f"{CRM_HOST}/invoice/{invoice.id}?listType=inbox&itemIdField=invoiceId"
            )

        return await add_push_notifications_for_action(
            AuthSourceEnum.CRM_WEB, AuthSourceEnum.CRM_APP,
            action="crm_invoice:read",
            available_data={
                "profile_id": invoice.group_id,
                "invoice_id": invoice.id,
            },
            message=get_message,
            ignore_session_id=ignore_session_id,
        )
    except Exception as e:
        group = await Group.get(invoice.group_id)
        invoice_user = await User.get_by_id(invoice.user_id)

        logging.error(
            f"send_invoice_push_notifications FAILED: ({str(e)}", exc_info=True
        )
        await send_message_to_platform_admins(
            f"An error occurred while sending invoice push notifications: {str(e)}\n"
            f"Invoice id: {invoice.id}\n"
            f"Profile: {group.name}({group.id})\n"
            f"Invoice user: {invoice_user.name}({invoice_user.id})\n"
        )
