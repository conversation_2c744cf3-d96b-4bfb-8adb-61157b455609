import json
import logging

from aiogram import types
from aiogram.utils.exceptions import CurrencyTotalAmountInvalid

from core.invoice import show_invoice
from core.invoice.exception import InvoiceInvalidTotalAmountError
from db.models import ClientBot, Group, Invoice, StoreOrder, User, UserAnalyticAction, ObjectPaymentSettings, PaymentSettings
from utils.numbers import format_currency
from utils.text import c, f


async def send_shop_invoice_for_pay(
    order: StoreOrder, 
    payment_settings_id: int = None, 
    object_payment_settings_id: int = None
):
    payload = c("pay_for_invoice", order_id=order.id, invoice_id=order.invoice.id)
    
    # Передаємо параметри безпосередньо в show_invoice
    return await show_invoice(
        order.invoice, 
        payload,
        payment_settings_id=payment_settings_id,
        object_payment_settings_id=object_payment_settings_id
    )


def add_check_url_to_invoice(invoice: Invoice, successful_payment: types.SuccessfulPayment) -> str:
    check_url = None
    if hasattr(successful_payment, 'provider_payment_charge_id'):
        try:
            check_url = json.loads(successful_payment.provider_payment_charge_id).get('gnk_link', None)
        except Exception as err:
            logging.error(
                f'get json from successful_payment FAILED\n{successful_payment.provider_payment_charge_id=}\n{err}'
            )
        if check_url:
            invoice.check_url = check_url

    return check_url


async def send_invoice_from_deep_link(
    message: types.Message,
    invoice_id: str,
    payment_bot: ClientBot,
    lang: str,
    payment_settings_id: int = None,
    object_payment_settings_id: int = None,
):
    invoice = await Invoice.get(invoice_id) if invoice_id.isdigit() else await Invoice.get(uuid_id=invoice_id)
    if not invoice:
        return await message.answer(await f("payment invoice not found error", lang))

    if invoice.status == "payed":
        number_text = await f("invoice number header", lang, invoice_id=invoice.id)
        payed_text = await f("invoice is already paid", lang)
        message_text = "\n".join([number_text, payed_text])
        return await message.answer(message_text)

    payload = c("pay_for_invoice", invoice_id=invoice.id)

    user = None
    if payment_bot.bot_type == "telegram":
        user = await User.get(message.from_user.id)
    elif payment_bot.bot_type == "whatsapp":
        user = await User.get_by_wa_phone(wa_phone=message.from_user.id)
    if not user:
        return await message.answer(await f("user not found error", lang))

    invoice.user_id = user.id
    invoice.bot_id = payment_bot.id

    try:
        # Передаємо параметри безпосередньо в show_invoice
        await show_invoice(
            invoice, 
            payload, 
            lang=lang,
            payment_settings_id=payment_settings_id,
            object_payment_settings_id=object_payment_settings_id
        )
    except (InvoiceInvalidTotalAmountError, CurrencyTotalAmountInvalid):
        group = await Group.get(invoice.group_id)
        return await message.answer(
            await f(
                "minimum amount required error text", lang,
                payment_error=format_currency(invoice.raw_total_amount / 100, invoice.currency, group.lang)
            )
        )
    except Exception as error:
        logging.error(f"{error}", exc_info=True)
        return await message.answer(await f("payment unknown error", lang))

    if payment_bot.bot_type == "telegram":
        await UserAnalyticAction.save_link_following(
            message.from_user.id,
            payment_bot,
            "pay_external_invoice" if isinstance(invoice_id, str) else "pay_invoice_from_payment_bot",
            invoice_id=invoice.id,
        )
