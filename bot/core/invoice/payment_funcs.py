import logging
from datetime import datetime, timezone
from typing import Any

from aiohttp import ClientSession
from incust_terminal_api_client.api.check_transactions_api import CheckTransactionsApi
from incust_terminal_api_client.api.customer_benefits_api import CustomerBenefitsApi
from incust_terminal_api_client.models.customer_benefits_top_up_operation_data import \
    CustomerBenefitsTopUpOperationData
from incust_terminal_api_client.models.incust_controllers_term_api_finalize_check_request import \
    IncustControllersTermApiFinalizeCheckRequest

import schemas
from core.ext.adapters.get_order.get_order_funcs import send_order_to_get_order
from core.ext.adapters.poster import send_order_to_poster
from core.ext.types import ExternalType
from core.kafka.producer.functions import add_invoice_payment_notification
from core.loyalty.incust_client_adapter import run_with_incust_terminal_api
from db.models import (
    Brand, EWallet, Group, Invoice, LoyaltySettings, MenuInStore,
    Payment, Store,
    StoreOrder, User,
)
from schemas import (
    InvoiceTypeEnum, PaymentCallBackData,
)
from utils.platform_admins import send_message_to_platform_admins
from .exception import InvoicePaymentNotFoundError
from .invoice_to_schema import invoice_to_schema
from .loyalty_helpers import get_loyalty_settings_for_invoice

debugger = logging.getLogger('debugger.payments')


async def export_to_external_source(order: StoreOrder, store: Store):
    if store.external_type == ExternalType.GET_ORDER.value:
        await send_order_to_get_order(order, store)
    elif store.external_type == ExternalType.POSTER.value:
        await send_order_to_poster(order, store)


async def get_payment_by_payment_data(payment_data: dict) -> Payment:
    payment = None
    if payment_data.get('payment_uuid'):
        payment = await Payment.get(uuid_id=payment_data.get('payment_uuid'))
    elif payment_data.get('external_payment_id'):
        payment = await Payment.get(external_id=payment_data.get('external_payment_id'))

    if not payment:
        raise InvoicePaymentNotFoundError(payment_data)
    return payment


async def save_payment_data(payment: Payment, payment_data: PaymentCallBackData):
    payment.payment_method = payment_data.payment_method

    if payment_data.external_id:
        payment.external_id = payment_data.external_id
    if payment_data.card_type:
        payment.card_type = payment_data.card_type
    if payment_data.card_mask:
        payment.card_mask = payment_data.card_mask
    if payment_data.payment_settings_id:
        payment.payment_settings_id = payment_data.payment_settings_id
    if payment_data.is_sandbox:
        payment.is_sandbox = payment_data.is_sandbox

    await payment.save_pmt_data(
        {
            payment_data.payment_method: {
                "pmt_callback": payment_data.callback_data,
                "callback_date": f"{datetime.now(timezone.utc):%Y-%m-%d %H:%M:%S}"
            }
        }
    )
    debugger.debug('callback data saved')


async def finalize_incust_loyalty(
        invoice: Invoice,
        brand: Brand,
        store_id: int | None = None,
):
    """Фіналізує транзакцію лояльності через нові клієнти InCust."""
    debugger.debug("finalize_incust_loyalty:")
    try:
        user = await User.get_by_id(invoice.user_id)
        lang = await user.get_lang()

        # Отримуємо LoyaltySettings для інвойса
        loyalty_settings = await get_loyalty_settings_for_invoice(invoice)
        if not loyalty_settings:
            debugger.debug("No loyalty settings – nothing to finalize")
            return

        # 1. Фіналізація транзакції, якщо інвойс має збережений transaction_id
        txn_id = invoice.incust_transaction_id
        if txn_id:
            finalize_req = IncustControllersTermApiFinalizeCheckRequest(id=txn_id)
            try:
                await run_with_incust_terminal_api(
                    loyalty_settings,
                    CheckTransactionsApi.incust_controllers_term_api_finalize_check,
                    finalize_req,
                )
            except Exception as e:
                logging.error(
                    f"Finalize loyalty transaction error for invoice {invoice.id}: {e}",
                    exc_info=True,
                )

        # 2. Поповнення спеціального рахунку (TOPUP_ACCOUNT)
        if invoice.invoice_type == InvoiceTypeEnum.TOPUP_ACCOUNT:
            if not invoice.ewallet_id:
                raise InvoicePaymentNotFoundError(invoice)
            
            ewallet = await EWallet.get(invoice.ewallet_id, is_deleted=False, is_enabled=True)
            if not ewallet or not ewallet.incust_account_id:
                raise InvoicePaymentNotFoundError(invoice)
            
            # Отримуємо зовнішній ID користувача для InCust
            if not user.incust_external_id:
                raise InvoicePaymentNotFoundError(invoice)
            
            # Створюємо дані для топапу через новий API
            topup_data = CustomerBenefitsTopUpOperationData(
                amount=(invoice.before_loyalty_sum / 100),  # Конвертуємо з копійок у основну валюту
                special_account_id=ewallet.incust_account_id,
                id=user.incust_external_id,
                id_type="external-id",
                type="special-account",
                skip_message=False,
            )
            
            try:
                await run_with_incust_terminal_api(
                    loyalty_settings,
                    CustomerBenefitsApi.incust_controllers_term_api_charge_benefits_ex,
                    topup_data,
                )
                debugger.debug(f"Successfully topped up account for invoice {invoice.id}")
            except Exception as e:
                logging.error(
                    f"Top-up account error for invoice {invoice.id}: {e}",
                    exc_info=True,
                )
                raise InvoicePaymentNotFoundError(invoice) from e

    except Exception as ex:
        logging.error(ex, exc_info=True)
        err_text = str(ex)
        err_text += f"\nInvoice_id: {invoice.id}"
        await send_message_to_platform_admins(err_text)


async def send_invoice_webhook_if_exists(invoice: Invoice) -> dict[str, Any] | None:
    if not invoice.successful_payment_callback_url:
        return None

    try:
        group = await Group.get(invoice.group_id)
        invoice_schema = await invoice_to_schema(invoice, group.lang, group)
        async with ClientSession() as session:
            async with session.post(
                    invoice.successful_payment_callback_url,
                    data=invoice_schema.json()
            ) as resp:
                try:
                    text = await resp.text()
                except Exception as e:
                    text = "Error: " + str(e)
        return {
            "http_status": resp.status,
            "body": text,
            "sending_error": None,
        }

    except Exception as e:
        debugger.debug(
            e, exc_info=True
        )  # writing to debug due to error can be only with external service
        return {
            "http_status": "Unknown",
            "body": None,
            "error": str(e)
        }


async def finish_process_full_bonus_payment(
        group: Group,
        brand: Brand,
        incust_service: IncustService,
        invoice: Invoice,
        no_error: bool = True,
        terminal_key: str | None = None,
) -> bool:
    incust_check = schemas.IncustCheckSchema(**invoice.incust_check)

    try:
        result, err_msg = await (
            incust_service.one_step_incust_loyalty_transaction_finalize(
                incust_check, invoice
            ))

        if result and not err_msg:
            await invoice.update(
                status="payed",
                payer_id=invoice.user_id,
            )
            
            # Відправляємо повідомлення про успішну оплату через Kafka
            await add_invoice_payment_notification(
                group_id=group.id,
                brand_id=brand.id if brand else None,
                invoice_id=invoice.id,
                invoice_user_id=invoice.user_id,
                is_full_bonuses_payment=True,
                terminal_key=terminal_key,
                lang=incust_service.lang if incust_service else group.lang,
                menu_in_store_id=invoice.menu_in_store_id,
            )
            return True
    except IncustError as ex:
        debugger.error(ex, exc_info=True)

        err_text = ex.msg_text
        err_text += f"\nInvoice_id: {invoice.id}"

        await send_message_to_platform_admins(err_text)

        if not no_error:
            raise

    return False



