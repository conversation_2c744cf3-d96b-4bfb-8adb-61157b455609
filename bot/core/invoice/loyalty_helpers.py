"""
Helper functions for invoice loyalty operations.
"""

from db.models import Group, Invoice, LoyaltySettings, MenuInStore


async def get_loyalty_settings_for_invoice(invoice: Invoice) -> LoyaltySettings | None:
    """Отримує налаштування лояльності для рахунку.
    Порядок пошуку:
    1. За invoice.loyalty_settings_id
    2. За всіма ідентифікаторами з інвойса через LoyaltySettings.get_loyalty_settings_for_context
    """
    # 1. За прямим посиланням
    if invoice.loyalty_settings_id:
        ls = await LoyaltySettings.get(invoice.loyalty_settings_id)
        if ls and ls.is_enabled:
            return ls

    # 2. Контекстний пошук
    store_id = None
    if invoice.store_order:
        store_id = invoice.store_order.store_id
    elif invoice.menu_in_store_id:
        menu_in_store = await MenuInStore.get(invoice.menu_in_store_id)
        if menu_in_store and menu_in_store.store_id:
            store_id = menu_in_store.store_id

    # Отримуємо brand_id з рахунку
    group = await Group.get(invoice.group_id)
    brand_id = group.brand_id if group else None

    return await LoyaltySettings.get_loyalty_settings_for_context(
        group_id=invoice.group_id,
        brand_id=brand_id,
        store_id=store_id,
        invoice_template_id=invoice.invoice_template_id,
        ewallet_id=invoice.ewallet_id,
    )
