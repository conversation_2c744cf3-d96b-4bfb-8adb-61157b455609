from typing import Any, Awaitable

from aiogram.dispatcher import FSMContext
from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel, Field, validator

import schemas
from core.incust.helpers import (
    get_currency_from_store_or_brand,
    make_incust_check_items_for_invoice,
)
from core.invoice.invoice_process.exceptions import \
    InvoiceProcessorAmountIsNotEnteredError
from core.invoice.invoice_process.state import InvoiceProcess
from db import crud
from db.models import Brand, Group, InvoiceTemplate, InvoiceTemplateItem, MenuInStore, User
from schemas import (
    IncustCheckPayloadSchema, IncustCheckSchema, InvoicePaymentModeEnum,
    InvoiceTypeEnum,
)


class InvoiceProcessorData(BaseModel):
    class Config:
        allow_population_by_field_name = True

    payment_mode: InvoicePaymentModeEnum = Field(alias="ip_payment_mode")
    invoice_type: InvoiceTypeEnum = Field(alias="ip_invoice_type")

    invoice_template_id: int | None = Field(None, alias="ip_invoice_template_id")
    menu_in_store_id: int | None = Field(None, alias="ip_menu_in_store_id")

    count: int = Field(1, alias="ip_count")
    amount: float | None = Field(None, alias="ip_amount")
    bonuses_redeem_amount: float | None = Field(None, alias="ip_bonuses_redeem_amount")
    product_code: str | None = Field(None, alias="ip_product_code")

    incust_check: IncustCheckSchema | None = Field(None, alias="ip_incust_check")

    @validator("payment_mode")
    def validate_payment_mode(cls, v):
        assert v != InvoicePaymentModeEnum.STORE_ORDER, \
            "payment_mode STORE_ORDER is not supported here"
        return v

    @validator("invoice_type")
    def validate_invoice_type(cls, v):
        assert v == InvoiceTypeEnum.MENU_IN_STORE, f"invoice_type {v} is not supported here"
        return v

    @validator("invoice_template_id")
    def validate_invoice_template_id(cls, v, values):
        assert values.get("payment_mode") != InvoicePaymentModeEnum.TEMPLATE or v, \
            "invoice_template_id is required when payment_mode is TEMPLATE"
        return v

    @validator("menu_in_store_id")
    def validate_menu_in_store_id(cls, v, values):
        assert values.get("invoice_type") != InvoiceTypeEnum.MENU_IN_STORE or v, \
            "menu_in_store_id is required when invoice_type is MENU_IN_STORE"
        return v


class InvoiceProcessorState:
    invoice_template: Awaitable[InvoiceTemplate | None]
    is_items: Awaitable[bool]
    items: Awaitable[list[InvoiceTemplateItem] | None]
    menu_in_store: Awaitable[MenuInStore | None]
    group_id: Awaitable[int]
    group: Awaitable[Group]
    brand: Awaitable[Brand]

    def __init__(self, data: InvoiceProcessorData):
        self._data = data
        self._cache: dict[str, Any] = {}

    @property
    def data(self):
        return self._data

    def set_data(self, new_data: InvoiceProcessorData):
        if not isinstance(new_data, InvoiceProcessorData):
            raise ValueError("new_data must be an instance of InvoiceProcessorData")
        old_data = self.data
        self._data = new_data

        for key, value in old_data.dict().items():
            if key.endswith("_id") and value != getattr(new_data, key):
                self._cache.pop(key[:-3], None)
                self._cache.pop("group")

    async def cached(self, item: str):
        if item in self._cache:
            return self._cache[item]

        match item:
            case "invoice_template":
                self._cache["invoice_template"] = await InvoiceTemplate.get(self.data.invoice_template_id)
            case "menu_in_store":
                self._cache["menu_in_store"] = await MenuInStore.get(self.data.menu_in_store_id)
            case "is_items":
                invoice_template_id = self.data.invoice_template_id
                self._cache["is_items"] = invoice_template_id and await crud.get_invoice_template_items(
                    invoice_template_id, "exists"
                )
            case "items":
                invoice_template_id = self.data.invoice_template_id
                self._cache["items"] = await crud.get_invoice_template_items(
                    invoice_template_id
                ) if invoice_template_id else None
            case "product_code":
                invoice_template_id = self.data.invoice_template_id
                if not invoice_template_id:
                    return None
                invoice_template = await InvoiceTemplate.get(invoice_template_id)
                self._cache["product_code"] = invoice_template.product_code
            case "group_id":
                self._cache["group_id"] = (await self.menu_in_store).group_id
            case "group":
                self._cache["group"] = await Group.get(await self.group_id)
            case "brand":
                self._cache["brand"] = await Brand.get(group_id=await self.group_id)
            case _:
                raise AttributeError(f"Unknown item {item}")

        return self._cache[item]

    @classmethod
    async def init(cls, data: InvoiceProcessorData):
        return cls(data)

    def __getattr__(self, item):
        return self.cached(item)


class InvoiceProcessor:

    def __init__(
            self,
            state: FSMContext,
    ):
        self._fsm_state = state
        self._state: InvoiceProcessorState | None = None
        self._closed: bool = False

    @property
    def state(self):
        if self._closed:
            raise RuntimeError("InvoiceProcessor is closed")

        if not self._state:
            raise RuntimeError("InvoiceProcessor is not initialized")
        return self._state

    async def update_state_data(self, __new_data: dict[str, Any] | None = None, **kwargs):
        if not __new_data:
            __new_data = {}

        new_data = InvoiceProcessorData(
            **{
                **self.state.data.dict(by_alias=True),
                **__new_data,
                **kwargs
            }
        )
        await self._fsm_state.update_data(jsonable_encoder(new_data, exclude_defaults=True, by_alias=True))
        self.state.set_data(new_data)
        return self.state.data

    async def init(self, initial_data: InvoiceProcessorData | None = None, ):
        if initial_data:
            data = initial_data
        else:
            data = InvoiceProcessorData(**await self._fsm_state.get_data())
        self._state = await InvoiceProcessorState.init(data)

    async def __aenter__(self):
        await self.init()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        self._closed = True

    async def prepare_incust_check_items(self, lang: str):
        check_items = await make_incust_check_items_for_invoice(
            self.state.data.amount,
            await self.state.items,
            self.state.data.count,
            self.state.data.product_code,
            lang,
        )
        if not check_items:
            raise InvoiceProcessorAmountIsNotEnteredError()
        return check_items

    async def process_check(self, user: User, brand: Brand, lang: str, terminal_id: int | None = None):
        from db.models.store.loyalty_settings import LoyaltySettings
        from core.loyalty.incust_client_adapter import run_with_incust_terminal_api
        from core.incust.schema_converter import convert_payload_to_check, convert_check_to_schema
        
        menu_in_store = None
        if self.state.data.menu_in_store_id:
            menu_in_store = await MenuInStore.get(self.state.data.menu_in_store_id)
        currency = await get_currency_from_store_or_brand(
            menu_in_store=menu_in_store, 
            group_id=brand.group_id,
            invoice_template=await self.state.invoice_template,
        )

        check_items = await self.prepare_incust_check_items(lang)
        amount = sum(map(lambda x: x.amount, check_items))

        check = IncustCheckPayloadSchema(
            id=user.incust_external_id,
            id_type="external-id",
            amount=amount,
            bonuses_redeemed_amount=self.state.data.bonuses_redeem_amount,
            payment_type="currency",
            payment_id=currency,
            manual_rules=False,
            check_items=check_items,
        )

        # Отримуємо налаштування лояльності
        loyalty_settings = await LoyaltySettings.get_loyalty_settings_for_context(
            brand_id=brand.id,
            invoice_template_id=self.state.data.invoice_template_id,
            group_id=brand.group_id,
        )
        
        if not loyalty_settings:
            raise Exception("Loyalty settings not found")

        # Конвертуємо стару схему в нову
        check_data = convert_payload_to_check(check, user=user)
        
        # Використовуємо новий клієнт
        from incust_terminal_api_client.api.check_transactions_api import CheckTransactionsApi
        
        result_check = await run_with_incust_terminal_api(
            loyalty_settings,
            CheckTransactionsApi.incust_controllers_term_api_terminal_process_check,
            body=check_data,
        )
        
        if not result_check:
            raise Exception("Failed to process check")
        
        # Конвертуємо назад в стару схему
        processed_check = convert_check_to_schema(result_check)
        
        await self.update_state_data(incust_check=processed_check)

        return processed_check

    @classmethod
    async def start(cls, state: FSMContext, data: InvoiceProcessorData):
        await state.finish()
        instance = cls(state)
        await instance.init(data)
        if (
                instance.state.data.amount and
                instance.state.data.payment_mode == schemas.InvoicePaymentModeEnum.ENTERED_AMOUNT
        ):
            await InvoiceProcess.Confirmation.set()
        else:
            await InvoiceProcess.PaymentData.set()
        await state.update_data(jsonable_encoder(data, by_alias=True))
        return instance
