from aiogram.dispatcher import FSMContext
from psutils.forms.exceptions import CancelField

from ..callback_data import SetCountCallbackData
from ..invoice_processor import InvoiceProcessor


async def payment_data_processor(invoice_count: int | None = None, invoice_amount: float | None = None):
    if invoice_count:
        return {"ip_count": invoice_count}
    if invoice_amount:
        return {"ip_amount": invoice_amount}
    return {}


async def set_count_data_processor(set_count: SetCountCallbackData, state: FSMContext):
    async with InvoiceProcessor(state) as processor:
        if not await processor.state.is_items:
            raise CancelField()

        current_count = processor.state.data.count

        match set_count.count:
            case "-":
                new_count = max(current_count - 1, 1)
            case "+":
                new_count = current_count + 1
            case count:
                new_count = count

    return {"count": new_count}
