from gspread import Worksheet
from gspread.utils import rowcol_to_a1

from ..base import BaseColorMixin

from ...cell import Cell


class SheetsColorMixin(BaseColorMixin):

    @property
    def color1(self) -> dict:
        return self.get_color(1, "rgb")

    @property
    def color2(self) -> dict:
        return self.get_color(2, "rgb")

    @staticmethod
    def __get_format(
            background_color: dict,
            foreground_color: dict,
    ) -> dict:
        format_ = {}
        if background_color:
            format_.update(backgroundColor=background_color)
        if foreground_color:
            text_format = {}
            text_format.update(foregroundColor=foreground_color)
            format_.update(textFormat=text_format)
        return format_

    @staticmethod
    def address(row_id: int | str, col_id: int | str) -> str:
        return rowcol_to_a1(row_id, col_id)

    async def format_cells(
            self, sheet: Worksheet,
            cells: list[Cell],
            background_color: dict = None,
            foreground_color: dict = None,
    ):
        if not any([background_color, foreground_color]):
            return

        format_ = self.__get_format(background_color, foreground_color)

        addresses = [self.address(cell.row_id, cell.col_id) for cell in cells]
        sheet.format(addresses, format_)
