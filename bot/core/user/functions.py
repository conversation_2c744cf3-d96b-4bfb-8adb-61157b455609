import logging
from typing import Any

from config import ANONYMOUS_USER_ID, DEFAULT_LANG, USER_CART_ACCESS_TOKEN_EXPIRES
from core import messangers_adapters as ma
from core.auth.functions import create_user_access_token
from core.custom_texts import ct
from core.user.exceptions import (
    CreateGuestUserWithEmailExistError, CreatingUserForTelegramChatError,
    InvalidObjectForCreateOrUpdateUser, SavingUserPhotoError,
)
from core.user.types import CreateOrUpdateMessangerUserInfo
from db import crud
from db.models import ClientBot, Group, User, UserClientBotActivity
from utils.localisation import localisation
from utils.message import send_tg_message
from utils.qrcode import generate_qr_code
from utils.translator import Translator

logger = logging.getLogger("debugger")


async def create_or_update_messanger_user(
        obj: ma.AnswerObject | ma.User | str | int,
        bot: ClientBot | int | None = None,
        return_is_created: bool = False,
        need_set_is_accepted_agreement: bool = False,
        allowed_create_user: bool = True,
) -> User | CreateOrUpdateMessangerUserInfo:
    if not bot and ClientBot.get_current_bot_id():
        bot = await ClientBot.get_current()

    elif not isinstance(bot, ClientBot):
        bot = await ClientBot.get(bot)

    if isinstance(obj, ma.wa.types.message.AnswerObject):
        obj = obj.user

    if isinstance(obj, ma.wa.types.User):
        user_kwargs = {
            "wa_name": obj.name,
            "wa_phone": obj.phone_number
        }
    else:
        user_kwargs = __calculate_tg_user_kwargs(obj)

    bot_group = await Group.get(bot.group_id) if bot else None

    if isinstance(obj, ma.tg.types.User) and obj.is_bot is False:
        lang = obj.language_code
    elif isinstance(obj, ma.wa.types.Message) and obj.from_user.is_bot is False:
        lang = obj.from_user.language_code
    else:
        lang = bot_group.lang if bot_group else DEFAULT_LANG

    if not bot_group:
        available_langs_list = await localisation.langs
    elif bot_group.is_translate and bot_group.allow_all_google_langs:
        available_langs_list = (
            await Translator.get_supported_languages(DEFAULT_LANG)).keys()
    else:
        available_langs_list = bot_group.get_langs_list()

    user_info = await crud.create_or_update_messanger_user(
        lang, available_langs_list, bot, **user_kwargs,
        need_set_is_accepted_agreement=need_set_is_accepted_agreement,
        allowed_create_user=allowed_create_user,
    )

    if user_info.user and \
            user_info.user.chat_id and \
            isinstance(user_info.user_bot_activity, UserClientBotActivity) and \
            user_info.user_bot_activity.is_entered_bot is True:
        await save_user_photo(user_info.user)

    return user_info if return_is_created else user_info.user


def __calculate_tg_user_kwargs(
        obj: ma.tg.types.Message | ma.tg.types.CallbackQuery | ma.tg.types.User | str
             | int,
) -> dict[str, Any]:
    telegram_user: ma.tg.types.User | ma.tg.types.Chat | None

    user_kwargs = dict()

    if isinstance(obj, ma.tg.types.User):
        telegram_user = obj
    elif isinstance(obj, str):
        telegram_user = None
        user_kwargs.update(username=obj)
    elif isinstance(obj, int):
        telegram_user = None
        user_kwargs.update(chat_id=obj)
    elif obj.left_chat_member:
        telegram_user = obj.left_chat_member
    elif hasattr(obj, "from_user") and hasattr(
            obj, "chat"
    ) and obj.from_user.is_bot is True:
        if obj.chat.type != "private":
            raise CreatingUserForTelegramChatError(obj.to_python())

        telegram_user = obj.chat
    elif hasattr(obj, "from_user"):
        telegram_user = obj.from_user
    else:
        raise InvalidObjectForCreateOrUpdateUser(obj)

    if telegram_user:
        user_kwargs.update(
            chat_id=telegram_user.id,
            username=telegram_user.username,
            first_name=telegram_user.first_name,
            last_name=telegram_user.last_name,
            full_name=telegram_user.full_name,
        )

    return user_kwargs


async def save_user_photo(user: User):
    try:
        telegram_bot = ma.tg.Bot.get_current()
        if not telegram_bot:
            logger.debug(f"Telegram bot for {user=} not found...")
            return

        photos_data = await telegram_bot.get_user_profile_photos(user.chat_id, limit=1)
        photos = photos_data.photos
        if photos:
            photo = photos[0][-1]
            photo_info: ma.tg.types.File = await photo.get_file()
            await user.set_photo(photo_info.file_path, photo_info.file_unique_id)

    except Exception as e:
        raise SavingUserPhotoError() from e


def generate_user_cart_token(user: User):
    if user.status != "active":
        raise ValueError("unable generate user cart token")

    return create_user_access_token(
        user.id, expire=USER_CART_ACCESS_TOKEN_EXPIRES,
        scopes=["me:read", "makeInvoice"]
    )


async def send_user_cart_tg(
        user: User,
        bot: ClientBot,
        lang: str,
) -> ma.tg.types.Message:
    cart_token = generate_user_cart_token(user)

    qr = generate_qr_code(cart_token)
    return await send_tg_message(
        user.chat_id, "photo", photo=qr, text=await ct(
            bot, lang,
            "main", "my qr", "message_text",
        )
    )


async def create_guest_user(
        email: str,
        first_name: str | None = None,
        last_name: str | None = None,
        lang: str | None = None,
        is_accepted_agreement: bool = False,
):
    if not email:
        raise ValueError("email is required")

    user = await User.get_by_email(email)
    if not user:
        user = await crud.create_user(
            email,
            first_name=first_name,
            last_name=last_name,
            is_accepted_agreement=is_accepted_agreement,
            is_guest_user=True,
            lang=lang,
        )

    if not user or not user.is_guest_user:
        raise CreateGuestUserWithEmailExistError()
    return user


def not_anonym(user_id: int):
    """Returns user_id if it is not anonymous user"""
    if user_id != ANONYMOUS_USER_ID:
        return user_id
    return None


async def detect_messanger_user_lang(
        obj: ma.types.Message | ma.types.ButtonQuery | ma.User,
        bot: ClientBot,
        user: User | None = None,
):
    if user:
        return await user.get_lang(bot)
    bot_group = await Group.get(bot.group_id)
    if bot_group.is_translate and bot_group.allow_all_google_langs:
        langs_list = list(
            (await Translator.get_supported_languages(
                DEFAULT_LANG
            )).keys()
        )
    else:
        langs_list = bot_group.get_langs_list()

    if isinstance(obj, ma.tg.types.CallbackQuery | ma.tg.types.Message):
        obj = obj.from_user

    if isinstance(obj, ma.tg.types.User):
        lang = obj.language_code
    else:
        lang = None

    if lang and lang in langs_list:
        return lang

    return bot_group.lang
