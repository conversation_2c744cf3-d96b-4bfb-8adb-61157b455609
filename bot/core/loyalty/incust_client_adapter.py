"""
Адаптер для роботи з InCust API клієнтами.
Забезпечує правильну конфігурацію клієнтів та управління токенами користувачів.
"""
import logging
import uuid
from typing import Awaitable, Callable, Optional, TypeVar

from incust_client_api_client import (
    ApiClient as IncustApiClient, ApiException as ClientApiException,
    Configuration as IncustConfiguration,
)
from incust_terminal_api_client import (
    ApiClient as TerminalApiClient, ApiException as TerminalApiException,
    Configuration as TerminalConfiguration,
)
from incust_terminal_api_client.models import Customer, IdType, UserIdentifier
from sqlalchemy.exc import IntegrityError

from config.config import USER_VERIFICATION_TOKEN_EXPIRE
from db.models import IncustCustomer, LoyaltySettings, User
from db.sync_to_async import async_commit, async_rollback
from utils.jwt_token import create_jwt_token

logger = logging.getLogger("debugger.loyalty.incust_client_adapter")

T = TypeVar("T")
R = TypeVar("R")

# Глобальні конфігурації для кешування
_client_configs = {}
_terminal_configs = {}

MAX_AUTH_RETRIES = 2


def _get_config_key(settings: LoyaltySettings, client_type: str) -> str:
    """Генерує унікальний ключ для кешування конфігурації."""
    return f"{client_type}_{settings.brand_id}_{settings.white_label_id or 'default'}"


def _get_client_config(settings: LoyaltySettings, lang: str = "en") -> IncustConfiguration:
    """Отримує або створює конфігурацію для Client API."""
    config_key = _get_config_key(settings, "client")
    
    if config_key not in _client_configs:
        api_path = "/v1/wlclient" if settings.white_label_id else "/v1/client"
        config = IncustConfiguration(host=settings.server_url + api_path)
        if not hasattr(config, 'api_key_prefix') or config.api_key_prefix is None:
            config.api_key_prefix = {}
        config.api_key_prefix['Accept-Language'] = lang
        _client_configs[config_key] = config
    return _client_configs[config_key]


def _get_terminal_config(settings: LoyaltySettings) -> TerminalConfiguration:
    """Отримує або створює конфігурацію для Terminal API."""
    config_key = _get_config_key(settings, "terminal")
    
    if config_key not in _terminal_configs:
        config = TerminalConfiguration(host=settings.server_url + "/v1/term")
        _terminal_configs[config_key] = config
    return _terminal_configs[config_key]


async def get_or_create_incust_customer(
    user: User,
    settings: LoyaltySettings,
    lang: str = "en"
) -> Optional[IncustCustomer]:
    """
    Отримує або створює InCust клієнта для користувача.
    Це ключова бізнес-логіка для роботи з InCust API.
    """
    if not user:
        raise ValueError("User is required for get_or_create_incust_customer")

    brand_id = settings.brand_id
    if not brand_id:
        raise ValueError("Cannot determine brand_id from loyalty settings")

    # Шукаємо існуючого клієнта
    incust_customer: Optional[IncustCustomer] = await IncustCustomer.get(
        user_id=user.id,
        brand_id=brand_id,
    )
    
    if incust_customer:
        # Тестуємо чи працює токен
        try:
            from incust_client_api_client.api.card_info_api import CardInfoApi
            
            config = _get_client_config(settings, lang)
            config.api_key = {'api_key': incust_customer.token}
            
            async with IncustApiClient(configuration=config) as client:
                api = CardInfoApi(client)
                await api.incust_controllers_client_client_card_info()
                
            logger.debug(f"Found existing InCust customer for user {user.id}")
            return incust_customer
            
        except (ClientApiException, Exception) as e:
            logger.warning(f"InCust customer token invalid for user {user.id}: {e}")
            await incust_customer.delete()
            incust_customer = None
    
    # Створюємо нового клієнта
    if not incust_customer:
        try:
            # Створюємо JWT токен
            jwt_token: str = create_jwt_token(
                data={
                    "sub": f"{user.id}",
                    "type": "user", 
                    "scopes": ["me:read"],
                    "brand_id": brand_id,
                },
                expire=USER_VERIFICATION_TOKEN_EXPIRE
            )

            # Підготовуємо ідентифікатори
            user_identifiers = []

            if not user.incust_external_id:
                await user.update(incust_external_id=uuid.uuid4().hex)
            
            user_identifiers.append(UserIdentifier(
                type_=IdType.EXTERNAL_ID,
                code=user.incust_external_id
            ))

            if user.phone:
                user_identifiers.append(UserIdentifier(
                    type=IdType.PHONE,
                    code=user.phone
                ))

            if user.email:
                user_identifiers.append(UserIdentifier(
                    type=IdType.EMAIL,
                    code=user.email
                ))
            
            # Створюємо Customer об'єкт
            customer_data = Customer(
                identification=user_identifiers,
                name=user.name or user.username,
                language=lang
            )
            
            # Створюємо через Terminal API
            config = _get_terminal_config(settings)
            
            async with TerminalApiClient(configuration=config) as client:
                from incust_terminal_api_client.api.customer_api import CustomerApi
                api = CustomerApi(client)
                created_customer = await api.incust_controllers_term_api_customer_add(customer_data)
            
            if not created_customer:
                raise Exception("Failed to create customer in InCust")
            
            logger.info(f"Created new InCust customer: {created_customer.id}")
            
            # Зберігаємо в БД
            try:
                incust_customer = await IncustCustomer.create(
                    user_or_id=user.id,
                    brand_or_id=brand_id,
                    token=jwt_token
                )
                await async_commit()
                logger.info(f"Created IncustCustomer record for user {user.id}")
            
            except IntegrityError as err:
                await async_rollback()
                if "Duplicate entry" in str(err):
                    incust_customer = await IncustCustomer.get(
                        user_id=user.id,
                        brand_id=brand_id
                    )
                    if incust_customer:
                        logger.info("Found existing IncustCustomer after integrity error")
                        return incust_customer
                raise
                
        except (TerminalApiException, Exception) as e:
            await async_rollback()
            logger.error(f"Failed to create InCust customer for user {user.id}: {e}")
            raise Exception(f"InCust customer creation failed: {str(e)}") from e
    
    return incust_customer


async def run_with_incust_client_api(
    settings: LoyaltySettings,
    user: Optional[User],
    api_method: Callable[..., Awaitable[R]],
    *args,
    lang: str = "en",
    **kwargs
) -> R:
    """
    Виконує виклик до Client API методу з автоматичною конфігурацією.
    
    Args:
        settings: Налаштування лояльності
        user: Користувач для якого робиться запит (може бути None для публічних методів)
        api_method: Метод API класу (наприклад, CardInfoApi.incust_controllers_client_client_card_info)
        *args: Аргументи для методу
        lang: Мова запитів
        **kwargs: Keyword аргументи для методу
        
    Returns:
        Результат виклику API методу
    """
    config = _get_client_config(settings, lang)
    
    # Якщо переданий користувач, отримуємо або створюємо InCust клієнта
    user_token = None
    if user:
        incust_customer = await get_or_create_incust_customer(user, settings, lang)
        if not incust_customer or not incust_customer.token:
            raise ValueError(f"Could not get or create InCust customer for user {user.id}")
        user_token = incust_customer.token
    
    # Виконуємо виклик
    auth_retries = 0
    while True:
        client = None
        try:
            # Створюємо Client API клієнт
            client = IncustApiClient(configuration=config)
            
            # Додаємо токен авторизації, якщо є
            if user_token:
                client.set_default_header("Authorization", f"Bearer {user_token}")
            
            # Додаємо white_label_id, якщо є
            if settings.white_label_id:
                client.set_default_header("X-Application-Id", settings.white_label_id)
            
            # Додаємо мову
            client.set_default_header("Accept-Language", lang)
            
            # Отримуємо API клас з qualname методу
            module_name = api_method.__module__
            qualname = api_method.__qualname__
            class_name = qualname.split('.')[0]
            method_name = qualname.split('.')[1]
            
            # Імпортуємо клас динамічно
            import importlib
            module = importlib.import_module(module_name)
            api_class = getattr(module, class_name)
            
            # Створюємо екземпляр API та викликаємо метод
            api_instance = api_class(client)
            method = getattr(api_instance, method_name)
            
            result = await method(*args, **kwargs)
            
            # Закриваємо клієнт безпечно
            try:
                if hasattr(client, 'close'):
                    await client.close()
                elif hasattr(client, 'rest_client') and hasattr(client.rest_client, 'close'):
                    await client.rest_client.close()
            except (AttributeError, Exception):
                pass
            
            return result
                
        except (ClientApiException, Exception) as error:
            # Безпечно закриваємо клієнт при помилці
            if client:
                try:
                    if hasattr(client, 'close'):
                        await client.close()
                    elif hasattr(client, 'rest_client') and hasattr(client.rest_client, 'close'):
                        await client.rest_client.close()
                except (AttributeError, Exception):
                    pass
            
            if hasattr(error, 'status') and error.status == 401 and auth_retries < MAX_AUTH_RETRIES and user:
                auth_retries += 1
                logger.warning(f"Authentication failed for user {user.id}, retrying... (attempt {auth_retries})")
                # При 401 помилці перестворюємо клієнта
                incust_customer = await get_or_create_incust_customer(user, settings, lang)
                if incust_customer and incust_customer.token:
                    user_token = incust_customer.token
                    continue
            raise


async def run_with_incust_terminal_api(
    settings: LoyaltySettings,
    api_method: Callable[..., Awaitable[R]],
    *args,
    **kwargs
) -> R:
    """
    Виконує виклик до Terminal API методу з автоматичною конфігурацією.
    
    Args:
        settings: Налаштування лояльності
        api_method: Метод API класу (наприклад, CustomerApi.incust_controllers_term_api_customer_add)
        *args: Аргументи для методу
        **kwargs: Keyword аргументи для методу
        
    Returns:
        Результат виклику API методу
    """
    config = _get_terminal_config(settings)
    
    auth_retries = 0
    while True:
        client = None
        try:
            # Створюємо Terminal API клієнт
            client = TerminalApiClient(configuration=config)
            
            # Додаємо токен авторизації
            client.set_default_header("Authorization", f"Bearer {settings.terminal_api_key}")
            
            # Додаємо white_label_id, якщо є
            if settings.white_label_id:
                client.set_default_header("X-Application-Id", settings.white_label_id)
            
            # Отримуємо API клас з qualname методу
            module_name = api_method.__module__
            qualname = api_method.__qualname__
            class_name = qualname.split('.')[0]
            method_name = qualname.split('.')[1]
            
            # Імпортуємо клас динамічно
            import importlib
            module = importlib.import_module(module_name)
            api_class = getattr(module, class_name)
            
            # Створюємо екземпляр API та викликаємо метод
            api_instance = api_class(client)
            method = getattr(api_instance, method_name)
            
            result = await method(*args, **kwargs)
            
            # Закриваємо клієнт безпечно
            try:
                if hasattr(client, 'close'):
                    await client.close()
                elif hasattr(client, 'rest_client') and hasattr(client.rest_client, 'close'):
                    await client.rest_client.close()
            except (AttributeError, Exception):
                pass
            
            return result
                
        except (TerminalApiException, Exception) as error:
            # Безпечно закриваємо клієнт при помилці
            if client:
                try:
                    if hasattr(client, 'close'):
                        await client.close()
                    elif hasattr(client, 'rest_client') and hasattr(client.rest_client, 'close'):
                        await client.rest_client.close()
                except (AttributeError, Exception):
                    pass
            
            if hasattr(error, 'status') and error.status == 401 and auth_retries < MAX_AUTH_RETRIES:
                auth_retries += 1
                logger.warning(f"Terminal API authentication failed, retrying... (attempt {auth_retries})")
                continue
            raise


# Експорт функцій
__all__ = [
    "get_or_create_incust_customer",
    "run_with_incust_client_api", 
    "run_with_incust_terminal_api",
]