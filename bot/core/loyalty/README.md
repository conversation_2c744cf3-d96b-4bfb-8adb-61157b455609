# Система лояльності Pay4Say - Інтеграція з InCust API

Цей модуль забезпечує інтеграцію з InCust API через адаптер, який дозволяє використовувати методи згенерованого клієнта напряму з автоматичною конфігурацією та авторизацією.

## Філософія архітектури

**Не дублюємо методи клієнта - використовуємо їх напряму!**

Замість створення обгорток для кожного API методу, адаптер дозволяє:
1. Імпортувати методи напряму з згенерованого клієнта
2. Передавати їх в адаптер для виконання
3. Автоматично застосовувати налаштування та авторизацію
4. Отримувати результат в оригінальних схемах


Шляхи до нових АПІ клієнтів Інкаст для пошуку реальних існуючих методів:

\home\vik\in-cust-api-client
\home\vik\in-cust-terminal-api-client

Ці клієнти є в віртуальному середовищі проекту і вони додані як пакети і відповідно імпортуються.

в проекті вони доступні в віртуальному середовищі по шляхах:
/home/<USER>/pay4say/.venv/lib/python3.10/site-packages/incust_terminal_api_client
/home/<USER>/pay4say/.venv/lib/python3.10/site-packages/incust_term_api_client

## Основні компоненти

### 1. Адаптер InCust клієнтів
- **`incust_client_adapter.py`** - Головний адаптер для налаштування клієнтів
- **3 основні функції**:
  - `get_or_create_incust_customer()` - Створення/отримання клієнта InCust
  - `run_with_incust_client_api()` - Виконання Client API методів
  - `run_with_incust_terminal_api()` - Виконання Terminal API методів

  Коли викликаються ці методи то ми завжди маємо їх викликати із параметром LoyatySettings отриманим або створеним. Бо в LoyatySettings ми маємо передати урл і ключ АПІ для термінала.

### 2. Налаштування лояльності
- **`LoyaltySettings`** модель для збереження конфігурації
- **`get_loyalty_settings_for_context()`** - каскадна логіка вибору налаштувань
налаштування лояльності для обєктів беремо тільки з LoyaltySettings. Не беремо із самих обєктів. Всі налаштування лояльності ми перенесли із обєктів до LoyaltySettings. ТОбто для продукту, для шаблону , для магазину , для профілю ми завжди беремо налаштування і ЛоялтіСеттінгс де є ключ терміналу і сервер урл і інші налаштування.


### Правила переходу на новий клієнт АПІ інкаст

Якщо ми працюємо із обєктами які повертає АПІ інкасту то ми повинні і використовувати оригінальні схеми цих обєктів від нових клієнтів і там де ми міняємо процеси робити заміну старих схем на оригінальні.

Також при перетворення процесу на нові АПІ інкаст клієнти ти не можеш викидати ту логіку що була. ти повинен міняти тільки звернення до АПІ інкасту на нові клієнти. Обовязково потрібно зберігати логіку яка була реалізована.

 щоб знайти відповідні методи ти повинен дослідити старий виклик topup_customer_account. знайти який атм використовується роут і вже по цьому роуті знайти метод який використовує новий інкаст АПІ клієнт.

 ми зберігаємо транзактіон_ід в інвойсі. також ми зберігаємо і видані купони і рахунки в окрепмих порлях інвойсу коли ми фіналізуємо транзакцію лояльності. тому доцільно використати саме ці значення щоб не робити длишні запити до інкаст АПІ.
 тому нам не потрібно використовувати incvoice.incust_check а використовувати відповідні поля із збереженими купонами і рахунками коли потрібно.

### 3. Приклади використання
- **`usage_examples.py`** - Готові функції для різних сценаріїв

## Використання

### 1. Прямі виклики API методів (РЕКОМЕНДУЄТЬСЯ)

```python
from incust_client_api_client.api.card_info_api import CardInfoApi
from incust_terminal_api_client.api.customer_api import CustomerApi
from core.loyalty.incust_client_adapter import (
    run_with_incust_client_api, 
    run_with_incust_terminal_api,
    get_or_create_incust_customer
)

# Отримання налаштувань лояльності
settings = await LoyaltySettings.get_loyalty_settings_for_context(
    brand_id=brand.id,
    store_id=store.id,
    group_id=group.id
)

# Синхронізація користувача з InCust
customer = await get_or_create_incust_customer(
    user=user,
    settings=settings,
    lang="uk"
)

# Client API - для авторизованих запитів від імені користувача
card_info = await run_with_incust_client_api(
    settings,
    user,
    CardInfoApi.incust_controllers_client_client_card_info,
    lang="uk"
)

# Terminal API - для операцій з чеками та транзакціями
from incust_terminal_api_client.api.check_transactions_api import CheckTransactionsApi

transaction = await run_with_incust_terminal_api(
    settings,
    CheckTransactionsApi.incust_controllers_term_api_make_check_by_rules,
    rules_type="by-all-rules",
    check=check_data
)
```

### 2. Обробка чеків лояльності

```python
from incust_terminal_api_client.models.check import Check
from core.incust.schema_converter import convert_payload_to_check

# Конвертуємо внутрішню схему в формат InCust API
check_data = convert_payload_to_check(receipt_payload, user=user)

# Обробляємо чек через InCust
result_check = await run_with_incust_terminal_api(
    loyalty_settings,
    CheckTransactionsApi.incust_controllers_term_api_terminal_process_check,
    body=check_data
)

# result_check це оригінальний Check об'єкт з InCust API
```

### 3. Робота з купонами

```python
from core.invoice_loyalty.coupons_service import InvoiceLoyaltyCouponsService

coupons_service = InvoiceLoyaltyCouponsService()

# Отримуємо купони з транзакції
coupons = await coupons_service.get_coupons_data_from_transaction(
    transaction_id="trans_123",
    loyalty_settings=settings,
    user=user,
    lang="uk",
    need_pdf_file=True
)
```

### 4. Використання готових функцій

```python
from core.loyalty.usage_examples import get_user_card_info

# Готова функція для отримання інформації про карту
card_info = await get_user_card_info(settings, user, "uk")
```

## Налаштування лояльності - Каскадна логіка

Система підтримує гнучке налаштування лояльності з пріоритетами:

```python
# Пошук найбільш специфічних налаштувань
settings = await LoyaltySettings.get_loyalty_settings_for_context(
    brand_id=1,
    store_id=5,
    group_id=10,
    product_id=15,  # Найвищий пріоритет
    ewallet_id=20
)
```

Порядок пошуку (від найвищого до найнижчого пріоритету):
1. `product_id` - специфічно для товару
2. `store_id` - для магазину
3. `ewallet_id` - для електронного гаманця
4. `brand_id` - для бренду
5. `group_id` - загальні налаштування групи

## Важливі принципи

### 1. Використання оригінальних схем
```python
# ✅ ПРАВИЛЬНО - використовуємо оригінальні схеми InCust
from incust_terminal_api_client.models.check import Check

def process_check(check: Check) -> dict:
    return {
        "amount": check.amount,
        "bonuses": check.bonuses_added_amount,
        "coupons": len(check.emitted_coupons) if check.emitted_coupons else 0
    }

# ❌ НЕПРАВИЛЬНО - не використовуємо застарілі внутрішні схеми
# from schemas import IncustCheckSchema  # Застаріло!
```

### 2. Методи моделей SQLAlchemy
```python
# ✅ ПРАВИЛЬНО - використовуємо вбудовані методи
settings = await LoyaltySettings.get(id)
settings_list = await LoyaltySettings.get_list(brand_id=1)
new_settings = await LoyaltySettings.create(**data)

# ❌ НЕПРАВИЛЬНО - не створюємо зайві CRUD функції
# await crud_get_loyalty_settings(id)  # Непотрібно!
```

### 3. Pydantic v1 в проекті
```python
# ✅ ПРАВИЛЬНО для Pydantic v1
schema = Schema(**model.as_dict())

# ❌ НЕПРАВИЛЬНО - model_validate тільки в Pydantic v2
# schema = Schema.model_validate(model)  # Не працює в v1!
```

### 4. Не вигадувати атрибути
```python
# ✅ ПРАВИЛЬНО - використовуємо існуючі атрибути
loyalty_id = loyalty_settings.loyalty_id

# ❌ КАТЕГОРИЧНО ЗАБОРОНЕНО - вигадувати атрибути
# incust_program_id = loyalty_settings.incust_program_id  # НЕ ІСНУЄ!
```

## Переваги архітектури

### 1. Відсутність дублювання
- Методи клієнта використовуються напряму
- Немає потреби підтримувати обгортки
- Автоматично підтримуються всі методи клієнта

### 2. Оригінальні схеми
- Результати в оригінальних Pydantic моделях InCust
- Повна типізація та валідація
- Сумісність з документацією API

### 3. Автоматизація
- Автоматична конфігурація клієнтів
- Автоматична авторизація та токени
- Retry логіка при помилках авторизації

### 4. Гнучкість налаштувань
- Каскадна логіка вибору налаштувань
- Підтримка різних рівнів конфігурації
- Легке додавання нових контекстів

## Налагодження

Увімкніть детальне логування:
```python
import logging
logging.getLogger("debugger.loyalty.incust_client_adapter").setLevel(logging.DEBUG)
```

## Критичні помилки та їх уникнення

1. **Не використовувати застарілі схеми** - завжди використовувати оригінальні з InCust клієнтів
2. **Не вигадувати атрибути** - тільки існуючі поля моделей
3. **Пам'ятати про Pydantic v1** - використовувати правильні методи
4. **Не дублювати CRUD операції** - використовувати вбудовані методи моделей
5. **Завжди перевіряти існування атрибутів** - через `hasattr()` та `getattr()`


також зверни увагу що в нас не буде IncustError тому нам потрібно буде замінити використання IncustError на спробу обробити і  зловити відповідні відповіді від АПІ інкаст клієнтів для формування правильних винятків.

в нас вже є loyalty_id в LoyaltySettings. Тому щоб отримати лоялті_ід нам не обовязково робити запит на отримання настройок терміналу.
Обовязково перевіряй які насправді є методи в нових АПІ клієнтах.  запамятай це собі на майбутнє, що ти не може придумувати методи. Ти маєш брати методи які реально існують в обєкті.