"""
Сумісний клас LoyaltyClientHelper для збереження існуючих інтерфейсів.
Використовує новий InCust адаптер всередині.
"""

import logging
from contextlib import asynccontextmanager

from incust_client_api_client import ApiClient as IncustApiClient
from incust_terminal_api_client import ApiClient as TerminalApiClient

from db.models import LoyaltySettings, User
from .incust_client_adapter import InCustClientAdapter, get_or_create_incust_customer

logger = logging.getLogger("debugger.loyalty.client_helper")


class LoyaltyClientHelper:
    """
    Сумісний клас для забезпечення зворотної сумісності з існуючим кодом.
    Використовує новий InCust адаптер всередині.
    """
    
    @staticmethod
    @asynccontextmanager
    async def terminal_client(settings: LoyaltySettings):
        """
        Контекст менеджер для Terminal API клієнта.
        Зберігає сумісність з існуючим кодом.
        """
        config = InCustClientAdapter.get_terminal_config(settings)
        client = None
        
        try:
            # Створюємо Terminal API клієнт
            client = TerminalApiClient(configuration=config)
            
            # Додаємо токен авторизації
            client.set_default_header("Authorization", f"Bearer {settings.terminal_api_key}")
            
            # Додаємо white_label_id, якщо є
            if settings.white_label_id:
                client.set_default_header("X-Application-Id", settings.white_label_id)
            
            yield client
            
        finally:
            # Безпечно закриваємо клієнт
            if client:
                try:
                    if hasattr(client, 'close'):
                        await client.close()
                    elif hasattr(client, 'rest_client') and hasattr(client.rest_client, 'close'):
                        await client.rest_client.close()
                except (AttributeError, Exception):
                    # Ігноруємо помилки закриття
                    pass
    
    @staticmethod
    @asynccontextmanager
    async def user_client(settings: LoyaltySettings, user: User, lang: str = "en"):
        """
        Контекст менеджер для Client API клієнта з користувачем.
        Зберігає сумісність з існуючим кодом.
        """
        config = InCustClientAdapter.get_client_config(settings, lang)
        client = None
        
        try:
            # Отримуємо або створюємо InCust клієнта
            incust_customer = await InCustClientAdapter.get_or_create_incust_customer(
                user, settings, lang
            )
            if not incust_customer or not incust_customer.token:
                raise ValueError(f"Could not get or create InCust customer for user {user.id}")
            
            # Створюємо Client API клієнт
            client = IncustApiClient(configuration=config)
            
            # Додаємо токен авторизації
            client.set_default_header("Authorization", f"Bearer {incust_customer.token}")
            
            # Додаємо white_label_id, якщо є
            if settings.white_label_id:
                client.set_default_header("X-Application-Id", settings.white_label_id)
            
            # Додаємо мову
            client.set_default_header("Accept-Language", lang)
            
            yield client
            
        finally:
            # Безпечно закриваємо клієнт
            if client:
                try:
                    if hasattr(client, 'close'):
                        await client.close()
                    elif hasattr(client, 'rest_client') and hasattr(client.rest_client, 'close'):
                        await client.rest_client.close()
                except (AttributeError, Exception):
                    # Ігноруємо помилки закриття
                    pass
    
    @staticmethod
    @asynccontextmanager
    async def client_api(settings: LoyaltySettings, lang: str = "en"):
        """
        Контекст менеджер для Client API без користувача (публічні методи).
        Зберігає сумісність з існуючим кодом.
        """
        config = InCustClientAdapter.get_client_config(settings, lang)
        client = None
        
        try:
            # Створюємо Client API клієнт
            client = IncustApiClient(configuration=config)
            
            # Додаємо white_label_id, якщо є
            if settings.white_label_id:
                client.set_default_header("X-Application-Id", settings.white_label_id)
            
            # Додаємо мову
            client.set_default_header("Accept-Language", lang)
            
            yield client
            
        finally:
            # Безпечно закриваємо клієнт
            if client:
                try:
                    if hasattr(client, 'close'):
                        await client.close()
                    elif hasattr(client, 'rest_client') and hasattr(client.rest_client, 'close'):
                        await client.rest_client.close()
                except (AttributeError, Exception):
                    # Ігноруємо помилки закриття
                    pass