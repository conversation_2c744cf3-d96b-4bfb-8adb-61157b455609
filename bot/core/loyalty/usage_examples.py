"""
Приклади використання InCust API через адаптер.

Цей файл містить готові функції для найпоширеніших сценаріїв використання
InCust API в проекті Pay4Say.

ВАЖЛИВО: Всі функції використовують методи клієнтів напряму через адаптер,
без дублювання або створення зайвих обгорток.
"""

import logging
from typing import Optional, List

# Імпортуємо методи напряму з згенерованих клієнтів
from incust_client_api_client.api.card_info_api import CardInfoApi
from incust_client_api_client.api.coupon_api import CouponApi
from incust_client_api_client.api.loyalty_api import LoyaltyApi
from incust_terminal_api_client.api.check_transactions_api import CheckTransactionsApi
from incust_terminal_api_client.api.customer_api import CustomerApi
from incust_terminal_api_client.models.check import Check
from incust_terminal_api_client.models.customer import Customer
from incust_terminal_api_client.models import IdType, UserIdentifier

from core.loyalty.incust_client_adapter import (
    run_with_incust_client_api,
    run_with_incust_terminal_api,
    get_or_create_incust_customer,
)
from db.models import User, LoyaltySettings

logger = logging.getLogger("debugger.loyalty.usage_examples")


# ==============================================================================
# ОСНОВНІ ФУНКЦІЇ ДЛЯ РОБОТИ З КЛІЄНТАМИ
# ==============================================================================

async def get_user_card_info(
    settings: LoyaltySettings,
    user: User,
    lang: str = "en"
):
    """
    Отримує інформацію про карту користувача.
    
    Приклад використання методу Client API напряму через адаптер.
    """
    return await run_with_incust_client_api(
        settings,
        user,
        CardInfoApi.incust_controllers_client_client_card_info,
        lang=lang
    )


async def get_user_loyalty_program(
    settings: LoyaltySettings,
    user: User,
    lang: str = "en"
):
    """
    Отримує програму лояльності для користувача.
    
    Включає бонуси, карти, купони та іншу інформацію.
    """
    return await run_with_incust_client_api(
        settings,
        user,
        LoyaltyApi.incust_controllers_client_client_loyalty,
        loyalty_id=settings.loyalty_id,
        bonuses=1,
        coupons=1,
        cards=1,
        customer_access=1,
        lang=lang
    )


async def get_user_coupons(
    settings: LoyaltySettings,
    user: User,
    lang: str = "en"
) -> List:
    """
    Отримує список купонів користувача.
    """
    return await run_with_incust_client_api(
        settings,
        user,
        CouponApi.incust_controllers_client_client_coupons_list,
        lang=lang
    )


async def create_customer_with_identifiers(
    settings: LoyaltySettings,
    user: User,
    lang: str = "en"
):
    """
    Створення клієнта InCust з правильними ідентифікаторами.
    """
    # Підготовуємо ідентифікатори
    user_identifiers = []
    
    if user.phone:
        user_identifiers.append(UserIdentifier(
            type_=IdType.PHONE,
            code=user.phone
        ))
    
    if user.email:
        user_identifiers.append(UserIdentifier(
            type_=IdType.EMAIL,
            code=user.email
        ))
    
    # Створюємо об'єкт Customer з оригінальною схемою
    customer_data = Customer(
        identification=user_identifiers,
        name=user.name or user.username,
        language=lang
    )
    
    # Використовуємо метод клієнта напряму
    return await run_with_incust_terminal_api(
        settings,
        CustomerApi.incust_controllers_term_api_customer_add,
        customer_data
    )


# ==============================================================================
# ФУНКЦІЇ ДЛЯ РОБОТИ З ЧЕКАМИ ТА ТРАНЗАКЦІЯМИ
# ==============================================================================

async def process_loyalty_check(
    settings: LoyaltySettings,
    check_data: Check,
    lang: str = "en"
) -> Optional[Check]:
    """
    Обробляє чек через систему лояльності.
    
    Args:
        settings: Налаштування лояльності
        check_data: Дані чеку в форматі InCust Check
        lang: Мова інтерфейсу
        
    Returns:
        Оброблений чек з нарахованими бонусами та купонами
    """
    try:
        result = await run_with_incust_terminal_api(
            settings,
            CheckTransactionsApi.incust_controllers_term_api_terminal_process_check,
            body=check_data
        )
        return result
    except Exception as e:
        logger.error(f"Помилка обробки чеку лояльності: {e}", exc_info=True)
        return None


async def make_loyalty_transaction(
    settings: LoyaltySettings,
    check_data: Check,
    rules_type: str = "by-all-rules"
) -> Optional[dict]:
    """
    Створює транзакцію лояльності за правилами.
    
    Args:
        settings: Налаштування лояльності
        check_data: Дані чеку
        rules_type: Тип правил ("by-all-rules", "by-bonuses-rules", etc.)
        
    Returns:
        Дані створеної транзакції
    """
    try:
        transaction = await run_with_incust_terminal_api(
            settings,
            CheckTransactionsApi.incust_controllers_term_api_make_check_by_rules,
            rules_type=rules_type,
            check=check_data
        )
        return transaction
    except Exception as e:
        logger.error(f"Помилка створення транзакції лояльності: {e}", exc_info=True)
        return None


async def get_transaction_check(
    settings: LoyaltySettings,
    transaction_id: str
) -> Optional[Check]:
    """
    Отримує чек за ID транзакції.
    """
    try:
        return await run_with_incust_terminal_api(
            settings,
            CheckTransactionsApi.incust_controllers_term_api_transaction_check,
            transaction_id=transaction_id
        )
    except Exception as e:
        logger.error(f"Помилка отримання чеку транзакції {transaction_id}: {e}", exc_info=True)
        return None


# ==============================================================================
# ФУНКЦІЇ ДЛЯ РОБОТИ З КУПОНАМИ
# ==============================================================================

async def get_coupon_details(
    settings: LoyaltySettings,
    user: User,
    coupon_id: str,
    lang: str = "en"
):
    """
    Отримує детальну інформацію про купон.
    """
    try:
        from uuid import UUID
        return await run_with_incust_client_api(
            settings,
            user,
            CouponApi.incust_controllers_client_client_coupon_get,
            coupon_id=UUID(coupon_id),
            lang=lang
        )
    except Exception as e:
        logger.error(f"Помилка отримання купона {coupon_id}: {e}", exc_info=True)
        return None


async def get_coupon_by_code(
    settings: LoyaltySettings,
    user: User,
    coupon_code: str,
    lang: str = "en"
):
    """
    Отримує інформацію про купон за кодом.
    """
    try:
        return await run_with_incust_client_api(
            settings,
            user,
            CouponApi.incust_controllers_client_client_coupon_info,
            code=coupon_code,
            lang=lang
        )
    except Exception as e:
        logger.error(f"Помилка отримання купона за кодом {coupon_code}: {e}", exc_info=True)
        return None


# ==============================================================================
# КОМПЛЕКСНІ БІЗНЕС-ФУНКЦІЇ
# ==============================================================================

async def sync_and_get_user_loyalty_data(
    user: User,
    brand_id: int,
    store_id: Optional[int] = None,
    group_id: Optional[int] = None,
    lang: str = "en"
) -> dict:
    """
    Комплексна функція для синхронізації користувача та отримання даних лояльності.
    
    Використовується в API ендпоінтах для отримання повної інформації.
    """
    try:
        # Отримуємо налаштування лояльності
        settings = await LoyaltySettings.get_loyalty_settings_for_context(
            brand_id=brand_id,
            store_id=store_id,
            group_id=group_id
        )
        
        if not settings:
            return {"error": "Налаштування лояльності не знайдено"}
        
        # Синхронізуємо користувача з InCust
        customer = await get_or_create_incust_customer(
            user=user,
            settings=settings,
            lang=lang
        )
        
        if not customer:
            return {"error": "Не вдалося створити клієнта InCust"}
        
        # Отримуємо дані лояльності
        loyalty_data = await get_user_loyalty_program(settings, user, lang)
        card_info = await get_user_card_info(settings, user, lang)
        
        return {
            "success": True,
            "customer_id": customer.id if hasattr(customer, 'id') else None,
            "loyalty_program": loyalty_data,
            "card_info": card_info,
            "settings": settings.as_dict() if settings else None
        }
        
    except Exception as e:
        logger.error(f"Помилка синхронізації користувача {user.id}: {e}", exc_info=True)
        return {"error": f"Помилка синхронізації: {str(e)}"}


async def process_receipt_with_loyalty(
    user: User,
    receipt_data: dict,
    brand_id: int,
    store_id: Optional[int] = None,
    lang: str = "en"
) -> dict:
    """
    Обробляє чек з системою лояльності.
    
    Включає конвертацію, обробку та отримання купонів.
    """
    try:
        # Отримуємо налаштування
        settings = await LoyaltySettings.get_loyalty_settings_for_context(
            brand_id=brand_id,
            store_id=store_id
        )
        
        if not settings:
            return {"error": "Налаштування лояльності не знайдено"}
        
        # Синхронізуємо користувача
        await get_or_create_incust_customer(user, settings, lang)
        
        # Конвертуємо чек у формат InCust
        from core.incust.schema_converter import convert_payload_to_check
        check_data = convert_payload_to_check(receipt_data, user=user)
        
        # Обробляємо чек
        processed_check = await process_loyalty_check(settings, check_data, lang)
        
        if not processed_check:
            return {"error": "Не вдалося обробити чек"}
        
        # Отримуємо купони якщо є
        coupons = []
        if hasattr(processed_check, 'emitted_coupons') and processed_check.emitted_coupons:
            from core.invoice_loyalty.coupons_service import InvoiceLoyaltyCouponsService
            coupons_service = InvoiceLoyaltyCouponsService()
            
            # Конвертуємо купони в потрібний формат
            coupons_data = []
            for coupon in processed_check.emitted_coupons:
                if coupon:
                    coupon_dict = coupon.to_dict() if hasattr(coupon, 'to_dict') else {}
                    coupons_data.append(coupon_dict)
            
            coupons = await coupons_service.get_coupons_data_from_invoice(
                invoice_coupons_data=coupons_data,
                loyalty_settings=settings,
                user=user,
                lang=lang,
                need_pdf_file=True
            )
        
        return {
            "success": True,
            "check": processed_check,
            "coupons": coupons,
            "bonuses_added": getattr(processed_check, 'bonuses_added_amount', 0),
            "discount_amount": getattr(processed_check, 'discount_amount', 0)
        }
        
    except Exception as e:
        logger.error(f"Помилка обробки чеку з лояльністю: {e}", exc_info=True)
        return {"error": f"Помилка обробки: {str(e)}"}


# ==============================================================================
# ФУНКЦІЇ ДЛЯ API ЕНДПОІНТІВ
# ==============================================================================

async def api_get_user_loyalty_info(
    user: User,
    brand_id: int,
    store_id: Optional[int] = None,
    lang: str = "en"
) -> dict:
    """
    API функція для отримання інформації про лояльність користувача.
    
    Використовується в REST API ендпоінтах.
    """
    result = await sync_and_get_user_loyalty_data(
        user=user,
        brand_id=brand_id,
        store_id=store_id,
        lang=lang
    )
    
    if "error" in result:
        return result
    
    # Форматуємо відповідь для API
    return {
        "loyalty_program": result.get("loyalty_program"),
        "card_info": result.get("card_info"),
        "customer_synced": result.get("customer_id") is not None
    }


async def api_get_card_info(user: User, brand_id: int, lang: str = "en"):
    """
    API функція для отримання карт-інфо.
    Можна використовувати напряму в FastAPI роутерах.
    """
    settings = await LoyaltySettings.get_loyalty_settings_for_context(brand_id=brand_id)
    
    if not settings:
        raise ValueError(f"Loyalty settings not found for brand {brand_id}")
    
    return await run_with_incust_client_api(
        settings,
        user,
        CardInfoApi.incust_controllers_client_client_card_info,
        lang=lang
    )


async def api_get_user_coupons_list(
    user: User,
    brand_id: int,
    store_id: Optional[int] = None,
    lang: str = "en"
) -> dict:
    """
    API функція для отримання списку купонів користувача.
    """
    try:
        settings = await LoyaltySettings.get_loyalty_settings_for_context(
            brand_id=brand_id,
            store_id=store_id
        )
        
        if not settings:
            return {"error": "Налаштування лояльності не знайдено"}
        
        coupons = await get_user_coupons(settings, user, lang)
        
        return {
            "success": True,
            "coupons": coupons,
            "count": len(coupons) if coupons else 0
        }
        
    except Exception as e:
        logger.error(f"Помилка отримання купонів користувача {user.id}: {e}", exc_info=True)
        return {"error": f"Помилка отримання купонів: {str(e)}"}


# ==============================================================================
# ЕКСПОРТ ДЛЯ ЗРУЧНОСТІ
# ==============================================================================

__all__ = [
    # Основні функції
    'get_user_card_info',
    'get_user_loyalty_program',
    'get_user_coupons', 
    'create_customer_with_identifiers',
    
    # Функції для чеків
    'process_loyalty_check',
    'make_loyalty_transaction',
    'get_transaction_check',
    
    # Функції для купонів
    'get_coupon_details',
    'get_coupon_by_code',
    
    # Бізнес-функції
    'sync_and_get_user_loyalty_data',
    'process_receipt_with_loyalty',
    
    # API функції
    'api_get_user_loyalty_info',
    'api_get_card_info',
    'api_get_user_coupons_list',
]