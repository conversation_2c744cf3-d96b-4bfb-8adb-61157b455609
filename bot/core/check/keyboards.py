import aiogram as tg
import aiowhatsapp as wa

from db.models import Brand, ShortToken
from utils.redefined_classes import InlineBtn, InlineKb
from utils.text import f
from .texts import CheckTemplate


async def get_order_keyboard_telegram(
        brand: Brand,
        store_id: int,
        order_id: int,
        lang: str
) -> InlineKb:
    btn_order = InlineBtn(
        await f("email store goto order text", lang),
        web_app=tg.types.WebAppInfo(
            url=brand.get_url(f"{lang}/s/{store_id}/orders/{order_id}")
        )

    )
    btn_store = InlineBtn(
        await f("web store back home link text", lang),
        web_app=tg.types.WebAppInfo(
            url=brand.get_url(f"{lang}/s/{store_id}/menu")
        )

    )

    keyboard = InlineKb()
    keyboard.add(btn_order)
    keyboard.add(btn_store)
    return keyboard


async def get_referral_program_keyboard_telegram(
        template: CheckTemplate,
):
    keyboard = InlineKb()
    keyboard.insert(
        InlineBtn(
            template.referral_program_learn_more_button,
            callback_data="vm_referal",
        )
    )
    return keyboard


async def get_leave_review_keyboard_telegram(
        template: CheckTemplate,
):
    keyboard = InlineKb()
    keyboard.insert(
        InlineBtn(
            template.leave_review_button,
            web_app=tg.types.WebAppInfo(
                url=template.leave_review_link,
            )
        )
    )
    return keyboard


async def get_referral_program_keyboard_whatsapp(
        template: CheckTemplate,
        short_token: ShortToken,
):
    url = template.referral_program_link
    if "?" in url:
        if not url.endswith("&"):
            url += "&"
    else:
        url += "?"
    url += f"st={short_token.token}"

    return wa.types.UrlKeyboard(
        button=wa.types.UrlButton(
            display_text=template.referral_program_learn_more_button,
            url=url,
        )
    )


async def get_leave_review_keyboard_whatsapp(
        template: CheckTemplate,
        short_token: ShortToken,
):
    url = template.leave_review_link
    if "?" in url:
        if not url.endswith("&"):
            url += "&"
    else:
        url += "?"
    url += f"st={short_token.token}"

    return wa.types.UrlKeyboard(
        button=wa.types.UrlButton(
            display_text=template.leave_review_button,
            url=url,
        )
    )
