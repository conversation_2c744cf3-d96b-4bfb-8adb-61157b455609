import logging
import math
from typing import Any, Type

from psutils.convertors import date_to_str, datetime_to_str
from psutils.date_time import localise_datetime
from pydantic import BaseModel, Field
from typing_extensions import Literal

import schemas
from config import NO_CENT_CURRENCIES, PAYMENT_METHODS, WEB_APP_HOST
from core.custom_texts import ct
from core.custom_texts.models.group import GroupCustomTextsModel
from core.incust import IncustService
from core.incust.functions import get_terminal_data_from_object
from core.payment.utils.extra_fees import get_formated_extra_fees
from core.store.order.functions import get_pmt_info
from core.store.product.functions import products_list_to_schemas
from db import crud
from db.models import (
    Brand, BrandCustomSettings, ClientBot, Group, Invoice, InvoiceTemplate,
    OrderAttribute, OrderProduct,
    OrderShipment, OrderShippingStatus, PaymentSettings, ShortToken, Store,
    StoreAttribute, StoreCustomField, StoreOrder, StoreOrderPayment, StoreProduct, User,
)
from loggers import J<PERSON>NLogger
from schemas import BaseTemplateSchema
from schemas.base import ExtraFeeFormated
from schemas.store.types import ShipmentType
from utils.log_func_exec_time import log_func_exec_time
from utils.numbers import format_currency, format_sum
from utils.platform_admins import send_message_to_platform_admins
from utils.text import f, fd
from utils.translator import t


class CheckItemAttribute(BaseModel):
    code: str | None = None
    name: str
    quantity: int
    price: str
    sum: str


class CheckItemModifier(BaseModel):
    id: int
    name: str
    value: str
    orig_value: str


class CheckItem(BaseModel):
    code: str | None = None
    name: str
    quantity: int
    price: str
    sum: str
    discount_sum: str | None = None
    url: str | None = None
    thumbnail_url: str | None = None
    attributes: list[CheckItemAttribute] | None = None
    modifiers: list[CheckItemModifier] | None = None
    modifiers_str: str | None = None


class CheckTemplate(BaseTemplateSchema):
    TEMPLATE_PATH = "notification_with_check_web.html"

    header: str | None = None
    message: str | None = None
    footer: str | None = None

    is_order: bool = False

    store_name_text: str
    date_text: str
    status_text: str | None = None
    status_pay_text: str | None = None
    status_pay: str | None = None

    to_text: str
    email: str | None = None
    phone: str | None = None

    payment_method_text: str | None = None
    payment_method_comment_text: str | None = None
    payment_method_info_text: str | None = None

    delivery_method_text: str | None = None
    delivery_method_pickup_point_text: str | None = None
    delivery_method_address_text: str | None = None
    delivery_method_comment_text: str | None = None
    desired_delivery_date_text: str | None = None
    desired_pickup_date_text: str | None = None

    comment_text: str | None = None
    manager_comment_text: str | None = None

    items_text: str
    item_sale_text: str
    check_items: list[CheckItem] = Field(default_factory=list)

    subtotal_text: str
    subtotal: str | None = None
    subtotal_value: float | None = None

    total_discount_text: str
    total_discount: str | None = None

    payment_costs_text: str
    payment_price: str | None = None

    delivery_costs_text: str
    delivery_payment_separately_text: str
    is_delivery_payment_separately: bool = False
    delivery_price: str | None = None

    total_amount_text: str
    total_amount: str | None = None
    total_amount_value: float | None = None

    tips_text: str
    tips: str | None = None

    total_amount_with_tips_text: str
    total_amount_with_tips: str | None = None
    total_amount_with_tips_value: float | None = None

    paid_text: str
    payment_due_text: str
    paid_sum: str | None = None
    paid_sum_value: float | None = None

    pmt_info: str | None = None
    pmt_info_header: str | None = None
    pmt_info_html: str | None = None

    loyalty_issued_per_order_text: str
    loyalty_to_be_issued_per_payment_text: str

    is_loyalty_awards: bool = False

    loyalty_bonuses_text: str
    loyalty_bonuses: str | None = None

    loyalty_customer_accounts_text: str
    loyalty_customer_accounts_to_be_replenished_text: str
    loyalty_customer_accounts: list[dict[str, Any]] | None = None

    loyalty_vouchers_text: str
    loyalty_vouchers_count: int | None = None

    coupons: list[schemas.CouponShowData] | None = None

    link: str | None = None
    goto_order_text: str | None = None
    pmt_button_text: str | None = None
    pmt_button_url: str | None = None
    web_store_back_home_link_text: str | None = None
    web_store_back_home_url: str | None = None

    loyalty_settings: schemas.LoyaltySettingsClientSchema | None = None
    referral_program_title: str | None = None
    referral_program_learn_more_button: str | None = None
    referral_program_link: str | None = None

    review_proposal_text: str | None = None
    leave_review_button: str | None = None
    leave_review_link: str | None = None

    header_friend: str | None = None
    message_friend: str | None = None

    order_type: schemas.OrderType = "regular"
    gifts_awarded_text: str | None = None
    topup_error: str | None = None
    topup_account: str | None = None
    topup_card: str | None = None
    topup_charge: str | None = None
    topup_charge_text: str | None = None
    payer_fee_text: str | None = None
    payer_fee: str | None = None
    paid_sum: str | None = None
    extra_fees: list[ExtraFeeFormated] | None = None
    total_sum_with_extra_fee: str | None = None
    total_sum_with_extra_fee_value: float | None = None

    @property
    def from_name(self):
        return self.store_name_text


async def get_check_texts(lang: str):
    return await fd(
        {
            "store_name_text": "check store name text",
            "date_text": "check date text",
            "status_text": "check status text",
            "status_pay_text": "check status pay text",
            "status_pay_not_payed_text": "check status pay not payed text",
            "status_pay_payed_text": "check status pay payed text",
            "to_text": "check to text",
            "payment_method_text": "check payment method text",
            "payment_method_comment_text": "check payment method comment text",
            "payment_method_info_text": "check payment method info text",
            "delivery_method_text": "check delivery method text",
            "delivery_method_address_text": "check delivery method address text",
            "delivery_method_comment_text": "check delivery method comment text",
            "delivery_method_pickup_point_text": "check delivery method pickup point "
                                                 "text",
            "desired_delivery_date_text": "check desired delivery date text",
            "desired_pickup_date_text": "check desired pickup date text",
            "comment_text": "check comment text",
            "items_text": "check items text",
            "item_sale_text": "check item sale text",
            "subtotal_text": "check subtotal text",
            "total_discount_text": "check total discount text",
            "payment_costs_text": "check payment costs text",
            "delivery_costs_text": "check delivery costs text",
            "delivery_payment_separately_text": "check delivery payment separately "
                                                "text",
            "total_amount_text": "check total amount text",
            "tips_text": "check tips text",
            "total_amount_with_tips_text": "check total amount with tips text",
            "paid_text": "check paid text",
            "payment_due_text": "check payment due text",
            "loyalty_issued_per_order_text": "check loyalty issued per order text",
            "loyalty_to_be_issued_per_payment_text": "check loyalty to be issued per "
                                                     "payment text",
            "loyalty_bonuses_text": "check loyalty bonuses text",
            "loyalty_customer_accounts_text": "check loyalty customer accounts text",
            "loyalty_customer_accounts_to_be_replenished_text": "check loyalty "
                                                                "customer accounts to "
                                                                "be replenished text",
            "loyalty_vouchers_text": "check loyalty vouchers text",
            "gifts_awarded_text": "gifts awarded text",
            "payer_fee_receipt_text": "payer fee receipt text",
        }, lang
    )


async def build_check_template(
        lang: str,
        order: StoreOrder | None = None,
        order_status: OrderShippingStatus | None = None,
        invoice: Invoice | None = None,
        store: Store | None = None,
        brand: Brand | None = None,
        user: User | None = None,
        group: Group | None = None,
        bot: ClientBot | None = None,
        template_cls: Type[CheckTemplate] = CheckTemplate, *,
        logger: JSONLogger,
):
    texts = await get_check_texts(lang)
    template = template_cls(**texts)
    template.is_order = bool(order)

    template.status_text = None
    template.status_pay_text = None
    template.payment_method_comment_text = None
    template.payment_method_info_text = None
    template.delivery_method_text = None
    template.delivery_method_pickup_point_text = None
    template.delivery_method_address_text = None
    template.delivery_method_comment_text = None
    template.desired_delivery_date_text = None
    template.desired_pickup_date_text = None
    template.comment_text = None
    template.header_friend = None
    template.message_friend = None
    template.order_type = order.type

    obj = order or invoice

    if not user:
        user = await User.get_by_id(obj.user_id)
    elif user.id != obj.user_id:
        raise ValueError("invalid user received from args")

    template.link = f"{brand.domain}" if brand.domain else f"{WEB_APP_HOST}/"

    if order:
        group, brand, store, invoice, incust_check_data = await __build_for_order(
            template, texts, lang,
            order, order_status,
            store, brand, group,
            bot=bot,
        )
        currency = store.currency
    elif invoice:
        group, brand, incust_check_data = await __build_for_invoice(
            template, texts, lang,
            invoice, brand, group,
        )
        currency = invoice.currency
    else:
        raise ValueError("You must specify either store_order or invoice")

    template.extra_fees = await get_formated_extra_fees(
        obj, currency, lang, group.country_code
    )
    template.total_sum_with_extra_fee_value = obj.total_sum_with_extra_fee / 100
    template.total_sum_with_extra_fee = format_currency(
        template.total_sum_with_extra_fee_value, currency, lang, group.country_code
    )

    ct_obj = await GroupCustomTextsModel.from_object(group)

    gift_name = ""
    topup_name = ""
    if order.type == "gift":
        status_text = "gift_text"
        order_products = await crud.get_order_products(order.id)
        if order_products and len(order_products):
            gift_name = order_products[0].name
    elif order.type == "topup":
        if order_status.status != "open_unconfirmed":
            status_text = "topup_text"
        else:
            status_text = f"{order_status.status}_text"
        order_products = await crud.get_order_products(order.id)
        if order_products and len(order_products):
            order_prod = order_products[0]
            topup_name = order_prod.name
            if order_prod.is_topup_error:
                template.topup_error = await f("loyalty topup notification error", lang)
            account_name = order_prod.incust_account.get(
                "title", ""
            ) if order_prod.incust_account else ""
            card_number = order_prod.incust_card or ""
            if account_name:
                template.topup_account = (f"{await f('special account title', lang)}: "
                                          f"{account_name}")
            if card_number:
                template.topup_card = (f"{await f('loyalty topup card title', lang)}: "
                                       f"{card_number}")
            if order_prod.topup_charge:
                template.topup_charge_text = await f("loyalty topup charge title", lang)
                template.topup_charge = format_currency(
                    round(order_prod.topup_charge / 100, 2),
                    currency, locale=group.lang,
                )
    else:
        if (template.total_sum_with_extra_fee_value == 0 and order_status.status ==
                "payed"):
            status_text = "open_unconfirmed_text"
        else:
            status_text = f"{order_status.status}_text"

    orders_or_invoices = "orders" if order else "invoices"

    def get_key(header_or_message: Literal["header", "message"]):
        return ("store", orders_or_invoices, "notifications", header_or_message,
                status_text)

    template.header = await ct(
        ct_obj, lang,
        *get_key("header"),
        order_id=order.id if order else "",
        invoice_id=invoice.id if invoice else "",
        name=user.name,
        comment=order_status.comment or "",
        gift_name=gift_name,
        topup_name=topup_name,
    )

    if (
            order.status_pay == "payed" and invoice and invoice.is_friend and
            invoice.payer_id
            and invoice.payer_id != invoice.user_id):
        template.header_friend = await ct(
            ct_obj, lang,
            "store", orders_or_invoices, "notifications", "header", "friend_payed_text",
            order_id=order.id if order else "",
            invoice_id=invoice.id if invoice else "",
            name=user.name,
            comment=order_status.comment or "",
        )
        payer = await User.get_by_id(invoice.payer_id)
        template.message_friend = await ct(
            ct_obj, lang,
            *get_key("message"),
            order_id=order.id if order else "",
            invoice_id=invoice.id if invoice else "",
            name=payer.name,
            comment=order_status.comment or "",
        )

    template.message = await ct(
        ct_obj, lang,
        *get_key("message"),
        order_id=order.id if order else "",
        invoice_id=invoice.id if invoice else "",
        name=user.name,
        comment=order_status.comment or "",
    )

    if bot and bot.bot_type == "whatsapp" and order:
        short_token = await ShortToken.create(
            user.id,
            user.allowed_scopes_str,
            bot.id if bot else None,
            lang,
        )

        link = brand.get_url(
            f"s/{store.id}/orders/{order.id}",
            qrmenu=order.menu_in_store_id,
            short_token=short_token,
        )

        template.footer = await ct(
            ct_obj, lang,
            "store", orders_or_invoices, "notifications", "footer_text",
            link=link,
        )

    if order and (order.status == "closed" or order.status_pay == "payed"):
        template.review_proposal_text = await ct(
            ct_obj, lang,
            "review", "proposal_text",
        )
        template.leave_review_button = await ct(
            ct_obj, lang,
            "review", "leave_button",
        )
        template.leave_review_link = brand.get_url(f"s/{store.id}/review")

    template.store_name_text = texts["store_name_text"].format(
        name=store.name if store else brand.name,
    )

    datetime_str = datetime_to_str(localise_datetime(obj.time_created, group.timezone))

    template.date_text = texts["date_text"].format(datetime=datetime_str)
    template.to_text = texts["to_text"].format(name=user.name)

    if obj.email:
        template.email = obj.email
    if obj.phone:
        template.phone = obj.phone

    with (log_func_exec_time("build_check_template:loyalty", logger)):
        if incust_check_data:
            # Використовуємо дані напряму з incust_check_data без додаткової конвертації
            incust_check = incust_check_data
            invoice_template = await InvoiceTemplate.get(
                invoice.invoice_template_id
            ) if invoice and invoice.invoice_template_id else None

            template.loyalty_bonuses = format_sum(
                incust_check.get("bonuses_added_amount", 0), group.lang,
                currency=invoice.currency if invoice else None
            )
            
            # Використовуємо збережені дані зі спеціальних акаунтів замість запиту до API
            if invoice and invoice.loyalty_special_accounts_data:
                template.loyalty_customer_accounts = invoice.loyalty_special_accounts_data
            else:
                template.loyalty_customer_accounts = None

            template.loyalty_vouchers_count = len(
                incust_check.get("emitted_coupons", [])
            ) if incust_check.get("emitted_coupons") else None
            
            # Використовуємо новий сервіс для отримання купонів якщо є нові дані
            if (invoice and invoice.loyalty_coupons_data and 
                invoice.loyalty_settings_id and invoice.incust_transaction_id):
                from core.invoice_loyalty.coupons_service import InvoiceLoyaltyCouponsService
                from db.models import LoyaltySettings, User
                
                try:
                    loyalty_settings = await LoyaltySettings.get(invoice.loyalty_settings_id)
                    user = await User.get_by_id(obj.user_id)
                    coupons_service = InvoiceLoyaltyCouponsService()
                    
                    # Перетворюємо дані якщо потрібно
                    coupons_data = invoice.loyalty_coupons_data
                    if isinstance(coupons_data, dict):
                        coupons_data = [coupons_data]
                    elif not isinstance(coupons_data, list):
                        coupons_data = []
                    
                    template.coupons = await coupons_service.get_coupons_data_from_invoice(
                        invoice_coupons_data=coupons_data,
                        loyalty_settings=loyalty_settings,
                        user=user,
                        lang=lang,
                        need_pdf_file=True,
                    )
                except Exception as e:
                    logger.error(f"Помилка отримання купонів через новий сервіс: {e}")
                    template.coupons = []
            else:
                # Не використовуємо старий спосіб - просто порожній список
                template.coupons = []
            template.is_loyalty_awards = any(
                (
                    template.loyalty_bonuses,
                    template.loyalty_vouchers_count,
                    template.loyalty_customer_accounts,
                )
            )

            try:
                # Використовуємо нові клієнти для отримання loyalty settings
                if invoice and invoice.loyalty_settings_id:
                    from db.models import LoyaltySettings
                    from core.loyalty.incust_client_adapter import run_with_incust_client_api
                    
                    loyalty_settings = await LoyaltySettings.get(invoice.loyalty_settings_id)
                    if loyalty_settings:
                        from incust_client_api_client.api.loyalty_api import LoyaltyApi
                        
                        loyalty_program = await run_with_incust_client_api(
                            loyalty_settings,
                            user,
                            LoyaltyApi.incust_controllers_client_client_loyalty,
                            loyalty_id=loyalty_settings.loyalty_id,
                            lang=lang
                        )
                        if loyalty_program:
                            # Конвертуємо оригінальну схему Loyalty в схему яку очікує template
                            loyalty_dict = loyalty_program.to_dict()
                            from schemas import LoyaltySettingsClientSchema
                            template.loyalty_settings = LoyaltySettingsClientSchema(**loyalty_dict)
                            
                            if (
                                hasattr(loyalty_program, 'referral_program') and 
                                loyalty_program.referral_program and
                                hasattr(loyalty_program.referral_program, 'title') and
                                loyalty_program.referral_program.title
                            ):
                                template.referral_program_title = loyalty_program.referral_program.title
                                template.referral_program_learn_more_button = await f(
                                    "incust loyalty referral learn more button", lang,
                                )
                                template.referral_program_link = brand.get_url(
                                    f"s/{store.id}/profile/share_and_earn"
                                )
                else:
                    template.loyalty_settings = None
            except Exception as e:
                logging.error(e, exc_info=True)
                template.loyalty_settings = None

    return template


async def __build_for_order(
        template: CheckTemplate,
        texts: dict[str, str], lang: str,
        order: StoreOrder | None = None,
        order_status: OrderShippingStatus | None = None,
        store: Store | None = None,
        brand: Brand | None = None,
        group: Group | None = None,
        invoice: Invoice | None = None,
        bot: ClientBot | None = None,
):
    if not store:
        store = await Store.get(order.store_id)
    elif store.id != order.store_id:
        raise ValueError("invalid store received from args")
    currency = store.currency

    if not brand:
        brand = await Brand.get(store.brand_id)
    elif brand.id != store.brand_id:
        raise ValueError("invalid brand received from args")

    if not group:
        group = await Group.get(brand.group_id)
    elif group.id != brand.group_id:
        raise ValueError("invalid group received from args")

    if not invoice:
        invoice = await Invoice.get(order.invoice_id) if order.invoice_id else None
    elif invoice.id != order.invoice_id:
        raise ValueError("invalid invoice received from args")

    if order.comment:
        template.comment_text = texts["comment_text"].format(comment=order.comment)

    # Додаємо коментар менеджера, якщо він є
    if order_status and order_status.comment:
        manager_comment_label = await f("manager comment label", lang)
        template.manager_comment_text = (f"{manager_comment_label}: "
                                         f"{order_status.comment}")

    template.status_pay = order.status_pay
    template.status_pay_text = texts["status_pay_text"].format(
        status=texts["status_pay_payed_text"]
        if order.status_pay == "payed"
        else texts["status_pay_not_payed_text"]
    )

    shipment: OrderShipment = await crud.get_order_shipment(order.id)

    order_payment: StoreOrderPayment | None = None
    if order:
        order_payment: StoreOrderPayment = await StoreOrderPayment.get(
            order_id=order.id
        )
    elif invoice:
        order_payment: StoreOrderPayment = await StoreOrderPayment.get(
            invoice_id=invoice.id
        )

    status = await f(
        f'email store order {order_status.status} status text', lang,
        order_id=order.id
    )
    template.status_text = texts["status_text"].format(status=status)
    payment_method_name, payment_label_comment, payment_price, post_payment_info = \
        await get_payment_info(
            shipment,
            order_payment,
            group, lang,
            total_sum_str=format_currency(
                round(order.sum_to_pay / 100, 2),
                currency, locale=group.lang,
            )
        )

    template.payment_method_text = texts["payment_method_text"].format(
        payment_method=payment_method_name
    )
    if payment_label_comment and order_payment.comment:
        template.payment_method_comment_text = texts[
            "payment_method_comment_text"].format(
            payment_comment_label=payment_label_comment,
            payment_comment=order_payment.comment,
        )

    if order_payment and post_payment_info:
        template.payment_method_info_text = texts["payment_method_info_text"].format(
            payment_info=post_payment_info
        )

    if payment_price:
        template.payment_price = payment_price

    if (shipment.base_type != ShipmentType.IN_STORE.value and shipment.base_type !=
            ShipmentType.NO_DELIVERY.value):
        shipment_method_name, shipment_label_comment = await get_shipment_name_and_data(
            shipment, group, lang,
        )
        template.delivery_method_text = \
            texts["delivery_method_text"].format(delivery_method=shipment_method_name)

        if shipment.base_type == ShipmentType.PICKUP.value:
            template.delivery_method_pickup_point_text = \
                texts["delivery_method_pickup_point_text"].format(name=store.name)

        if address_text := await get_address_text(order, store, shipment, bot):
            if shipment.base_type != ShipmentType.PICKUP.value:
                template.delivery_method_address_text = \
                    texts["delivery_method_address_text"].format(address=address_text)

        if shipment_label_comment and shipment.comment:
            template.delivery_method_comment_text = \
                texts["delivery_method_comment_text"].format(
                    delivery_comment_label=shipment_label_comment,
                    delivery_comment=shipment.comment,
                )

        if order.desired_delivery_date or order.desired_delivery_time:
            values = [date_to_str(order.desired_delivery_date),
                      order.desired_delivery_time]
            desired_delivery_date_str = " ".join([el for el in values if el])
            # noinspection PyTypedDict
            setattr(
                template, f"desired_{shipment.base_type}_date_text",
                texts[f"desired_{shipment.base_type}_date_text"].format(
                    datetime=desired_delivery_date_str,
                )
            )

    order_products_data: list[tuple[OrderProduct, StoreProduct]] = \
        await crud.get_order_products(order.id, with_products=True)

    products = [(el[1], None) for el in order_products_data]
    products_schemas = await products_list_to_schemas(
        products, store.id, group.id,
        lang, group.lang, group.is_translate,
        brand,
    )

    subtotal = 0
    for i, order_product_data in enumerate(order_products_data):
        order_product = order_product_data[0]
        ids = [el.id for el in products_schemas]
        if order_product.product_id in ids:
            product_schema = products_schemas[ids.index(order_product.product_id)]
        else:
            product_schema = products_schemas[i]

        subtotal += order_product.before_loyalty_sum
        template.check_items.append(
            await make_check_item_from_order(
                order_product,
                product_schema,
                brand,
                store.id,
                group.lang,
                currency,
            )
        )

    template.subtotal_value = subtotal / 100
    template.subtotal = format_currency(
        template.subtotal_value, currency, group.lang,
    )
    template.total_discount = format_sum(
        order.discount_and_bonuses / 100, group.lang, currency=currency
    )
    template.is_delivery_payment_separately = shipment.is_paid_separately
    template.delivery_price = format_sum(shipment.price, group.lang, currency=currency)

    await __set_fee(group, invoice, template, currency)

    template.total_amount_value = round(
        order.total_sum / 100, 2
    ) if store.currency not in NO_CENT_CURRENCIES else math.ceil(
        order.total_sum / 100
    )
    template.total_amount = format_currency(
        template.total_amount_value,
        currency, locale=group.lang,
    )

    template.paid_sum_value = round(
        order.paid_sum / 100, 2
    ) if store.currency not in NO_CENT_CURRENCIES else math.ceil(
        order.paid_sum / 100
    )
    template.paid_sum = format_currency(
        template.paid_sum_value,
        currency, locale=group.lang,
    )

    if order.tips_sum:
        template.tips = format_currency(
            round(order.tips_sum / 100, 2),
            currency, locale=group.lang,
        )

    template.total_amount_with_tips_value = round(order.sum_to_pay / 100, 2)
    template.total_amount_with_tips = format_currency(
        template.total_amount_with_tips_value,
        currency, locale=group.lang,
    )

    template.pmt_info = await get_pmt_info(order.id, lang)
    if template.pmt_info:
        template.pmt_info_header = await f(
            "web store make order payment header text", lang
        )
        template.pmt_info_header = template.pmt_info.replace(
            '\n- - - - - - - -', ''
        ).replace('\n', '<br>')

    template.goto_order_text = await f('email store goto order text', lang)
    template.web_store_back_home_link_text = await f(
        'web store back home link text', lang
    )
    template.web_store_back_home_url = brand.get_url(f"s/{store.id}")

    if order.status_pay != 'payed' \
            and order.status != 'closed':
        template.pmt_button_text = await f('web store orders pay button text', lang)
    template.pmt_button_url = brand.get_url(
        f"s/{store.id}/orders/{order.id}", order_token=order.token
    )

    # Використовуємо дані лояльності з Invoice (нові поля)
    if invoice and invoice.incust_transaction_id:
        # Створюємо incust_check_data з нових структурованих полів Invoice
        # Додаємо всі обов'язкові поля для IncustCheckSchema
        # Отримуємо payment_type з transaction_data або за замовчуванням
        payment_type = "currency"
        if invoice.loyalty_transaction_data and isinstance(invoice.loyalty_transaction_data, dict):
            payment_type = invoice.loyalty_transaction_data.get("payment_type", "currency")
        
        incust_check_data = {
            "amount": invoice.loyalty_amount / 100,  # З копійок в основні одиниці - обов'язкове
            "bonuses_added_amount": invoice.loyalty_bonuses_added / 100,  # З копійок в основні одиниці
            "discount_amount": invoice.loyalty_discount_amount / 100,  # З копійок в основні одиниці
            "amount_to_pay": invoice.loyalty_amount_to_pay / 100,  # З копійок в основні одиниці
            "emitted_coupons": invoice.loyalty_coupons_data or [],
            "special_accounts_charges": invoice.loyalty_special_accounts_data or [],
            "skip_message": invoice.loyalty_skip_message or False,
            "transaction_id": invoice.incust_transaction_id,
            # Обов'язкові поля для IncustCheckSchema
            "payment_id": invoice.incust_transaction_id,  # Використовуємо transaction_id як payment_id
            "payment_type": payment_type,  # Отримуємо з збережених даних
        }
        
        # Додаємо transaction дані якщо є
        if invoice.loyalty_transaction_data:
            transaction_data = invoice.loyalty_transaction_data.copy()
            # Перевіряємо та додаємо обов'язкові поля для TerminalTransaction
            if not transaction_data.get("amount"):
                transaction_data["amount"] = invoice.loyalty_amount / 100
            if not transaction_data.get("amount_to_pay"):
                transaction_data["amount_to_pay"] = invoice.loyalty_amount_to_pay / 100
            if not transaction_data.get("currency"):
                transaction_data["currency"] = currency
            if not transaction_data.get("id"):
                transaction_data["id"] = invoice.incust_transaction_id
            if not transaction_data.get("payment_type"):
                transaction_data["payment_type"] = "currency"
            if not transaction_data.get("summary_amount"):
                transaction_data["summary_amount"] = invoice.loyalty_amount / 100
            if not transaction_data.get("type"):
                transaction_data["type"] = "reserve"
                
            incust_check_data["transaction"] = transaction_data
    else:
        incust_check_data = None

    return group, brand, store, invoice, incust_check_data


async def __build_for_invoice(
        template: CheckTemplate,
        texts: dict[str, str], lang: str,
        invoice: Invoice | None = None,
        brand: Brand | None = None,
        group: Group | None = None,
):
    template.status_pay = invoice.status
    template.status_pay_text = texts["status_pay_text"].format(
        status=texts["status_pay_payed_text"]
        if invoice.status_pay == "payed"
        else texts["status_pay_not_payed_text"]
    )

    subtotal = 0
    items = await crud.get_invoice_items(invoice.id)
    for item in items:
        subtotal += item.before_loyalty_sum

        template.check_items.append(
            CheckItem(
                code=item.item_code,
                name=item.name,
                quantity=item.quantity,
                price=format_sum(
                    round(item.price / 100, 2), lang, False, currency=invoice.currency
                ),
                discount_sum=format_sum(round(item.discount / 100, 2), lang),
                sum=format_sum(
                    round(item.before_loyalty_sum / 100, 2), lang, False,
                    currency=invoice.currency
                ),
            )
        )

    fee = await __set_fee(group, invoice, template, invoice.currency)

    template.subtotal_value = round(
        subtotal / 100, 2
    ) if invoice.currency not in NO_CENT_CURRENCIES else math.ceil(
        subtotal / 100
    )
    template.subtotal = format_currency(
        template.subtotal_value, invoice.currency,
        group.lang,
    )
    template.total_amount_value = template.subtotal_value
    template.total_amount = format_currency(
        template.total_amount_value, invoice.currency,
        group.lang,
    )

    template.paid_sum_value = round(
        (subtotal + fee) / 100, 2
    ) if invoice.currency not in NO_CENT_CURRENCIES else math.ceil(
        (subtotal + fee) / 100
    )
    template.paid_sum = format_currency(
        template.paid_sum_value, invoice.currency,
        group.lang,
    )
    template.total_amount_with_tips_value = template.paid_sum_value
    template.total_amount_with_tips = format_currency(
        template.total_amount_with_tips_value, invoice.currency,
        group.lang,
    )

    if not brand:
        brand = await Brand.get(group_id=invoice.group_id)
    elif brand.group_id != invoice.group_id:
        raise ValueError("invalid brand received from args")

    if not group:
        group = await Group.get(invoice.group_id)
    elif group.id != invoice.group_id:
        raise ValueError("invalid group received from args")

    # Використовуємо дані лояльності з Invoice (нові поля) для invoice-only випадку
    if invoice and invoice.incust_transaction_id:
        # Створюємо incust_check_data з нових структурованих полів Invoice
        # Додаємо всі обов'язкові поля для IncustCheckSchema
        # Отримуємо payment_type з transaction_data або за замовчуванням
        payment_type = "currency"
        if invoice.loyalty_transaction_data and isinstance(invoice.loyalty_transaction_data, dict):
            payment_type = invoice.loyalty_transaction_data.get("payment_type", "currency")
        
        incust_check_data = {
            "amount": invoice.loyalty_amount / 100,  # З копійок в основні одиниці - обов'язкове
            "bonuses_added_amount": invoice.loyalty_bonuses_added / 100,  # З копійок в основні одиниці
            "discount_amount": invoice.loyalty_discount_amount / 100,  # З копійок в основні одиниці
            "amount_to_pay": invoice.loyalty_amount_to_pay / 100,  # З копійок в основні одиниці
            "emitted_coupons": invoice.loyalty_coupons_data or [],
            "special_accounts_charges": invoice.loyalty_special_accounts_data or [],
            "skip_message": invoice.loyalty_skip_message or False,
            "transaction_id": invoice.incust_transaction_id,
            # Обов'язкові поля для IncustCheckSchema
            "payment_id": invoice.incust_transaction_id,  # Використовуємо transaction_id як payment_id
            "payment_type": payment_type,  # Отримуємо з збережених даних
        }
        
        # Додаємо transaction дані якщо є
        if invoice.loyalty_transaction_data:
            transaction_data = invoice.loyalty_transaction_data.copy()
            # Перевіряємо та додаємо обов'язкові поля для TerminalTransaction
            if not transaction_data.get("amount"):
                transaction_data["amount"] = invoice.loyalty_amount / 100
            if not transaction_data.get("amount_to_pay"):
                transaction_data["amount_to_pay"] = invoice.loyalty_amount_to_pay / 100
            if not transaction_data.get("currency"):
                transaction_data["currency"] = invoice.currency
            if not transaction_data.get("id"):
                transaction_data["id"] = invoice.incust_transaction_id
            if not transaction_data.get("payment_type"):
                transaction_data["payment_type"] = "currency"
            if not transaction_data.get("summary_amount"):
                transaction_data["summary_amount"] = invoice.loyalty_amount / 100
            if not transaction_data.get("type"):
                transaction_data["type"] = "reserve"
                
            incust_check_data["transaction"] = transaction_data
    else:
        incust_check_data = None

    return group, brand, incust_check_data


async def __set_fee(
        group: Group, invoice: Invoice, template: CheckTemplate, currency: str
) -> int:
    fee = 0
    if invoice and invoice.payer_fee:
        fee = invoice.payer_fee
        template.payer_fee = format_currency(
            round(fee / 100, 2) if currency not in NO_CENT_CURRENCIES else math.ceil(
                fee / 100
            ),
            currency, locale=group.lang,
        )
        template.payer_fee_text = await f("payer fee receipt text", group.lang)
    return fee


async def get_payment_info(
        shipment: OrderShipment,
        order_payment: StoreOrderPayment,
        group: Group,
        lang: str | None = None,
        # if None, group lang will be used and no translations applied
        total_sum_str: str | None = None,
        add_account_id: bool = True,
):
    label_comment = None
    price = None

    post_payment_info = ""
    payment_method_name = None
    if order_payment:
        if lang:
            settings = await PaymentSettings.get(id=order_payment.payment_settings_id)
            translated = await t(
                settings, lang, group.lang,
                group_id=group.id,
                is_auto_translate_allowed=group.is_translate,
            )
            payment_method_name = translated["name"]
            label_comment = translated["label_comment"]
            post_payment_info = translated["post_payment_info"]
            if isinstance(
                    post_payment_info, str
            ) and total_sum_str and "{amount}" in post_payment_info:
                post_payment_info = post_payment_info.replace("{amount}", total_sum_str)
        else:
            payment_method_name = order_payment.name
            label_comment = order_payment.label_comment
        if not label_comment and order_payment.comment:
            label_comment = await f("invoice comment label", group.lang or lang)

        price = order_payment.json_data.get(
            "payer_fee_value", 0
        ) if order_payment.json_data else 0
    if not order_payment or not order_payment.payment_method:
        payment_method_name = "-"
    else:
        if not payment_method_name:
            payment_method_name = None
            if (shipment.base_type == ShipmentType.IN_STORE.value and not
            order_payment.name):
                payment_method_name = await f(
                    "store payment later text", group.lang or lang
                )
            if (not payment_method_name and not order_payment.name and
                    order_payment.payment_method and order_payment.payment_method in
                    PAYMENT_METHODS):
                payment_method_name = await f(
                    "payments card payment method text", group.lang or lang
                ) + f" ({order_payment.payment_method})"
            if (not payment_method_name and not order_payment.name and
                    order_payment.payment_method and order_payment.payment_method not
                    in PAYMENT_METHODS):
                match order_payment.payment_method:
                    case "cash":
                        payment_method_name = await f(
                            "store payment in store text", group.lang or lang
                        )
                    case "friend":
                        payment_method_name = await f(
                            "store payment friend text", group.lang or lang
                        )
                    case "incust_pay":
                        payment_method_name = await f(
                            "store payment incust text", group.lang or lang
                        )
                    case "tg_pay":
                        payment_method_name = "Telegram"
                    case "bonuses":
                        payment_method_name = await f(
                            "store payment bonuses text", group.lang or lang
                        )
            if not payment_method_name:
                payment_method_name = "-"
                # payment_method_name = await f(f"store payment {
                # order.payment_method} text", group.lang or lang)

    if price and isinstance(price, str):
        price = int(float(price) * 100)

    if order_payment and order_payment.incust_account_id:
        order = await StoreOrder.get(order_payment.order_id)
        if order and order_payment.incust_account_name:
            payment_method_name = (
                f"{order_payment.incust_account_name}\n"
                f"{order_payment.incust_account_id})"
                if add_account_id else
                order_payment.incust_account_name
            )
            if order_payment.incust_card_id:
                payment_method_name += f"\n{order_payment.incust_card_id}"

    return payment_method_name, label_comment, price, post_payment_info


async def get_shipment_name_and_data(
        shipment: OrderShipment,
        group: Group,
        lang: str | None = None,
        # if None, group lang will be used and no translations applied
):
    settings = await BrandCustomSettings.get(shipment.settings_id)

    if not settings or settings.is_custom_shipment:
        if group and settings and lang:
            translated = await t(
                settings, lang, group.lang,
                group_id=group.id,
                is_auto_translate_allowed=group.is_translate,
            )
            shipment_name = translated["name"]
            shipment_label_comment = translated["label_comment"]
            if not shipment_label_comment:
                shipment_label_comment = await settings.get_label_comment(lang)
        else:
            shipment_name = shipment.name
            shipment_label_comment = shipment.label_comment
    else:
        shipment_name = await f(f"store {shipment.base_type} text", lang or group.lang)
        shipment_label_comment = None

    return shipment_name, shipment_label_comment


async def get_address_text(
        order: StoreOrder,
        store: Store,
        shipment: OrderShipment,
        bot: ClientBot | None = None,
        include_link: bool = True
):
    texts = [
        order.delivery_address,
        order.address_flat,
        order.address_floor,
        order.address_entrance,
        order.address_comment,
    ]

    address_text = ", ".join([str(el) for el in texts if el])
    if not address_text and shipment.base_type == ShipmentType.PICKUP.value:
        store_address = await StoreCustomField.get(store_id=store.id, name="address")
        address_text = store_address.value if store_address else ""

    if include_link:
        if order.map_link:
            # whatsapp does not support <a href="">
            if not bot or bot.bot_type != "whatsapp":
                address_text = f'<a href="{order.map_link}">{address_text}</a>'
            else:
                address_text += f"\n{order.map_link}"

    return address_text


async def make_check_item_from_order(
        order_product: OrderProduct,
        product_schema: schemas.ProductSchema,
        brand: Brand,
        store_id: int,
        lang: str,
        currency: str | None = None,
):
    return CheckItem(
        code=product_schema.product_id,
        name=product_schema.name,
        quantity=order_product.quantity,
        price=format_sum(order_product.price / 100, lang, False, currency=currency),
        discount_sum=format_sum(
            order_product.discount_sum / 100, lang, currency=currency
        ),
        sum=format_sum(
            order_product.price * order_product.quantity / 100, lang, False, currency
        ),
        url=brand.get_url(f"s/{store_id}/menu", product_id=product_schema.id),
        thumbnail_url=product_schema.thumbnail_url,
        attributes=[
            await make_check_item_attribute_from_order(
                product_schema, x, lang, order_product.quantity, currency
            )
            for
            x, _ in
            await crud.get_order_product_attributes(order_product.id)
        ],
        modifiers=[CheckItemModifier(
            id=modifier.id,
            name=modifier.name,
            value=modifier.value,
            orig_value=modifier.orig_value,
        ) for modifier in product_schema.modifiers.values()],
        modifiers_str=" ".join(
            [modifier.value for modifier in product_schema.modifiers.values()]
        )
    )


async def make_check_item_attribute_from_order(
        product_schema: schemas.ProductSchema,
        order_attribute: OrderAttribute,
        lang: str,
        product_qty: int,
        currency: str | None = None,
):
    attribute_code = None
    attribute_name = None

    group_schema = next(
        (
            x for x in product_schema.attribute_groups
            if x.attribute_group_id == order_attribute.group_attribute_id
        ), None
    )
    if group_schema:
        attribute = next(
            (
                x for x in group_schema.attributes
                if x.id == order_attribute.attribute_id
            ), None
        )
        if attribute:
            attribute_code = attribute.attribute_id
            attribute_name = attribute.name

    if not attribute_name:
        attribute = await StoreAttribute.get(order_attribute.attribute_id)
        if attribute:
            attribute_code = attribute.attribute_id
            attribute_name = attribute.name

    if not attribute_code:
        attribute_code = "-"
    if not attribute_name:
        attribute_name = order_attribute.name

    return CheckItemAttribute(
        code=attribute_code,
        name=attribute_name,
        quantity=order_attribute.quantity * product_qty,
        price=format_sum(order_attribute.price_impact / 100, lang, False, currency),
        sum=format_sum(
            order_attribute.price_impact * order_attribute.quantity / 100 * product_qty,
            lang, False, currency
        ),
    )
