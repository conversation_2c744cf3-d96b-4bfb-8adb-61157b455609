import json
import logging

from incust_client_api_client.api.loyalty_api import LoyaltyApi
from incust_terminal_api_client.api.settings_api import SettingsApi

from core.loyalty.incust_client_adapter import (
    run_with_incust_client_api,
    run_with_incust_terminal_api,
)
from core.messangers_adapters import FSMContext
from db.models import Brand, ClientBot, LoyaltySettings, User
from utils.text import f
from whatsapp_bot.bot.deep_links import incust_referral_deep_link_no_store
from ..callback_data import (
    IncustReferralCallbackData, IncustReferralQrOrLinkCallbackData,
    IncustReferralShareMessage,
)
from ..functions import send_referral_share_message
from ..referral_processor import IncustReferralProcessor
from ...incust.helpers import get_currency_from_store_or_brand
from ...messangers_adapters import types


async def incust_referral_united_handler(
    message: types.AnswerObject, state: FSMContext, user: User,
    bot: ClientBot, lang: str,
    keyboard: types.Keyboard,
    referral_data: IncustReferralCallbackData,
):
    ref_code = referral_data.referral_code
    brand_id = referral_data.brand_id
    store_id = referral_data.store_id

    initiator = "deep_link"
    debug_data = {
        "ref_code": ref_code,
        "brand_id": brand_id,
        "store_id": store_id,
        "inviting_user_first_name": user.first_name,
        "inviting_user_last_name": user.last_name,
        "inviting_user_id": user.id,
        "bot_id": bot.id,
        "bot_name": bot.display_name,
        "main_keyboard_type": str(type(keyboard)),
    }

    try:
        brand = await Brand.get(brand_id)
        currency = await get_currency_from_store_or_brand(brand.id)

        loyalty_settings = await LoyaltySettings.get_loyalty_settings_for_context(
            brand_id=brand.id,
            store_id=store_id,
            group_id=brand.group_id,
        )
        
        if not loyalty_settings:
            raise Exception("Loyalty settings not found for brand")
        
        processor = IncustReferralProcessor(message, state, bot, user, lang, keyboard, store_id=store_id)
        await processor.accept_invitation(
            ref_code, loyalty_settings, currency=currency, debug_data=debug_data, initiator=initiator
        )
        return None
    except Exception as err:
        if hasattr(err, "message") and getattr(err, "message"):
            debugger = logging.getLogger("debugger.incust.referral-invite")
            debug_text = (f"sending referral inviting message with keyboard "
                          f"incust_referral_united_handler_error_{initiator}")
            debug_text += f"\n {json.dumps(debug_data, indent=4, ensure_ascii=False)}"
            debugger.debug(debug_text)

            return await message.answer(
                await f("regular error text", lang, message=err.message),
                reply_markup=keyboard,
            )
        logging.error(err, exc_info=True)
        return await message.answer(
            await f("incust loyalty referral accept unknown error", lang), reply_markup=keyboard
        )


async def incust_referral_qr_or_link_united_handler(
    message: types.AnswerObject, state: FSMContext, user: User,
    bot: ClientBot, lang: str,
    keyboard: types.Keyboard,
    referral_data: IncustReferralQrOrLinkCallbackData,
):
    ref_code = referral_data.referral_code
    content_type = referral_data.content_type
    is_web = referral_data.is_web
    with_qr = referral_data.with_qr

    processor = IncustReferralProcessor(message, state, bot, user, lang, keyboard, store_id=referral_data.store_id)

    await processor.show_qr_or_link(
        ref_code, 
        content_type,
        is_web=is_web,
        with_qr=with_qr,
    )


async def get_share_and_earn_message_united_handler(
    message: types.AnswerObject, lang: str, bot: ClientBot,
    user: User, incust_share_msg: IncustReferralShareMessage,
):

    brand = await Brand.get(incust_share_msg.brand_id)

    loyalty_settings = await LoyaltySettings.get_loyalty_settings_for_context(
        brand_id=brand.id,
        group_id=brand.group_id,
    )
    
    if not loyalty_settings:
        logging.warning(f"Loyalty settings not found for brand {brand.id}")
        return

    if bot.bot_type == "telegram":
        url = f"https://t.me/{bot.username}?start=referral-{incust_share_msg.referral_code}"
    else:
        url = bot.whatsapp_link(incust_referral_deep_link_no_store.new(incust_share_msg.referral_code))

    loyalty_settings_response = await run_with_incust_client_api(
        loyalty_settings,
        user,
        LoyaltyApi.incust_controllers_client_client_loyalty_settings,
        loyalty_id=loyalty_settings.loyalty_id,
        lang=lang
    )

    loyalty_photo = None
    if (loyalty_settings_response and 
        hasattr(loyalty_settings_response, 'referral_program') and
        loyalty_settings_response.referral_program and
        hasattr(loyalty_settings_response.referral_program, 'referral_logo') and
        loyalty_settings_response.referral_program.referral_logo):
        
        if hasattr(loyalty_settings_response.referral_program.referral_logo, 'url'):
            loyalty_photo = loyalty_settings_response.referral_program.referral_logo.url
    
    # Якщо немає фото в referral програмі, отримуємо з terminal settings
    if not loyalty_photo:
        try:
            terminal_settings = await run_with_incust_terminal_api(
                loyalty_settings,
                SettingsApi.incust_controllers_term_api_settings,
            )
            
            if (terminal_settings and 
                terminal_settings.loyalty and 
                terminal_settings.loyalty.photos and 
                len(terminal_settings.loyalty.photos) >= 1):
                loyalty_photo = terminal_settings.loyalty.photos[0]
                
        except Exception as e:
            logging.error(f"Failed to get terminal settings: {e}", exc_info=True)

    await send_referral_share_message(
        bot,
        lang,
        loyalty_settings_response,  # Передаємо оригінальну схему з нових клієнтів
        message,
        loyalty_photo,
        url,
        incust_share_msg.referral_code,
        user,
    )
