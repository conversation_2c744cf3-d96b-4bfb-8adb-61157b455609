import json
import logging

import aiowhatsapp as wa
from incust_client_api_client.api.loyalty_api import LoyaltyApi

from client.main.functions import set_menu_button
from core.loyalty.incust_client_adapter import (
    get_or_create_incust_customer,
    run_with_incust_client_api,
    run_with_incust_terminal_api,
)
from core.messangers_adapters import FSMContext
from db import crud
from db.models import ClientBot, LoyaltySettings, User
from schemas import LoyaltySettingsClientSchema, ReferrerUserClient
from utils.numbers import format_currency
from utils.qrcode import generate_qr_code
from utils.text import f, html_to_markdown
from whatsapp_bot.bot.deep_links import incust_referral_deep_link_no_store
from .keyboards import (
    get_incust_referral_keyboard,
    get_incust_referral_qr_or_link_keyboard,
)
from ..messangers_adapters import types

debugger = logging.getLogger("debugger.incust.referral-invite")


class IncustReferralProcessor:

    def __init__(
            self,
            message: types.AnswerObject,
            state: FSMContext | None,
            bot: ClientBot,
            user: User, lang: str,
            keyboard: types.Keyboard | None = None,
            referral_code: str | None = None,
            store_id: int | None = None,
    ):
        self.message: types.AnswerObject = message
        self.state: FSMContext | None = state
        self.bot: ClientBot = bot
        self.user: User = user
        self.lang: str = lang
        self.main_keyboard = keyboard
        self.referral_code: str = referral_code
        self.store_id = store_id

    async def process(self):
        try:
            if self.state:
                await self.state.finish()
            brand = await crud.get_brand_by_group(self.bot.group_id)
            if not await brand.is_incust:
                return await self.message.answer(
                    await f(
                        "loyalty not connected text", self.lang, brand_name=brand.name
                    )
                )

            is_valid_ref_code = self.referral_code.startswith('5') and len(
                self.referral_code
            ) == 12
            if not is_valid_ref_code:
                return await self.message.answer(
                    await f("incust loyalty not valid referral code", self.lang)
                )

            # Отримуємо налаштування лояльності
            settings = await LoyaltySettings.get_loyalty_settings_for_context(
                brand_id=brand.id,
                store_id=self.store_id
            )
            
            if not settings:
                return await self.message.answer(
                    await f("loyalty not connected text", self.lang, brand_name=brand.name)
                )
            
            # Синхронізуємо користувача
            incust_customer = await get_or_create_incust_customer(self.user, settings, self.lang)

            # Отримуємо налаштування лояльності з InCust
            incust_loyalty_settings = await self.__get_loyalty_settings(settings)
            if (
                    not incust_loyalty_settings.referral_program or
                    incust_loyalty_settings.referral_program.active != 1
            ):
                return await self.message.answer(
                    await f("incust loyalty referral disabled error", self.lang)
                )

            invitation_info = await run_with_incust_client_api(
                settings,
                self.user,
                LoyaltyApi.incust_controllers_client_client_referral_program_invitation_info,
                code=self.referral_code
            )
            same_user = False
            if (
                    incust_customer and hasattr(incust_customer, 'id') and
                    invitation_info.referrer_user.id == incust_customer.id
            ):
                same_user = True

            # Отримуємо налаштування терміналу окремим запитом
            terminal_settings = await self.__get_terminal_settings(settings)

            title = incust_loyalty_settings.referral_program.title
            desc = incust_loyalty_settings.referral_program.description
            if incust_loyalty_settings.referral_program.referral_title:
                title = incust_loyalty_settings.referral_program.referral_title
            if hasattr(incust_loyalty_settings.referral_program, 'referral_description') and incust_loyalty_settings.referral_program.referral_description:
                desc = incust_loyalty_settings.referral_program.referral_description

            text = f"<b>{title}</b>\n\n"
            text += desc

            if not same_user:
                keyboard = await get_incust_referral_keyboard(
                    brand.id, self.lang, self.referral_code, self.bot.bot_type,
                    store_id=self.store_id,
                )
                referrer_id = self.get_referrer_user_id(invitation_info.referrer_user)
                referrer_text = await f(
                    "incust loyalty invited by header", self.lang,
                    referrer_id=referrer_id
                )
                text += f"\n\n<b>{referrer_text}</b>"
            else:
                keyboard = await get_incust_referral_qr_or_link_keyboard(
                    self.referral_code, self.lang,
                    bot_type=self.bot.bot_type, store_id=self.store_id,
                    brand_id=brand.id
                )
                text += "\n\n" + await f(
                    'incust loyalty referral same user share header', self.lang
                )

            if terminal_settings.loyalty.photos and (
                    len(
                        terminal_settings.loyalty.photos
                    ) > 0 or loyalty_settings.referral_program.referral_logo):
                if (loyalty_settings.referral_program.referral_logo and
                        loyalty_settings.referral_program.referral_logo.url):
                    photo = loyalty_settings.referral_program.referral_logo.url
                else:
                    photo = terminal_settings.loyalty.photos[0]
                if self.bot.bot_type == "telegram":
                    await self.message.answer_photo(
                        photo=photo,
                        caption=text,
                        reply_markup=keyboard,
                    )
                    return None
                elif self.bot.bot_type == "whatsapp":
                    text = html_to_markdown(text)
                    await self.message.answer_image(
                        image=photo,
                        caption=text,
                        reply_markup=keyboard,
                    )
                    return None
                return None
            else:
                if self.bot.bot_type == "whatsapp":
                    text = html_to_markdown(text)
                    return await self.message.answer(text, reply_markup=keyboard)

                await self.message.answer(
                    text,
                    reply_markup=keyboard,
                )
                return None
        except Exception as e:
            logging.error(e, exc_info=True)
            return None

    async def accept_invitation(
            self, referral_code: str, loyalty_settings: LoyaltySettings,
            currency: str, debug_data: dict, initiator: str = "unknown"
    ):
        try:
            ref_code = referral_code or self.referral_code

            from incust_client_api_client.models.incust_controllers_client_client_referral_program_invitation_request import IncustControllersClientClientReferralProgramInvitationRequest
            
            request_data = IncustControllersClientClientReferralProgramInvitationRequest(
                action="accept"
            )
            
            response = await run_with_incust_client_api(
                loyalty_settings,
                self.user,
                LoyaltyApi.incust_controllers_client_client_referral_program_invitation,
                code=ref_code,
                incust_controllers_client_client_referral_program_invitation_request=request_data
            )
        except Exception as ex:
            if hasattr(ex, 'msg_text'):
                return await self.message.answer(
                    await f("regular error text", self.lang, message=ex.msg_text)
                )
            else:
                logging.error(f"Failed to accept invitation: {ex}", exc_info=True)
                return await self.message.answer(
                    await f("incust loyalty accept invitation unknown error", self.lang)
                )

        if (
                response and
                (
                        response.message == "Ok" or
                        (not response.message and response.code == ref_code)
                )
        ):
            # Отримуємо налаштування лояльності для отримання інформації про винагороди
            incust_loyalty_for_benefits = await self.__get_loyalty_settings(loyalty_settings)

            benefits_text = await self.__get_benefits(
                incust_loyalty_for_benefits, currency
            )
            text = await f("incust loyalty accepted invitation success", self.lang)
            text += f"\n\n{benefits_text}"

            if self.bot.bot_type == "telegram":
                await set_menu_button(self.bot, self.user, self.lang)

                debug_text = (
                    f"sending referral inviting message with keyboard "
                    f"accept_invitation_success_{initiator}"
                )
                debug_text += "\n " + json.dumps(
                    debug_data, indent=4, ensure_ascii=False
                )
                debugger.debug(debug_text)

                await self.message.answer(text, reply_markup=self.main_keyboard)
                try:
                    await self.message.delete()
                except Exception as ex:
                    logging.error(ex, exc_info=True)
                finally:
                    return
            elif self.bot.bot_type == "whatsapp":
                text = html_to_markdown(text)
                return await self.message.answer(text)

        # if incust return error message
        if response and response.message:
            text = response.message
        else:
            text = await f("incust loyalty accept invitation unknown error", self.lang)
        debug_text = (
            f"sending referral inviting message with keyboard "
            f"accept_invitation_error_{initiator}"
        )
        debug_text += f"\n {json.dumps(debug_data, indent=4, ensure_ascii=False)}"
        debugger.debug(debug_text)
        await self.message.answer(text, reply_markup=self.main_keyboard)

    async def __get_benefits(
            self, loyalty_settings: LoyaltySettingsClientSchema, currency: str
    ) -> str:
        if loyalty_settings.referral_program.referral_reward_type == "coupon":
            text = f'{await f("loyalty vouchers awarded after header", self.lang)}:'
            text += ("\n" +
                     loyalty_settings.referral_program.referral_reward_coupon_batch
                     .title)
            return text

        elif (
                loyalty_settings.referral_program.referral_reward_type in
                ["regular-bonuses", "promotional-bonuses"]
        ):
            text = f'{await f("loyalty bonuses awarded after header", self.lang)}:'
            value_text = format_currency(
                loyalty_settings.referral_program.referral_reward_value, currency,
                locale=self.lang
            )
            text += f"\n{value_text}"
            return text

        elif (
                loyalty_settings.referral_program.referral_reward_type ==
                "special-account"
        ):
            awarded_text = await f(
                "loyalty account awarded after header", self.lang,
                account=loyalty_settings.referral_reward_special_account.title
            )
            text = f'{awarded_text}:'
            value_text = format_currency(
                loyalty_settings.referral_program.referral_reward_value, currency,
                locale=self.lang
            )
            text += f"\n{value_text}"
            return text

        else:
            return ""

    async def __get_loyalty_settings(
            self, settings: LoyaltySettings
    ) -> LoyaltySettingsClientSchema:
        """Отримує налаштування лояльності з InCust API."""
        if not settings.loyalty_id:
            return await self.message.answer(
                await f("incust loyalty not found loyalty id error", self.lang)
            )
        try:
            incust_loyalty_settings = await run_with_incust_client_api(
                settings,
                self.user,
                LoyaltyApi.incust_controllers_client_client_loyalty,
                loyalty_id=settings.loyalty_id,
                lang=self.lang
            )
            return incust_loyalty_settings
        except Exception as ex:
            logging.error(f"Failed to get loyalty settings: {ex}", exc_info=True)
            return await self.message.answer(
                await f("regular error text", self.lang, message=str(ex))
            )
    
    async def __get_terminal_settings(self, settings: LoyaltySettings):
        """Отримує налаштування терміналу з InCust API."""
        try:
            # Отримуємо налаштування терміналу через Terminal API
            from incust_terminal_api_client.api.settings_api import SettingsApi
            terminal_settings = await run_with_incust_terminal_api(
                settings,
                SettingsApi.incust_controllers_term_api_settings
            )
            return terminal_settings
        except Exception as ex:
            logging.error(f"Failed to get terminal settings: {ex}", exc_info=True)
            # Повертаємо порожній об’єкт як запасний варіант
            return {}

    @classmethod
    def get_referrer_user_id(cls, referrer_user: ReferrerUserClient) -> str:
        if referrer_user.name:
            return referrer_user.name
        if referrer_user.phone:
            string_length = len(referrer_user.phone)
            if string_length <= 3:
                return referrer_user.phone
            return referrer_user.phone[:-3] + '***'

        if referrer_user.email:
            string_length = len(referrer_user.email)
            if string_length <= 3:
                return referrer_user.email
            return '***' + referrer_user.email[3:]

        if referrer_user.external_id:
            string_length = len(referrer_user.external_id)
            if string_length <= 3:
                return referrer_user.external_id
            return referrer_user.external_id[:-3] + '***'

        string_length = len(referrer_user.id or "")
        if string_length <= 3:
            return referrer_user.id or ""

        return referrer_user.id[:-3] + '***'

    async def show_qr_or_link(
            self, referral_code: str,
            content_type: str,
            is_web: str = "",
            with_qr: str = "",
    ):
        if self.bot.bot_type == "telegram":
            url = f"https://t.me/{self.bot.username}?start=referral-{referral_code}"
            if self.store_id:
                url = url + f"store_id-{self.store_id}"
        elif self.bot.bot_type == "whatsapp":
            url = self.bot.whatsapp_link(
                incust_referral_deep_link_no_store.new(referral_code)
            )
        else:
            return self.message.answer("unknown bot type")  # TODO: add localization

        if is_web:
            brand = await crud.get_brand_by_group(self.bot.group_id)
            url = brand.get_url(
                f'shareAndEarn/{referral_code}',
                bot_id=self.bot.id,
            )

        match content_type:
            case "q":
                photo = generate_qr_code(url, convert_8_bit_channel=True)
                text = await f("your qr code referral header", self.lang)
                if self.bot.bot_type == "telegram":
                    return await self.message.answer_photo(photo=photo, caption=text)
                elif self.bot.bot_type == "whatsapp":
                    wa_bot = wa.WhatsappBot(self.bot.token, self.bot.whatsapp_from)
                    document_media = await wa_bot.upload_media(photo, "image/png")
                    image = wa.types.MediaCaption(id=document_media.id, caption=text)
                    return await self.message.answer_image(image)
            case "l":
                text = ""
                photo = None
                if with_qr:
                    text += await f(
                        'your link with qr referral header', self.lang
                    ) + "\n"
                    photo = generate_qr_code(url, convert_8_bit_channel=True)
                text += await f("your link referral header", self.lang)
                text += f"\n<code>{url}</code>"
                if self.bot.bot_type == "whatsapp":
                    text = html_to_markdown(text)
                    if with_qr and photo:
                        wa_bot = wa.WhatsappBot(self.bot.token, self.bot.whatsapp_from)
                        document_media = await wa_bot.upload_media(photo, "image/png")
                        image = wa.types.MediaCaption(
                            id=document_media.id, caption=text
                        )
                        return await self.message.answer_image(image)
                if with_qr and photo:
                    return await self.message.answer_photo(photo=photo, caption=text)

                return await self.message.answer(text)
            case _:
                return self.message.answer(
                    "unknown content type for referral content"
                )  # TODO: add localization
