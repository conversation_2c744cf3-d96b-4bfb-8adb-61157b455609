from typing import Literal

from aiowhatsapp import types

from client.db_funcs import is_incust
from db import crud
from db.models import Brand, ClientBot, CustomMenuButton, Group, User
from utils.text import f
from utils.translator import t
from .types import (
    MenuKeyboardAdditionalSectionsType, MenuKeyboardConf,
    MenuKeyboardSections,
)
from ..callback_data import CustomMenuButtonCallbackData
from ...custom_texts import ct
from ...custom_texts.models import ClientBotCustomTextsModel
from ...incust_referral.functions import is_incust_referral_active


async def get_scan_receipts_enabled(brand: Brand) -> bool:
    settings = await crud.get_brand_settings_by_type(brand.id, 'scan_receipts_enabled')
    return (
        bool(settings) and settings[0].value_data in ("1", "True")
        if settings else False
    )


class MenuKeyboardBuilder:
    def __init__(
            self,
            user: User,
            bot: ClientBot,
            lang: str,
            ct_obj: ClientBotCustomTextsModel | None = None,
            # if ct_obj is not specified,
            # it will be loaded on first purpose
    ):
        self.user: User = user
        self.bot: ClientBot = bot
        self._brand: Brand | None = None
        self.lang: str = lang
        self._ct_obj: ClientBotCustomTextsModel | None = ct_obj

        self.additional_sections = MenuKeyboardAdditionalSectionsType()

    @property
    async def brand(self) -> Brand:
        if not self._brand:
            self._brand = await Brand.get(group_id=self.bot.group_id)
        return self._brand

    @property
    async def ct_obj(self):
        if not self._ct_obj:
            self._ct_obj = await ClientBotCustomTextsModel.from_object(self.bot)
        return self._ct_obj

    def add_sections(
            self,
            *sections: types.Section,
            position: Literal["start", "end", "main_section"] = "start",
            main_section_name: str | None = None,
            main_section_position: Literal["before", "after"] | None = None,
            # default is after
    ):
        if position != "main_section":
            if main_section_position:
                raise ValueError(
                    "main_section_position can be specified only when position is "
                    "main_section"
                )
            if main_section_name:
                raise ValueError(
                    "main_section_name cannot be specified when position is "
                    "main_section"
                )

        match position:
            case "start":
                self.additional_sections.start.extend(sections)
            case "end":
                self.additional_sections.end.extend(sections)
            case "main_section":
                if not main_section_name:
                    raise ValueError(
                        "main_section_name is required when position is main_section"
                    )
                if not main_section_position:
                    main_section_position = "after"

                from_main_section = self.additional_sections.from_main_sections[
                    main_section_name]
                match main_section_position:
                    case "before":
                        from_main_section.before.extend(sections)
                    case "after":
                        from_main_section.after.extend(sections)

    async def build(
            self,
            button: str | None = None,  # if not specified, default will be used,
            conf: MenuKeyboardConf | None = None,
    ):
        if not conf:
            conf = MenuKeyboardConf()

        if button is None:
            button = await ct(
                await self.ct_obj, self.lang, "whatsapp", "menu", "button"
            )

        keyboard = types.ListKeyboard(
            button=button,
            sections=self.additional_sections.start
        )

        sections = MenuKeyboardSections()

        ct_obj = await self.ct_obj

        if conf.shop:
            brand = await self.brand
            if brand and await crud.get_stores(brand.id, operation="exists"):
                shop_button = await ct(ct_obj, self.lang, "shop", "button")
                if shop_button:
                    sections.shop = types.Section(
                        title=await ct(ct_obj, self.lang, "shop", "section title"),
                        rows=[
                            types.SectionRow(
                                id="shop",
                                title=shop_button,
                                description=await ct(
                                    ct_obj, self.lang, "shop", "button description"
                                )
                            ),
                        ]
                    )

        if conf.chat:
            start_chat_button = await ct(ct_obj, self.lang, "chat", "start button")
            if start_chat_button:
                sections.chat = types.Section(
                    title=await f("wa main menu chat section title", self.lang),
                )

                if start_chat_button:
                    sections.chat.add_rows(
                        types.SectionRow(
                            id="chat",
                            title=start_chat_button,
                            description=await ct(
                                ct_obj, self.lang, "chat", "start button description"
                            )
                        )
                    )

        if conf.profile:

            brand = await self.brand
            is_scan_receipts_enabled = await get_scan_receipts_enabled(brand)
            is_referral_active = await is_incust_referral_active(
                self.user, brand, self.lang
            )

            review_button = await ct(
                ct_obj, self.lang, "main", "leave review button"
            ) if (
                    self.bot.group.review_type
                    is not None and
                    self.bot.group.review_type
                    != "off" and False) else None

            is_incust = await brand.is_incust if brand else False

            if brand and is_incust:
                benefits_button = await ct(ct_obj, self.lang, "main", "benefits button")
                share_and_earn_button = await ct(
                    ct_obj, self.lang, "shop", "share and earn button"
                ) if (
                    is_referral_active) else None
                wallet_button = await ct(ct_obj, self.lang, "main", "wallet button")
            else:
                benefits_button = None
                share_and_earn_button = None
                wallet_button = None

            profile_button = await ct(
                ct_obj, self.lang, "main", "profile button"
            ) if brand else None
            scan_receipt_button = await ct(
                ct_obj, self.lang, "main", "scan receipt button"
            ) if is_scan_receipts_enabled else None

            ewallet_account_button = None
            if brand and is_incust:
                ewallets = await crud.get_ewallet_list(
                    bot_id=self.bot.id, user_id=self.user.id, operation="count"
                )
                if ewallets:
                    ewallet_account_button = await ct(self.bot, self.lang, "main", "ewallet button")

            if any(
                    (
                            review_button,
                            profile_button,
                            benefits_button,
                            share_and_earn_button,
                            wallet_button,
                            scan_receipt_button,
                            ewallet_account_button,
                    )
            ):
                sections.profile = types.Section(
                    title=await f("wa main menu profile section title", self.lang),
                )

                if review_button:
                    sections.profile.add_rows(
                        types.SectionRow(
                            id="review",
                            title=review_button,
                            description=await ct(
                                ct_obj, self.lang, "main", "review button description"
                            )
                        )
                    )

                if profile_button:
                    sections.profile.add_rows(
                        types.SectionRow(
                            id="profile",
                            title=profile_button,
                            description=await ct(
                                ct_obj, self.lang, "main", "profile button description"
                            )
                        )
                    )

                if benefits_button:
                    sections.profile.add_rows(
                        types.SectionRow(
                            id="benefits",
                            title=benefits_button,
                            description=await ct(
                                ct_obj, self.lang, "main", "benefits button description"
                            )
                        )
                    )

                if share_and_earn_button:
                    sections.profile.add_rows(
                        types.SectionRow(
                            id="share_and_earn",
                            title=share_and_earn_button,
                            description=await ct(
                                ct_obj, self.lang, "main",
                                "share and earn button description",
                            )
                        )
                    )

                if wallet_button:
                    sections.profile.add_rows(
                        types.SectionRow(
                            id="wallet",
                            title=wallet_button,
                            description=await ct(
                                ct_obj, self.lang, "main", "wallet button description",
                            )
                        )
                    )

                if scan_receipt_button:
                    sections.profile.add_rows(
                        types.SectionRow(
                            id="scan_receipt",
                            title=scan_receipt_button,
                            description=await ct(
                                ct_obj, self.lang, "main",
                                "scan receipt button description",
                            )
                        )
                    )

                if ewallet_account_button:
                    sections.profile.add_rows(
                        types.SectionRow(
                            id="ewallet_account",
                            title=ewallet_account_button,
                            description=await ct(
                                self.bot, self.lang, "main", "ewallet button description"
                            ),
                        )
                    )

        menu_buttons = await CustomMenuButton.get_list(
            bot_id=self.bot.id,
        )
        if menu_buttons:
            added_sections = 0

            for _, section in sections.iterate_items():
                if section:
                    added_sections += section.rows_count

            added_sections += len(self.additional_sections.start)
            added_sections += len(self.additional_sections.end)

            for from_main_section in (
                    self.additional_sections.from_main_sections.values()):
                added_sections += len(from_main_section.before)
                added_sections += len(from_main_section.after)

            if added_sections < 10:
                sections.extra = types.Section(
                    title=await f("wa main menu extra section title", self.lang),
                )

                group = await Group.get(self.bot.group_id)

                menu_buttons = menu_buttons[:10 - added_sections]
                sections.extra.add_rows(
                    *[types.SectionRow(
                        id=CustomMenuButtonCallbackData(button_id=button.id).to_str(),
                        title=await t(
                            button, self.lang, group.lang, field_name="text",
                            group_id=group.id,
                            is_auto_translate_allowed=group.is_translate
                        ),
                    ) for button in menu_buttons]
                )

        for section_name, section in sections.iterate_items():
            from_main_section = self.additional_sections.from_main_sections.get(
                section_name
            )
            if from_main_section and from_main_section.before:
                keyboard.add_sections(*from_main_section.before)

            if section:
                keyboard.add_sections(section)

            if from_main_section and from_main_section.after:
                keyboard.add_sections(*from_main_section.after)

        if self.additional_sections.end:
            keyboard.add_sections(*self.additional_sections.end)

        return keyboard
