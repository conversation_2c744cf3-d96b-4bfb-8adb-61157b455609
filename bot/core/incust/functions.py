import logging
from datetime import datetime

from db import crud
from db.models import (
    Brand, BrandSettings, EWallet, Group, InvoiceTemplate, MenuInStore, Store,
    StoreOrder, User, LoyaltySettings,
)
from schemas import (
    CustomerChargeOrWriteOffBenefitsType, ExtSysSetTypes,
    IncustCheckSchema,
)
from utils.platform_admins import send_message_to_platform_admins
from utils.text import f
from .exceptions import (
    BaseIncustTopupError, IncustError, IncustProhibitRedeemingBonusesError,
    IncustProhibitRedeemingCouponsError, IncustTopupCantWriteoffWithoutOwnerError,
    IncustTopupNoSpecialAccountForProvidedTypeError, IncustTopupNotFoundOwnerCardError,
    IncustTopupNotFoundOwnerSpecialAccountError, IncustTopupNotFoundSpecialAccountError,
    IncustTopupOwnerNotEnoughMoneyError,
)
from core.loyalty.incust_client_adapter import (
    run_with_incust_client_api,
    run_with_incust_terminal_api,
    get_or_create_incust_customer,
)
from incust_client_api_client.api.card_info_api import CardInfoApi
from incust_terminal_api_client.api.customer_benefits_api import CustomerBenefitsApi
from incust_client_api_client.models.card_info import CardInfo
from incust_terminal_api_client.models.customer_benefits_top_up_operation_data import CustomerBenefitsTopUpOperationData
from incust_terminal_api_client.models.customer_benefits_write_off_operation_data import CustomerBenefitsWriteOffOperationData
from incust_terminal_api_client.models.transaction import Transaction as IncustTerminalTransaction


async def update_incust_user_lang(user: User, brand: Brand, lang: str):
    """Оновлення мови користувача через новий InCust API."""
    settings = await LoyaltySettings.get_loyalty_settings_for_context(
        brand_id=brand.id
    )
    if not settings:
        logging.warning(f"Loyalty settings not found for brand {brand.id}")
        return False
    
    try:
        await get_or_create_incust_customer(user, settings, lang)
        return True
    except Exception as e:
        logging.error(f"Failed to update user language: {e}", exc_info=True)
        return False


async def update_incust_birth_date(
        user: User, brand: Brand, lang: str, birth_date: datetime | None = None
):
    """Оновлення дати народження користувача через новий InCust API."""
    settings = await LoyaltySettings.get_loyalty_settings_for_context(
        brand_id=brand.id
    )
    if not settings:
        logging.warning(f"Loyalty settings not found for brand {brand.id}")
        return
    
    try:
        await get_or_create_incust_customer(user, settings, lang)
        # Оновлення дати народження відбувається в процесі синхронізації
        # якщо воно потрібне, буде додано через відповідний API метод
    except Exception as e:
        logging.error(f"Failed to update birth date: {e}", exc_info=True)


async def topup_account_with_store_order(
        order: StoreOrder, brand: Brand, user: User, lang: str
):
    order_products = await crud.get_order_products(order.id)
    if order_products:
        if any(order_product.incust_account for order_product in order_products):
            for order_product in order_products:
                if order_product.incust_account:
                    err_text = ""
                    try:
                        user_transaction, owner_transaction = await topup_account(
                            round(
                                (
                                        order_product.total_sum -
                                        order_product.topup_charge) / 100,
                                2
                            ),
                            "special-account",
                            user, lang, brand, card=order_product.incust_card,
                            special_account_id=order_product.incust_account.get(
                                "id",
                                None
                            ),
                            terminal_api_key=order_product.incust_pay_term_api_key,
                            terminal_url=order_product.incust_pay_term_url,
                        )
                        await order_product.update(
                            {
                                "incust_user_transaction": user_transaction.dict(),
                                "incust_owner_transaction":
                                    owner_transaction.dict()
                            }
                        )
                    except BaseIncustTopupError as ex:
                        err_text = await f(ex.text_variable, lang, **ex.text_kwargs)
                        logging.error(ex, exc_info=True)
                    except IncustError as ex:
                        err_text = ex.msg_text
                        logging.error(ex, exc_info=True)
                    except Exception as ex:
                        err_text = await f("loyalty topup unknown error", lang)
                        logging.error(ex, exc_info=True)

                    if err_text:
                        await order_product.update(
                            {
                                "is_topup_error": True,
                            }
                        )
                        err_text = (
                            f"{await f('loyalty topup base header error', lang)}:"
                            f"\n{err_text}"
                        )
                        err_text += "\n" + await f(
                            'web store orders order number text', lang,
                            order_id=order.id
                        ) + ", " + brand.name
                        await send_message_to_platform_admins(
                            err_text, force_to_bot=False
                        )


async def topup_account(
        amount: float, transaction_type: CustomerChargeOrWriteOffBenefitsType,
        user: User, lang: str, brand: Brand, only_charge: bool = False,
        card: str | None = None, currency: str | None =
        None,
        terminal_api_key:
        str | None = None,
        terminal_url: str | None =
        None, special_account_id: str | None = None
) -> tuple[IncustTerminalTransaction, IncustTerminalTransaction | None]:
    """Поповнення рахунку через новий InCust API."""
    if transaction_type == "special-account" and not special_account_id:
        raise IncustTopupNoSpecialAccountForProvidedTypeError()

    # Отримуємо налаштування лояльності
    settings = await LoyaltySettings.get_loyalty_settings_for_context(
        brand_id=brand.id,
        terminal_api_key=terminal_api_key,
    )
    if not settings:
        raise IncustError("Loyalty settings not found")

    owner_transaction = None

    if not only_charge:
        group = await Group.get(brand.group_id)
        owner = await User.get_by_id(group.owner_id)
        if owner:
            if special_account_id:
                await validate_special_account_exist(
                    brand, lang, terminal_api_key, terminal_url, special_account_id,
                    amount, owner, is_owner=True,
                )

                write_off_schema = CustomerBenefitsWriteOffOperationData(
                    amount=amount,
                    type=transaction_type,
                    id=owner.incust_external_id,
                    id_type="external-id",
                    special_account_id=special_account_id,
                    currency=currency,
                )

                owner_transaction = await run_with_incust_terminal_api(
                    settings,
                    CustomerBenefitsApi.incust_controllers_term_api_write_off_benefits,
                    write_off_schema
                )
        else:
            raise IncustTopupCantWriteoffWithoutOwnerError(special_account_id)

    top_up_schema = CustomerBenefitsTopUpOperationData(
        amount=amount,
        type=transaction_type,
        id=card if card else user.incust_external_id,
        id_type="card" if card else "external-id",
        special_account_id=special_account_id,
        currency=currency,
    )

    user_transaction = await run_with_incust_terminal_api(
        settings,
        CustomerBenefitsApi.incust_controllers_term_api_charge_benefits,
        top_up_schema
    )

    return user_transaction, owner_transaction


async def validate_special_account_exist(
        brand: Brand, lang: str, terminal_api_key: str, terminal_server_api_url: str,
        special_account_id: str, amount: float, user: User | None = None,
        is_owner: bool = False,
):
    """Валідація існування спеціального рахунку через новий InCust API."""
    settings = await LoyaltySettings.get_loyalty_settings_for_context(
        brand_id=brand.id
    )
    if not settings:
        raise IncustError("Loyalty settings not found")

    owner_card_info: CardInfo = await run_with_incust_client_api(
        settings,
        user,
        CardInfoApi.incust_controllers_client_client_card_info,
        lang=lang
    )
    if not owner_card_info:
        raise IncustTopupNotFoundOwnerCardError()

    special_account = None
    if special_account_id:
        for account in (owner_card_info.specials or []):
            if account.id == special_account_id:
                special_account = account
                break

    if not special_account:
        if not is_owner:
            raise IncustTopupNotFoundSpecialAccountError(special_account_id)
        else:
            raise IncustTopupNotFoundOwnerSpecialAccountError(special_account_id)

    if is_owner and special_account.amount < amount:
        raise IncustTopupOwnerNotEnoughMoneyError(
            special_account.title, special_account_id
        )


async def validate_prohibit_redeeming_bonuses(
        incust_check: IncustCheckSchema, brand_id: int
):
    brand_settings = await BrandSettings.get_by_brand_and_type(
        brand_id=brand_id,
        type_data=ExtSysSetTypes.incust_prohibit_redeeming_bonuses.value,
    )
    if brand_settings and brand_settings.value_data == "true":
        if incust_check.bonuses_redeemed_amount:
            raise IncustProhibitRedeemingBonusesError()


async def validate_prohibit_redeeming_coupons(
        incust_check: IncustCheckSchema, brand_id: int
):
    brand_settings = await BrandSettings.get_by_brand_and_type(
        brand_id=brand_id,
        type_data=ExtSysSetTypes.incust_prohibit_redeeming_coupons.value,
    )
    if brand_settings and brand_settings.value_data == "true":
        if incust_check.redeemed_coupons:
            raise IncustProhibitRedeemingCouponsError()


async def get_terminal_data_from_object(
        store_id: int | None = None, menu_in_store: MenuInStore | None = None,
        invoice_template: InvoiceTemplate | None = None,
        ewallet: EWallet | None = None,
) -> tuple[str | None, str | None]:

    if ewallet:
        return ewallet.terminal_api_key, ewallet.incust_terminal_id

    if invoice_template:
        if invoice_template.incust_terminal_api_key:
            return invoice_template.incust_terminal_api_key, invoice_template.incust_terminal_id

    if not store_id:
        if menu_in_store and menu_in_store.store_id:
            store_id = menu_in_store.store_id
        if not store_id:
            return None, None

    store = await Store.get(store_id)
    if store and store.incust_terminal_api_key:
        return store.incust_terminal_api_key, store.incust_terminal_id

    return None, None


