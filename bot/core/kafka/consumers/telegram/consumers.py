from aiokafka import ConsumerRecord

from core.kafka.base.consumer import BaseDBConsumer
from core.kafka.constants import (
    TELEGRAM_LIMITED_TOPIC, TELEGRAM_PREFIX,
    TELEGRAM_REGULAR_TOPIC,
)
from core.kafka.consumers.telegram.sender import TelegramKafkaNotificationSender
from core.kafka.functions import get_limited_message
from schemas import TelegramMessageValue


class TelegramConsumer(BaseDBConsumer):
    TOPIC_BASE = TELEGRAM_REGULAR_TOPIC
    VALUE_MODEL = TelegramMessageValue

    async def process_message(
            self, message: ConsumerRecord, value: TelegramMessageValue
    ):
        await TelegramKafkaNotificationSender(self.redis, value)
        return True


class TelegramLimitedConsumer(BaseDBConsumer):
    TOPIC_BASE = TELEGRAM_LIMITED_TOPIC

    async def process_message(self, message: ConsumerRecord, value: str):
        while message := await get_limited_message(
                self.redis, TELEGRAM_PREFIX, "tg-bot-chat-id", value
        ):
            try:
                message_validated = TelegramMessageValue.parse_raw(message)
            except Exception as exception:
                await self.send_error(
                    "LIMITED Message value validation error",
                    message,
                    exception,
                )
                continue

            result = await TelegramKafkaNotificationSender(
                self.redis,
                message_validated,
                False,
            )
            if not result:
                break
        return True
