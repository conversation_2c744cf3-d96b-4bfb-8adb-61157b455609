import asyncio

from core.kafka.base.consumer import BaseConsumer
from core.kafka.constants import TASK_LIMITED_TOPIC, TASK_PREFIX, TASK_REGULAR_TOPIC
from core.kafka.functions import get_limited_message
from db import DBSession
from schemas import TaskValue
from .functions import do_task_action


class TaskConsumer(BaseConsumer):
    TOPIC_BASE = TASK_REGULAR_TOPIC
    VALUE_MODEL = TaskValue
    MULTIPLE_PARALLEL_COUNT = 10

    async def process_message(self, _, value: TaskValue):
        await do_task_action(self.redis, TASK_PREFIX, value)
        return True


class TaskLimitedConsumer(BaseConsumer):
    TOPIC_BASE = TASK_LIMITED_TOPIC
    MULTIPLE_PARALLEL_COUNT = 2

    async def process_message(self, _, value: str):
        messages: list[TaskValue] = []

        while message := await get_limited_message(
                self.redis, TASK_PREFIX, "task-type-type_task-object_id", value
        ):
            try:
                message_validated = TaskValue.parse_raw(message)
            except Exception as exception:
                with DBSession():
                    await self.send_error(
                        "LIMITED Message value validation error",
                        message,
                        exception,
                    )
                continue

            messages.append(message_validated)
            if len(messages) < message_validated.retry_chunk_size:
                continue

            results = await asyncio.gather(
                *[
                    do_task_action(
                        self.redis, TASK_PREFIX, el, False
                    )
                    for el in messages
                ], return_exceptions=False
            )
            if not all(results):
                break

        return True
