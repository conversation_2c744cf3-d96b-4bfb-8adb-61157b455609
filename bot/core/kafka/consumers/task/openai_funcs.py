import asyncio
import re
import time

import openai

from exceptions.task import TaskUrlError
from loggers import <PERSON><PERSON><PERSON>og<PERSON>
from schemas import TaskAiModelTypeEnum

PROMPT = """
Title: {obj_name}
Description: {obj_description}
"""

MAX_CONCURRENT_REQUESTS = 5
semaphore_dalle2 = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)
semaphore_dalle3 = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)


async def get_best_model(client: openai.AsyncClient):
    model = "gpt-4o-mini"
    response = await client.models.list()
    models = response.data
    # Сортуємо моделі за пріоритетом
    preferred_models = ["gpt-4o-mini", "gpt-3.5-turbo", "gpt-4o"]
    for model in preferred_models:
        if any(m.id == model for m in models):
            return model
    return model



async def generate_dalle_prompt(
        client: openai.AsyncClient,
        obj_name: str,
        obj_description: str,
        prompt: str,
) -> str:
    debugger = J<PERSON>NLogger(
        "kafka.consumer",
        "generate_dalle_prompt",
        {"obj_name": obj_name, "obj_description": obj_description, "prompt": prompt}
    )
    start = time.time()

    try:
        model = await get_best_model(client)
        response = await client.chat.completions.create(
            messages=[
                {
                    "role": "system",
                    "content": prompt,
                },
                {
                    "role": "user",
                    "content": PROMPT.format(
                        obj_name=obj_name, obj_description=obj_description
                    ),
                }
            ],
            model=model,
            temperature=0.3,
            max_tokens=150,
            top_p=1.0,
            frequency_penalty=0.0,
            presence_penalty=0.0,
        )
        return response.choices[0].message.content.strip()
    finally:
        end = time.time()
        debugger.debug(
            f"{obj_name=}: Executing generate_dalle_prompt took {end - start} seconds"
        )


def parse_ratelimit_reset(value: str):
    match = re.match(r"(\d+)([smh]?)", value)
    if not match:
        raise ValueError("Invalid x-ratelimit-reset format")

    time_value = int(match.group(1))
    unit = match.group(2)

    if unit == 'm':
        return time_value * 60
    elif unit == 'h':
        return time_value * 3600
    else:
        return time_value  # default is seconds


async def generate_image(
        client: openai.AsyncClient,
        model: str,
        prompt: str,
        obj_name: str,
        obj_type: str,
        retry: int = 0,
):
    debugger = JSONLogger(
        "kafka.consumer",
        "generate_image",
        {"obj_name": obj_name, "model": model, "prompt": prompt, "retry": retry}
    )

    params = {
        "model": model,
        "prompt": prompt,
        "n": 1,
        "size": "1792x1024" if obj_type == "store_banner"  else "1024x1024"
    }

    if model == "dall-e-3":
        params["quality"] = "standard"
        params["style"] = "natural"
        semaphore = semaphore_dalle3
    else:
        semaphore = semaphore_dalle2

    try:
        async with semaphore:
            start = time.time()
            try:
                debugger.debug(f"{model}: Creating image for {obj_name} started")
                response = await client.images.generate(**params)
                debugger.debug(f"{model}: Image for {obj_name} processed")
                return response.data[0].url
            finally:
                end = time.time()
                debugger.debug(
                    f"{obj_name=}: {model} Executing generate_image took "
                    f"{end - start} seconds"
                )
    except openai.RateLimitError as e:
        try:
            timout_str = e.response.headers.get(
                "x-ratelimit-reset-images", e.response.headers.get("retry-after")
            )
            timeout = parse_ratelimit_reset(timout_str)
        except:
            debugger.error(f"Unable to parse timeout: {e.response.headers}")
            timeout = (10 * retry) or 1
        debugger.debug(
            f"{model}: Rate limit hit for {obj_name}. Waiting {timeout} seconds "
            f"before retry."
        )
        await asyncio.sleep(timeout)
        return await generate_image(client, model, prompt, obj_name, obj_type, retry + 1)
    except Exception as e:
        debugger.error(f"{model}: Error generating image for {obj_name}: {e}")
        raise e


async def process_ai_image(item: dict):
    debugger = JSONLogger(
        "kafka.consumer",
        "process_ai_image",
        item,
    )
    start_time = time.time()
    status = "failed"

    result = {
        "task_id": item["id"],
        "status": status,
        "ai_model": "dall-e-2" if item.get(
            "ai_model"
        ) == TaskAiModelTypeEnum.DALLE2.value else "dall-e-3",
    }
    try:
        obj_name, obj_description, openai_key = item["name"], item["description"], item[
            "openai_key"]
        client = openai.AsyncClient(api_key=openai_key)
        prompt = await generate_dalle_prompt(
            client,
            obj_name,
            obj_description,
            item["prompt"],
        )

        result.update({"dalle_prompt": prompt})

        debugger.debug(f"Generating images for {obj_name}: {prompt}")

        results = await asyncio.gather(
            generate_image(
                client,
                result["ai_model"],
                prompt,
                obj_name,
                item.get("type_task"),
            ),
            return_exceptions=True
        )
        debugger.debug(f"{results=}")
        if isinstance(results[0], Exception):
            raise results[0]
        url = results[0]
        if not url.startswith("http"):
            raise TaskUrlError(url)
        debugger.debug(f"{obj_name=}: {url=}")
        status = "success"
    except Exception as e:
        debugger.error(f"Error processing {item['name']}: {str(e)}")
        raise e

    end_time = time.time()
    generation_time = end_time - start_time
    result.update(
        {
            "url": url, "status": status,
            "generation_time": f"{generation_time:.2f} seconds",
        }
    )
    debugger.debug(f"Processed: {item['name']} in {generation_time:.2f} seconds")
    return result
