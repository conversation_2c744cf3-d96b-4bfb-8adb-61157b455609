from abc import ABC, abstractmethod
from typing import Dict, Any, List, Type, Tuple

from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from db.models import User, Group, MailingArgs, MailingUser, ClientBot
from utils.message import send_tg_message

from utils.text import f, c
from utils.router import Router
from utils.redefined_classes import InlineKb, InlineBtn
from utils.mixins.kwargs_safe_mixin import KwargsSafeMixin
from psutils.forms.helpers import save_messages_to_state, with_delete_state_messages

from .base_groups_list_drawer import BaseGroupsListDrawer

from .base_create_mailing_form import create_form
from .base_state import BaseCreateMailingState

from ..keyboards import get_confirm_mailing_keyboard, get_menu_keyboard


class BaseMailing(KwargsSafeMixin, ABC, methods=None):

    state_group: Type[BaseCreateMailingState] = None

    DEFAULT_KWARGS_SAFE_METHODS = [
        "get_users",
    ]

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)

        if not hasattr(cls, "state_group"):
            raise TypeError("state_group must be redefined")

    @classmethod
    async def run_mailing(cls, message: types.Message, state: FSMContext, user: User, lang: str):
        # user is mailing creator

        state_data = await state.get_data()

        mailing_text = state_data.get("text", "")
        content_type = state_data.get("content_type", "text")
        file_path = state_data.get("file_path")

        sender_groups = await cls.get_sender_groups(user, state_data)

        user_and_data = await cls.get_users(user, state)

        users_and_bots_ids_and_mailing_mode = list(map(lambda x: (x[0], x[1], x[4]), user_and_data))

        bots_ids = await cls.get_bots_ids(user, state_data, users_and_bots_ids_and_mailing_mode)
        created_from_bot = await cls.get_created_bot(bots_ids)
        sender_group_id = sender_groups.id if isinstance(sender_groups, Group) else created_from_bot.group_id

        keyboard = InlineKb()

        if isinstance(sender_groups, Group):
            button_text = await f("turn off mailing from profile button", lang, group_name=sender_groups.name)
            keyboard.row(InlineBtn(button_text, callback_data=c("turn_off_mailing", group_id=sender_group_id)))

            callback_data = c("connect", group_id=sender_group_id)
            keyboard.row(InlineBtn(await f("mailing answer button", lang), callback_data=callback_data))

        groups_names = set(map(lambda x: x[3], user_and_data))
        groups_names = ", ".join(groups_names)

        bots_usernames = set(map(lambda x: x[2], user_and_data))
        bots_usernames = ", ".join(bots_usernames)

        turned_off = len(list(filter(lambda x: x[4] == "blocked", user_and_data)))

        created_from_bot_token = await ClientBot.get_current_bot_token()
        mailing_args = await MailingArgs.create(
            user, created_from_bot_token,
            sender_group_id, mailing_text,
            content_type, file_path, keyboard,
            groups_names, bots_usernames,
            turned_off=turned_off,
        )

        mailing_users = await MailingUser.create(mailing_args.id, users_and_bots_ids_and_mailing_mode)

        await state.finish()

        text = await f(
            "mailing started", lang, users_count=len(mailing_users), groups=groups_names, bots=bots_usernames
        )
        keyboard = await get_menu_keyboard(user, mailing_args.created_from_bot_token, lang)

        await message.answer(text, reply_markup=keyboard)
        await message.delete()

    @classmethod
    @abstractmethod
    async def get_users(cls, mailing_creator: User, state: FSMContext) -> List[Tuple[int, int, str, str, str]]:
        """
        Method to get users for send mailing
        Returns tuple of (user_id, bot_id, bot_username, group_username, mailing_mode(blocked, with_sound))
        """
        raise NotImplementedError

    @classmethod
    @abstractmethod
    async def make_get_groups_kwargs(
            cls,
            user: User,
            state_data: dict,
    ):
        """
        Method to get kwargs for get_groups method
        """
        raise NotImplementedError

    @classmethod
    @abstractmethod
    async def get_groups(
            cls,
            get_objects_kwargs: Dict[str, Any],
            position: int = 0,
            limit: int = None, *,
            operation: str = "all",
            only_names: bool = False,
    ) -> List[Group] | int:
        """
        Method to get possible sender-groups
        """
        raise NotImplementedError

    @classmethod
    async def get_sender_groups(cls, user: User, state_data: dict) -> Group | list[Group]:
        sender_group_id = state_data.get("sender_group_id")
        sender_group = await Group.get(sender_group_id)
        return sender_group

    @classmethod
    async def get_bots_ids(
            cls,
            user: User, state_data: dict,
            users_and_bots_ids_and_mailing_mode = None
    ) -> list[int]:
        if not users_and_bots_ids_and_mailing_mode:
            return []
        return set(map(lambda x: x[1], users_and_bots_ids_and_mailing_mode))

    @classmethod
    async def get_created_bot(cls, bots_ids: list[int]) -> int | None:
        bot_id = list(bots_ids)[0] if len(bots_ids) == 1 else None
        if bot_id:
            bot = await ClientBot.get(bot_id=bot_id)
        else:
            bot = await ClientBot.get_current()
        return bot

    @classmethod
    async def send_create_message_menu(
            cls,
            message: types.Message,
            state: FSMContext,
            user: User, lang: str, mode: str,
    ):
        state_data = await state.get_data()

        text = state_data.get("text")
        content_type = state_data.get("content_type")
        file_path = state_data.get("file_path")

        if not content_type:
            message_text = await f("enter mailing data header", lang)

            if mode == "edit":
                return await message.edit_text(message_text)
            return await message.answer(message_text)

        content = {}
        if content_type != "text":
            content[content_type] = file_path

        msg = await send_tg_message(user.chat_id, content_type, all_messages_in_result=True, text=text, **content)

        await save_messages_to_state(state, msg)

        message_text = (await f("mailing confirmation message", lang)).strip()
        keyboard = await get_confirm_mailing_keyboard(lang)
        new_message = await message.answer(message_text, reply_markup=keyboard)
        await message.delete()
        return new_message

    @classmethod
    @abstractmethod
    async def back_to_previous_state(
            cls,
            message: types.Message,
            state: FSMContext,
            user: User,
            lang: str,
    ):
        pass

    @classmethod
    @with_delete_state_messages
    async def previous_button_handler(
            cls,
            callback_query: types.CallbackQuery,
            state: FSMContext,
            user: User,
            lang: str,
    ):
        cur_state = await state.get_state("%:%")
        state_data = await state.get_data()

        state_name = cur_state.split(":")[-1]

        if state_name == "ChooseGroup" or state_data.get("is_groups_skipped", False):
            await cls.back_to_previous_state(callback_query.message, state, user, lang)
        else:
            await cls.state_group.previous()
            await Router.state_menu(callback_query, state, lang)

    @classmethod
    def setup_mailing(cls, dp: Dispatcher):

        router: Router = dp["router"]

        form = create_form(cls.state_group)

        form.setup_handlers(dp)

        make_get_objects_kwargs = cls.make_get_groups_kwargs
        get_groups = cls.get_groups

        class GroupsListDrawer(BaseGroupsListDrawer):
            state_group = cls.state_group

            @classmethod
            async def get_data_from_state(cls, user: User, state: FSMContext, mode: str = "new") -> Dict[str, Any]:
                return await state.get_data()

            @classmethod
            async def make_get_objects_kwargs(
                    cls,
                    user: User,
                    search_text: str,
                    data_from_state: Dict[str, Any],
            ) -> Dict[str, Any]:
                return await make_get_objects_kwargs(user, data_from_state)

            @classmethod
            async def get_objects(
                    cls,
                    get_objects_kwargs: Dict[str, Any],
                    position: int = 0,
                    limit: int = None, *,
                    operation: str = "all",
            ) -> List[Any] | int:
                return await get_groups(get_objects_kwargs, position, limit, operation=operation)

        dp.register_callback_query_handler(
            cls.previous_button_handler,
            previous_button=True,
            state=cls.state_group,
        )

        router.add_route(cls.state_group.ChooseGroup, GroupsListDrawer())
        router.add_route(cls.state_group.CreateMessage, cls.send_create_message_menu)
        router.add_route(cls.state_group.RunMailing, cls.run_mailing)
