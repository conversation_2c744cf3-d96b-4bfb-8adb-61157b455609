import asyncio

from psutils.exceptions import ErrorWithTextVariable

from core import messangers_adapters as ma
from core.external_coupon import send_coupon_info, send_coupon_photo
from core.external_coupon.deep_links import ExternalCouponDeepLink
from core.loyalty.incust_client_adapter import (
    run_with_incust_client_api,
    run_with_incust_terminal_api,
    get_or_create_incust_customer,
)
from core.messangers_adapters import FSMContext, types
from core.notifications.funcs import put_message_to_kafka
from db import crud
from db.models import Brand, ClientBot, LoyaltySettings, User
from loggers import JSONLogger
from utils.message import send_tg_message, send_wa_message
from utils.qrcode import send_qr_code
from utils.text import f, html_to_markdown


class ExternalCouponProcessor:

    def __init__(
        self,
        message: types.AnswerObject,
        state: FSMContext,
        bot: ClientBot,
        user: User, lang: str,
        keyboard: types.Keyboard | None = None,
        external_coupon_code: str | None = None,
        store_id: int | None = None,
    ):
        self.message: types.AnswerObject = message
        self.state: FSMContext = state
        self.bot: ClientBot = bot
        self.user: User = user
        self.lang: str = lang
        self.main_keyboard = keyboard
        self.external_coupon_code: str = external_coupon_code
        self.store_id: int = store_id
        self.debugger = JSONLogger(
            "ext_coupon", {
                "group_id": self.bot.group_id,
                "inviting_user_first_name": self.user.first_name,
                "inviting_user_last_name": self.user.last_name,
                "inviting_user_id": self.user.id,
                "bot_id": self.bot.id,
                "bot_name": self.bot.display_name,
                "main_keyboard_type": str(type(self.main_keyboard)),
            }
        )

    async def process(self):
        self.debugger.debug("process ->")
        try:
            await self.state.finish()
            brand = await crud.get_brand_by_group(self.bot.group_id)
            self.debugger.add_data({"brand_id": brand.id, "brand_name": brand.name})

            # Отримуємо налаштування лояльності
            loyalty_settings = await LoyaltySettings.get_loyalty_settings_for_context(
                brand_id=brand.id,
                store_id=self.store_id
            )
            
            if not loyalty_settings:
                self.debugger.debug(
                    "sending coupon message with keyboard "
                    "process_error_loyalty_not_connected"
                )
                return await self.message.answer(
                    await f(
                        "loyalty not connected text", self.lang, brand_name=brand.name
                    ),
                    reply_markup=(
                        self.main_keyboard
                        if self.bot.bot_type == "whatsapp" else None
                    ),
                )

            # Синхронізуємо користувача з InCust (створюємо якщо потрібно)
            await self._sync_incust_customer(loyalty_settings, brand)
            
            # Додаємо купон до гаманця
            if added_coupons := await self.add_coupon_to_wallet(loyalty_settings):
                await self.process_coupon(brand.id, loyalty_settings, added_coupons)

            asyncio.ensure_future(self.send_delayed_main_menu_message())

        except Exception as e:
            await self.process_exception(e)

    async def _sync_incust_customer(self, loyalty_settings: LoyaltySettings, brand: Brand):
        """Синхронізація користувача з InCust через новий клієнт"""
        try:
            await get_or_create_incust_customer(
                user=self.user,
                settings=loyalty_settings,
                lang=self.lang
            )
            self.debugger.debug("Користувач синхронізований з InCust")
        except Exception as e:
            self.debugger.error(f"Помилка синхронізації користувача з InCust: {e}", exc_info=True)
            raise

    async def process_coupon(
        self, brand_id: int,
        loyalty_settings: LoyaltySettings,
        added_coupons: list[str] = None,
    ):
        """Обробка купона через новий сервіс лояльності"""
        self.debugger.debug(
            f"process_coupon -> {self.external_coupon_code=}, {added_coupons=}"
        )

        try:
            from incust_client_api_client.api.coupon_api import CouponApi
            
            if added_coupons:
                # Якщо купон додано до гаманця - отримуємо його за ID
                coupon_id = added_coupons[0]
                
                # Отримуємо детальні дані купона через новий клієнт
                coupon = await run_with_incust_client_api(
                    loyalty_settings,
                    self.user,
                    CouponApi.incust_controllers_client_client_coupon_get,
                    coupon_id=coupon_id,
                    lang=self.lang
                )
            else:
                # Якщо купон не додано - отримуємо його за кодом (може вже був у гаманці)
                # Спробуємо отримати купон за кодом через новий клієнт
                coupon = await run_with_incust_client_api(
                    loyalty_settings,
                    self.user,
                    CouponApi.incust_controllers_client_client_coupon_info,
                    code=self.external_coupon_code,
                    lang=self.lang
                )

            if coupon:
                await send_coupon_info(
                    coupon,
                    self.user,
                    self.bot,
                    self.lang,
                    brand_id,
                )
                
                self.debugger.debug(
                    'process_coupon: Messages about coupon received successfully'
                )
            else:
                self.debugger.warning(f"Не вдалося отримати дані купона {self.external_coupon_code}")
                
        except Exception as e:
            self.debugger.error(f"Помилка обробки купона: {e}", exc_info=True)
            raise

    async def add_coupon_to_wallet(self, loyalty_settings: LoyaltySettings) -> list[str] | None:
        """Додавання купона до гаманця через новий terminal API"""
        self.debugger.debug("add_coupon_to_wallet ->")
        try:
            from incust_terminal_api_client.api.coupons_api import CouponsApi
            from incust_terminal_api_client.models.coupon_code import CouponCode
            
            # Додаємо купон користувачу через terminal API
            coupon_codes = [CouponCode(code=self.external_coupon_code)]
            result = await run_with_incust_terminal_api(
                loyalty_settings,
                CouponsApi.incust_controllers_term_api_customer_coupon_add,
                coupon_code=coupon_codes,
                user_id_value=self.user.incust_external_id,
                user_id_type="external-id"
            )
            
            self.debugger.debug(f"add_coupon_to_wallet result: {result}")
            
            if result and hasattr(result, 'coupons') and result.coupons:
                text = await f(
                    "add coupon to wallet success text",
                    self.lang,
                )
                
                # Повідомляємо про успішне додавання
                for _ in result.coupons:
                    await self.message.answer(text)
                    self.debugger.debug("add coupon to wallet message Ok")
                
                # Повертаємо список ID купонів
                return [str(coupon.id) if coupon.id else coupon.code for coupon in result.coupons]
            
            # Якщо купони не додані
            self.debugger.debug("add coupon to wallet error - no coupons returned")
            text = await f("add coupon to wallet failed text", self.lang)
            await self.message.answer(text)
            return None
                
        except Exception as e:
            self.debugger.error(f"Помилка додавання купона до гаманця: {e}", exc_info=True)
            text = await f("add coupon to wallet failed text", self.lang)
            await self.message.answer(text)
            return None

    async def share_coupon(self, coupon_id: str, loyalty_settings: LoyaltySettings, brand: Brand):
        """Поділитися купоном через новий client API"""
        self.debugger.debug(f"share_coupon -> {coupon_id=}")
        try:
            from incust_client_api_client.api.coupon_api import CouponApi
            
            # Поділитися купоном через client API
            shared_coupon = await run_with_incust_client_api(
                loyalty_settings,
                self.user,
                CouponApi.incust_controllers_client_client_coupon_client_share,
                coupon_id=coupon_id,
                lang=self.lang
            )

            if not shared_coupon or not shared_coupon.message:
                self.debugger.debug("not available coupon for share")
                raise Exception(await f('not available coupon for share', self.lang))

            # Отримуємо дані купона за кодом через новий клієнт
            coupon = await run_with_incust_client_api(
                loyalty_settings,
                self.user,
                CouponApi.incust_controllers_client_client_coupon_info,
                code=shared_coupon.message,
                lang=self.lang
            )
            
            if not coupon:
                raise Exception("Failed to get coupon data after sharing")

            # Формуємо URL для поділення
            if self.bot.bot_type == "telegram":
                url = (f"https://t.me/{self.bot.username}?start=coupon-"
                       f"{shared_coupon.message}")
                if self.store_id:
                    url += f"store_id-{self.store_id}"
            else:
                url = ExternalCouponDeepLink(
                    external_coupon_code=shared_coupon.message,
                    store_id=self.store_id,
                ).to_str(self.bot.bot_type, self.bot.id_name)

            await send_qr_code(
                self.message, self.state,
                url,
                show_url=False,
                url_apart=False,
                text=await f("please scan QR code", self.lang),
                bot=self.bot,
            )
            self.debugger.debug("send_qr_code Ok")

            await put_message_to_kafka(
                self.bot, self.user,
                await f("incust loyalty accept invitation qr message footer", self.lang),
                "text", None,
            )

            await send_coupon_photo(
                coupon,
                self.user,
                self.bot,
                url=url,
            )

            await put_message_to_kafka(
                self.bot, self.user,
                await f("can send link or publish footer", self.lang),
                "text", None,
            )

            self.debugger.debug("send_coupon_photo Ok")
            asyncio.ensure_future(self.send_delayed_main_menu_message())
            self.debugger.debug("share_coupon END")
        except Exception as e:
            return await self.process_exception(e)

    async def apply_coupon(self, coupon_code_or_id: str, loyalty_settings: LoyaltySettings,):
        """Застосування купона через нові клієнти"""
        self.debugger.debug(f"apply_coupon -> {coupon_code_or_id=}")
        try:
            from incust_client_api_client.api.coupon_api import CouponApi
            from incust_terminal_api_client.api.coupons_api import CouponsApi
            from incust_terminal_api_client.models.coupon_code import CouponCode
            
            is_in_wallet = False
            texts = []
            
            # Визначаємо чи це код чи ID купона
            if len(coupon_code_or_id.replace('-', '')) == 12:
                # Це код купона
                coupon = await run_with_incust_client_api(
                    loyalty_settings,
                    self.user,
                    CouponApi.incust_controllers_client_client_coupon_info,
                    code=coupon_code_or_id,
                    lang=self.lang
                )
                coupon_code = coupon_code_or_id
            else:
                # Це ID купона
                coupon = await run_with_incust_client_api(
                    loyalty_settings,
                    self.user,
                    CouponApi.incust_controllers_client_client_coupon_get,
                    coupon_id=coupon_code_or_id,
                    lang=self.lang
                )
                coupon_code = coupon.code if coupon else coupon_code_or_id
                
                # Якщо вдалося отримати купон за ID, значить він у гаманці користувача
                if coupon:
                    is_in_wallet = True

            if not coupon:
                raise Exception("Coupon not found")

            # Якщо купон не в гаманці - додаємо його
            if not is_in_wallet:
                coupon_codes = [CouponCode(code=coupon_code)]
                result = await run_with_incust_terminal_api(
                    loyalty_settings,
                    CouponsApi.incust_controllers_term_api_customer_coupon_add,
                    coupon_code=coupon_codes,
                    user_id_value=self.user.incust_external_id,
                    user_id_type="external-id"
                )

                if result and result.coupons and len(result.coupons) > 0:
                    added_coupon = result.coupons[0]
                    texts.append(await f('coupon was added', self.user.lang))
                    # Отримуємо актуальні дані купона за ID
                    if added_coupon.id:
                        coupon = await run_with_incust_client_api(
                            loyalty_settings,
                            self.user,
                            CouponApi.incust_controllers_client_client_coupon_get,
                            coupon_id=added_coupon.id,
                            lang=self.lang
                        )
                    else:
                        # Якщо немає ID, використовуємо отриманий купон
                        coupon = added_coupon
                else:
                    # Якщо не вдалося додати, використовуємо оригінальний купон
                    pass
            else:
                # Купон вже в гаманці, використовуємо існуючий об'єкт coupon
                pass

            if coupon.status and coupon.status == 'redeemed':
                texts.append(await f('coupon already used text', self.user.lang))

            # Застосовуємо купон якщо він не використаний
            if (coupon.status and coupon.status != 'redeemed' and
                coupon.type and coupon.type != 'check-modifier' and
                not (hasattr(coupon, 'redeem_at_terminal') and coupon.redeem_at_terminal)):
                
                if coupon and coupon.id:
                    result = await run_with_incust_client_api(
                        loyalty_settings,
                        self.user,
                        CouponApi.incust_controllers_client_client_coupon_client_redeem,
                        coupon_id=coupon.id,
                        lang=self.lang
                    )
                    self.debugger.debug(f"redeemed coupon result: {result}")
                else:
                    self.debugger.error("Cannot redeem coupon - no valid ID available")

            # Якщо це check-modifier купон
            if coupon.type and coupon.type == 'check-modifier':
                texts.append(await f('coupon can use for order', self.user.lang))

            if texts:
                await self.send_coupon_result_message('\n'.join(texts))
                    
        except Exception as e:
            await self.process_exception(e)

    async def send_coupon_result_message(self, text):
        self.debugger.debug("send_coupon_result_message ->")
        to = ma.get_user_to(self.user, self.bot.bot_type)
        if not to:
            self.debugger.debug(
                f"send_coupon_result_message: User is not in bot {self.bot.bot_type=}"
            )
            return None

        kwargs = {
            "keyboard": self.main_keyboard if self.bot.bot_type == "whatsapp" else None
        }

        match self.bot.bot_type:
            case "telegram":
                func = send_tg_message
                kwargs["text"] = text

            case "whatsapp":
                func = send_wa_message
                kwargs["wa_from"] = self.bot.whatsapp_from
                kwargs["text"] = html_to_markdown(text)
            case _:
                err_msg = f"Unsupported {self.bot.bot_type=}"
                self.debugger.debug(f"send_coupon_result_message: {err_msg}")
                raise ValueError(err_msg)

        return await func(
            to,
            "text",
            bot_token=self.bot.token,
            **kwargs,
        )

    async def process_exception(self, e: Exception):
        self.debugger.error(f"process_exception -> {e=}", exc_info=True)

        if isinstance(e, ErrorWithTextVariable):
            text_variable = e.text_variable
            text_kwargs = e.text_kwargs
        elif hasattr(e, "message") and getattr(e, "message"):
            text_variable = "external loyalty error"
            text_kwargs = {"message": e.message}
        else:
            text_variable = "external coupon unknown error"
            text_kwargs = {}

        return await self.send_coupon_result_message(
            await f(text_variable, self.lang, **text_kwargs)
        )

    async def send_delayed_main_menu_message(self):
        if self.bot.bot_type != "whatsapp":
            return
        self.debugger.debug("send_delayed_main_menu_message ->")
        await asyncio.sleep(4)  # Wait send coupon
        await self.message.answer(
            await f("main menu button", self.lang),
            reply_markup=self.main_keyboard if self.bot.bot_type == "whatsapp" else None
        )
        self.debugger.debug("main wa menu sent Ok")

