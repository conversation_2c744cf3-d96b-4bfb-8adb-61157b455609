from core.crm_ticket.functions import set_crm_ticket_status, vm_create_crm_ticket
from db import crud
from db.models import ClientBot, User
from schemas import (
    CRMTicketStatusEnum, CRMTicketStatusInitiatedByEnum, VMTicketCreateActionParams,
    VMTicketSetStatusActionParams,
)
from .base import VMInlineButtonAction


class TicketCreateButtonAction(VMInlineButtonAction[VMTicketCreateActionParams]):
    subtype = "ticket_create"
    params_model = VMTicketCreateActionParams

    full_width_params = (
        "ticket_title",
    )

    async def execute(self, _, bot: ClientBot, user: User):
        await vm_create_crm_ticket(
            user, bot, self.context.vmc,
            self.params.ticket_title,
            self.params.create_ticket_if_not_exists,
        )


class TicketSetStatusButtonAction(VMInlineButtonAction[VMTicketSetStatusActionParams]):
    subtype = "ticket_set_status"
    params_model = VMTicketSetStatusActionParams

    full_width_params = (
        "ticket_title",
        "ticket_status",
    )

    async def execute(self, _, bot: ClientBot, user: User):
        ticket = await crud.get_crm_ticket_for_user(
            user.id, bot,
            self.params.ticket_title,
            self.context.vmc.id,
        )
        if not ticket:
            return
        await set_crm_ticket_status(
            ticket,
            CRMTicketStatusEnum(self.params.ticket_status.value),
            CRMTicketStatusInitiatedByEnum.VM,
            notify_user=False,
        )
