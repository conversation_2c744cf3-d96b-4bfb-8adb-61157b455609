from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from client.main.functions import send_hello_message
from client.main.handlers.private.menu_handlers import benefits_button_handler
from db import crud
from db.crud import delete_currently_running_vm_chats
from db.models import (
    ChatMember, ClientBot, FriendlyBotAnalyticAction, Group, User,
    UserAnalyticAction,
)
from schemas import ChatTypeEnum
from utils.text import f
from ..chat import Chat<PERSON><PERSON><PERSON>ink
from ..init_chat import user_to_chat
from ..virtual_manager.deep_link import VMDeepLink
from ..virtual_manager.functions import start_virtual_manager_chat


async def cmd_incust_deep_link(
        message: types.Message, state: FSMContext, user: User, bot: ClientBot, lang: str
):
    await benefits_button_handler(message, state, user, bot, lang)


async def cmd_chat_deep_link(
        message: types.Message,
        chat_data: ChatDeepLink,
        user: User, lang: str
):
    bot = await ClientBot.get_current()

    group_id = chat_data.group_id or bot.group_id
    group = await Group.get(group_id)
    if not group:
        return await message.answer(
            await f(
                "group not found error",
                lang,
                profile_id=group_id
            ),
        )

    await delete_currently_running_vm_chats(user.id, bot.id)
    await user_to_chat(
        ChatTypeEnum.USER_WITH_GROUP,
        message, group_id=group.id,
        need_get_vm_from_history=False,
    )

    await UserAnalyticAction.save_link_following(
        message.from_user.id, bot, "chat"
    )

    if bot.is_friendly and group.channel:
        chat_member = await ChatMember.create_or_get(user, group.channel)
        await FriendlyBotAnalyticAction.save_contacted_from_chat(
            member_id=chat_member.id
        )


async def cmd_start_vm_deep_link(
        _,
        state: FSMContext,
        bot: ClientBot,
        vm_data: VMDeepLink,
        user: User,
        lang: str
):
    await state.finish()

    vm = await crud.get_vm_by_id_or_name_id(vm_data.vm_id)
    if vm.bot_hello_message_enabled:
        await send_hello_message(user, bot, lang, message_key="welcome text")
    await start_virtual_manager_chat(
        user, vm,
        vm_data.group_id,
        bot,
    )


def register_chat_commands_handlers(dp: Dispatcher):

    dp.register_message_handler(
        cmd_incust_deep_link,
        deep_link="benefit",
        chat_type="private",
        state="*",
    )

    dp.register_message_handler(
        cmd_chat_deep_link,
        ChatDeepLink.get_filter(return_field_name="chat_data"),
        chat_type="private",
        state="*",
    )

    dp.register_message_handler(
        cmd_start_vm_deep_link,
        VMDeepLink.get_filter(return_field_name="vm_data"),
        chat_type="private",
        state="*",
    )
