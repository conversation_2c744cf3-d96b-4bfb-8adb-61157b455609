from dataclasses import asdict

import schemas
from core.bot.ewallet_handlers import process_single_ewallet
from core.store.functions.payments import get_icon_for_payment
from db.models import EWallet, MediaObject, User


async def ewallet_to_schema(
        ewallet: EWallet,
        user: User | None = None,
        lang: str | None = None,
        media: MediaObject | None = None,
        add_account_info: bool = True
):
    if not media and ewallet.media_id:
        media = await MediaObject.get(ewallet.media_id)

    if user and add_account_info:
        res = await process_single_ewallet(ewallet, user, lang or user.lang)
        account_info = schemas.EwalletAccountInfoSchema(
            **asdict(res)
        )
    else:
        account_info = None

    return schemas.EwalletSchema(
        **ewallet.as_dict(),
        account_info=account_info,
        icon_url=media.url if media else get_icon_for_payment("ewallet"),
    )
