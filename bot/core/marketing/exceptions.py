import schemas


class MailingError(Exception):
    def __init__(self, message: str, data: dict | None = None):
        self.message = message
        self.data = data
        super().__init__(self.message, self.data)


class MailingUnknownError(MailingError):
    def __init__(self, data: dict | None = None):
        super().__init__("mailing unknown error", data)


class MailingUnknownChannelError(MailingError):
    def __init__(self, data: dict | None = None):
        super().__init__("mailing unknown channel error", data)


class MailingMessageNotFoundError(MailingError):
    def __init__(self, data: dict | None = None):
        super().__init__("mailing message not found error", data)


class MailingTemplateNotFoundError(MailingError):
    def __init__(self, data: dict | None = None):
        super().__init__("mailing template not found error", data)


class MailingBotNotFoundOrWrongBotTypeError(MailingError):
    def __init__(self, data: dict | None = None):
        super().__init__("mailing bot not found or wrong bot type error", data)
