from psutils.convertors import time_to_str

import schemas
from core.helpers import utc_time_to_local
from db import crud
from db.models import (
    BrandCustomSettings, Group, MediaObject, Store, StoreBanner, StoreCustomSettings,
    Translation,
)
from schemas.store.types import CustomType, ShipmentBaseTypeLiteral
from utils.translator import t
from .billing_settings import get_billing_settings_schema


async def get_shipment_status(
        store: Store, shipment_type: ShipmentBaseTypeLiteral
) -> bool | None:
    brand_settings = await BrandCustomSettings.get(
        brand_id=store.brand_id, custom_type=CustomType.SHIPMENT.value,
        base_type=shipment_type
    )
    store_settings = await StoreCustomSettings.get(
        store_id=store.id, custom_settings_id=brand_settings.id
    )

    if not store_settings or store_settings.is_enabled is None:
        return brand_settings.is_enabled
    return store_settings.is_enabled


async def store_to_schema(
        store: Store,
        group: Group,
        lang: str,
        translation: Translation | None = None,
) -> schemas.StoreSchema:
    if group is None:
        group = await Group.get_by_brand(store.brand_id)

    is_translate = group.is_translate
    origin_lang = group.lang

    store_schema = schemas.StoreSchema.from_orm(store)
    store_schema.billing_settings = await get_billing_settings_schema(
        store.id, store.brand_id
    )

    store_schema.is_offer_doc_exist = store.offer_media_id is not None
    store_schema.is_about_doc_exist = store.description_media_id is not None
    store_schema.image_url = await store.image_media_url

    store_schema.banners = await banners_to_schema(
        await StoreBanner.get_list(store_id=store.id), only_visible=True
    )

    categories = await crud.get_store_categories(
        store.brand_id, store.id, with_translations=False
    )
    if not categories:
        store_schema.has_categories = False

    working_days_data = await crud.get_working_times(store.id)

    store_schema.working_days = []

    for day_data in working_days_data:
        day = day_data.day

        schema = schemas.WorkingDaySchema(
            id=day.id,
            day=day.day,
            is_weekend=day.is_weekend,
        )

        for slot in day_data.slots:
            if schema.slots is None:
                schema.slots = []

            schema.slots.append(
                schemas.WorkingSlotSchema(
                    id=slot.id,
                    start_time=slot.start_time,
                    end_time=slot.end_time,
                    start_time_text=time_to_str(
                        utc_time_to_local(slot.start_time, group.timezone)
                    ),
                    end_time_text=time_to_str(
                        utc_time_to_local(slot.end_time, group.timezone)
                    )
                )
            )
        store_schema.working_days.append(schema)

    return await t(
        store, lang, origin_lang,
        translation=translation,
        result_obj=store_schema,
        group_id=group.id,
        is_auto_translate_allowed=is_translate,
    )


async def banners_to_schema(
        banners: list[StoreBanner] | None,
        only_visible: bool = False
) -> list[schemas.BannerSchema] | None:
    banners_schema: list[schemas.BannerSchema] | None = None
    if banners:
        banners_schema = []
        for banner in banners:
            if only_visible and not banner.is_visible:
                continue
            media = await MediaObject.get(banner.media_id)
            schema = schemas.BannerSchema(
                image_path=media.file_path,
                url=banner.url,
                is_visible=banner.is_visible,
                position=banner.position,
            )
            banners_schema.append(schema)
    if not banners_schema:
        return banners_schema

    return sorted(
        banners_schema,
        key=lambda x: x.position if x.position is not None else float('inf'),
        reverse=True
    )
