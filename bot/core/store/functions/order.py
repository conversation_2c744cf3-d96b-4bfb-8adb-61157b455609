import logging

import schemas
from core.invoice import InvoiceService
from db import crud
from db.models import (
    ClientBot, OrderCustomPayment, OrderShipment, StoreOrder, StoreProduct, User,
)
from .order_to_schema import order_to_schema as order_to_schema_func


async def order_to_schema(
        order: StoreOrder,
        lang: str,
        with_token: bool = False,
) -> schemas.OrderSchema:
    return await order_to_schema_func(order, lang, with_token)


async def get_custom_method_schema(
        data: schemas.CustomPaymentMethodData | schemas.CustomShipmentMethodData | None,
) -> schemas.OrderCustomMethodDataSchema | None:
    result, custom_object = None, None
    if not data:
        return result

    if isinstance(data, schemas.CustomPaymentMethodData):
        custom_object = await OrderCustomPayment.get(data.id)
    elif isinstance(data, schemas.CustomShipmentMethodData):
        custom_object = await OrderShipment.get(data.id)

    if custom_object:
        result = schemas.OrderCustomMethodDataSchema(
            name=custom_object.name,
            price=custom_object.price,
            comment=custom_object.comment,
        )
    return result


debugger = logging.getLogger('debugger')


async def make_invoice_for_order(
        store_order: StoreOrder, lang: str, bot: ClientBot | None = None, ):
    debugger.debug("make_invoice_for_order: lang=%s" % lang)
    items = []
    for order_product in store_order.order_products:
        product = await StoreProduct.get(order_product.product_id)

        attributes = ""
        for attr in order_product.attributes:
            attributes += f"+ {attr.name} (x{attr.quantity})\n"
        items.append(
            schemas.CreateInvoiceItemData(
                name=(
                    order_product.name if not attributes
                    else f"{order_product.name} x {order_product.quantity}\n"
                         f"{attributes}"
                ),
                quantity=order_product.quantity,
                item_code=product.product_id,
                price=round(order_product.price_with_attributes / 100, 2),
                unit_discount=round(order_product.discount_amount / 100, 2),
                unit_bonuses_redeemed=round(order_product.bonuses_redeemed / 100, 2),
            )
        )

    user = await User.get_by_id(store_order.user_id)
    group = await crud.get_group_by_store(store_order.store_id)

    service = InvoiceService(group=group, user=user)

    debugger.debug(
        f"invoice = await service.create_inv"
        f"oice {schemas.InvoiceTypeEnum.STORE_ORDER=}, "
        f"{schemas.InvoicePaymentModeEnum.STORE_ORDER.value=}, {items=}"
    )

    invoice = await service.create_invoice(
        schemas.InvoiceTypeEnum.STORE_ORDER,
        schemas.InvoicePaymentModeEnum.STORE_ORDER.value,
        store_order=store_order, items=items,
        bot=bot,
    )
    debugger.debug(f"invoice {invoice.id=} created")

    await crud.set_invoice_to_store_order(store_order.id, invoice)

    return invoice
