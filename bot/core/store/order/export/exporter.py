import asyncio
from datetime import datetime
from typing import Type

from .adapters.base import Base<PERSON><PERSON>pter, StoreOrderAdapterType
from .common import ExporterReturnType, OnStatusUpdatedType, StatusTypeLiteral

from .data_loader import <PERSON><PERSON><PERSON><PERSON>oa<PERSON>, DataLoader
from .exceptions import Export<PERSON>he<PERSON><PERSON>rror, OrderExportError, OrderExportUnknownError

from .types import OrderExportExternalTypeLiteral

DEFAULT_CHECKER_TIMEOUT = 1.0


class OrderExporter:

    def __init__(
            self,
            external_type: OrderExportExternalTypeLiteral,
            base_adapter_cls: Type[StoreOrderAdapterType] = BaseAdapter,
            data_loader_cls: Type[BaseDataLoader] = DataLoader,
    ):
        self.external_type = external_type

        self.base_adapter_cls = base_adapter_cls
        self.data_loader_cls = data_loader_cls

        self.time_started: datetime | None = None
        self.status: StatusTypeLiteral = "not_started"

    async def start(self, on_status_update: OnStatusUpdatedType | None = None, **kwargs) -> ExporterReturnType:
        self.time_started = datetime.utcnow()
        self.status: StatusTypeLiteral = "processing"

        try:
            loader = self.data_loader_cls(**kwargs)
            data = await loader.load()

            adapter_cls = self.base_adapter_cls.get_adapter(self.external_type)
            adapter = adapter_cls(**kwargs)
            result = await adapter.save_data(data)

        except Exception as e:
            self.status: StatusTypeLiteral = "error"
            if on_status_update:
                await on_status_update("error", datetime.utcnow() - self.time_started if self.time_started else 0)
            if not isinstance(e, OrderExportError):
                raise OrderExportUnknownError() from e
            raise

        self.status: StatusTypeLiteral = "done"

        return {
            "result": result,
            "status": self.status,
            "time_passed": datetime.utcnow() - self.time_started
        }

    async def _checker(self, on_status_updated: OnStatusUpdatedType, timeout: float, ignore_errors: bool):
        while True:
            try:
                time_passed = datetime.utcnow() - self.time_started if self.time_started else 0
                await on_status_updated(self.status, time_passed)
            except Exception as e:
                if not ignore_errors:
                    raise ExportCheckerError() from e

            if self.status in ("done", "error"):
                break

            await asyncio.sleep(timeout)

        time_passed = datetime.utcnow() - self.time_started if self.time_started else 0
        await on_status_updated(self.status, time_passed)

    async def start_with_checker(
            self,
            on_status_updated: OnStatusUpdatedType,
            timeout: float = DEFAULT_CHECKER_TIMEOUT,
            ignore_errors: bool = True,
            **kwargs,
    ) -> ExporterReturnType:
        results = await asyncio.gather(
            self.start(**kwargs),
            self._checker(on_status_updated, timeout, ignore_errors),
            return_exceptions=False,
        )

        return results[0]
