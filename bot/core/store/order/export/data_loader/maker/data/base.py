from __future__ import annotations

import abc
import inspect

from psutils.text import paschal_case_to_snake_case


class BaseMaker(abc.ABC):
    _data: dict[str, BaseMaker] = {}

    def __init_subclass__(cls, **kwargs):
        if not inspect.isabstract(cls):
            name = paschal_case_to_snake_case(cls.__name__.replace("Maker", ""))
            if name in cls._data:
                raise TypeError(f"DataMaker with name {cls.__name__} has already been registered")
            cls._data[name] = cls

    @classmethod
    def get_maker_names(cls) -> list[str]:
        return list(cls._data.keys())

    @classmethod
    def get_maker(cls, name: str) -> BaseMaker | None:
        return cls._data.get(name)
