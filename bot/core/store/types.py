from dataclasses import dataclass

from db.models import BrandCustomSettings, StoreCustomSettings


@dataclass
class ShipmentInfo:
    settings: BrandCustomSettings
    store_settings: StoreCustomSettings | None = None

    @property
    def is_enabled(self):
        if self.store_settings and self.store_settings.is_enabled is not None:
            return self.store_settings.is_enabled

        if self.settings.is_enabled is not None:
            return self.settings.is_enabled

        return False

    @property
    def is_base(self):
        return self.settings.is_base_shipment

    @property
    def is_custom(self):
        return not self.is_base
