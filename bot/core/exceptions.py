from abc import ABC

from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class BaseCoreError(ErrorWithHTTPStatus, ABC, base=True):
    def __init__(self, message: str = "Core error", **kwargs):
        self.message = message
        super().__init__(**kwargs)

    def __repr__(self):
        return f"Core error: {self.message}"


class CoreFinanceSystemError(BaseCoreError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "invoice profile must have finance system error"

    def __init__(self, group_id: int):
        super().__init__(
            f"group_id: '{group_id}' not set finance system'",
            group_id=group_id,
        )


class InvalidCurrencyError(BaseCoreError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "invoice invalid currency error"

    def __init__(self, currency: str):
        self.currency = currency
        super().__init__("invoice invalid currency error", currency=currency)


class MaxObjectPositionError(BaseCoreError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "max object position error"

    def __init__(self, object_name: str):
        self.object_name = object_name
        super().__init__("max object position error", object_name=object_name)


class BotNotFoundError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "bot not found error"

    def __init__(self, bot_id: int | None):
        super().__init__(bot_id=bot_id if bot_id else "")


class AdminGroupBotNotFoundError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "admin group bot not found error"

    def __init__(self, group_id: int | None):
        super().__init__(group_id=group_id if group_id else "")


class GroupNotFoundByIdError(ErrorWithHTTPStatus):
    status_code = status.HTTP_404_NOT_FOUND
    text_variable = "group not found by id error"

    def __init__(self, group_id: int):
        self.group_id = group_id
        super().__init__(group_id=group_id)
