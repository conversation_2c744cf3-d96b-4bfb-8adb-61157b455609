import asyncio
from fastapi import Depends
from functools import wraps
from psutils.exceptions.exception_handlers import UnknownErrorHandler

import schemas
from db.models import ClientBot, ExternalLoginRequest, User
from exceptions import (
    AuthExternalLoginBotRequiredError, AuthExternalLoginBotTypeInvalidError,
    AuthExternalLoginNotFoundError, AuthExternalLoginUnknownError, BaseAuthError,
)
from utils.type_vars import FuncT
from ..deep_links import ExternalLoginDeepLink
from ..depend import get_user_optional
from ..functions import create_auth_session_and_get_tokens, create_user_access_token
from ...api.depends import get_lang

handle_error = UnknownErrorHandler(AuthExternalLoginUnknownError, BaseAuthError)


def load_required(func: FuncT) -> FuncT:
    if asyncio.iscoroutinefunction(func):
        @wraps(func)
        async def wrapper(self: "ExternalLoginService", *args, **kwargs):
            if not self.request:
                raise ValueError(
                    "use ExternalLoginService.load_request() before using this method"
                )
            return await func(self, *args, **kwargs)

        return wrapper
    else:
        @wraps(func)
        def wrapper(self: "ExternalLoginService", *args, **kwargs):
            if not self.request:
                raise ValueError(
                    "use ExternalLoginService.load_request() before using this method"
                )
            return func(self, *args, **kwargs)

        return wrapper


class ExternalLoginService:
    def __init__(
            self,
            user: User | None = Depends(get_user_optional),
            lang: str | None = Depends(get_lang),
    ):
        assert isinstance(user, User | None)

        self.user: User | None = user
        self.request: ExternalLoginRequest | None = None
        self.lang: str | None = lang

    def set_user(self, user: User | None):
        self.user = user

    @property
    def safe_user_id(self) -> int | None:
        return self.user.id if self.user else None

    @load_required
    async def get_messanger_link(self):
        bot = await ClientBot.get(self.request.bot_id)

        return ExternalLoginDeepLink(
            external_login_request_uuid=self.request.uuid,
        ).to_str(bot.bot_type, bot.id_name)

    @load_required
    async def get_schema(self) -> schemas.ExternalLoginSchema:
        request = self.request

        token_data = None
        new_logged_in_data = None

        if request.status == schemas.ExternalLoginRequestStatusEnum.SUCCESS and (
                request.purpose == schemas.ExternalLoginRequestPurposeEnum.AUTH or
                request.purpose == schemas.ExternalLoginRequestPurposeEnum.LINK or
                request.purpose == schemas.ExternalLoginRequestPurposeEnum.RECEIPT or
                request.purpose == schemas.ExternalLoginRequestPurposeEnum.INVITATION
        ):
            user = request.user
            if not user:
                raise ValueError("User must be specified here")

            if request.auth_source and request.device_info:
                new_logged_in_data = await create_auth_session_and_get_tokens(
                    user, request.auth_source, request.device_info
                )
            else:
                token = create_user_access_token(user.id)
                token_data = schemas.Token(
                    token=token,
                    token_type="bearer"
                )

        return schemas.ExternalLoginSchema(
            **request.as_dict(True),
            messanger_link=await self.get_messanger_link(),
            incust_external_id=(
                request.user.incust_external_id
                if request.user else None
            ),
            logged_in_token_data=token_data,
            new_logged_in_data=new_logged_in_data
        )

    async def create(
            self, data: schemas.CreateExternalLoginData
    ) -> schemas.ExternalLoginSchema:
        bot = await ClientBot.get(data.bot_id)
        if not bot:
            raise AuthExternalLoginBotRequiredError()

        if data.type.value != bot.bot_type:
            raise AuthExternalLoginBotTypeInvalidError(
                data.type.value,
                bot.bot_type,
            )

        self.request = await ExternalLoginRequest.create(
            **data.dict(),
            user_id=self.safe_user_id,
            lang=self.lang,
        )
        return await self.get_schema()

    async def load_request(self, uuid: str) -> ExternalLoginRequest:
        self.request = await ExternalLoginRequest.get(
            uuid=uuid,
        )

        if not self.request:
            raise AuthExternalLoginNotFoundError(uuid)
        return self.request

    async def load_if_uuid(self, uuid: str | None):
        if uuid:
            return await self.load_request(uuid)
        return None

    @handle_error
    async def get_external_login(
            self, uuid: str | None = None,
    ) -> schemas.ExternalLoginSchema:
        await self.load_if_uuid(uuid)
        return await self.get_schema()

    @handle_error
    @load_required
    async def set_external_login_status(
            self, status: schemas.ExternalLoginRequestStatusEnum,
    ) -> schemas.ExternalLoginSchema:
        request = self.request  # pycharm shows unreachable code, if use
        # self.request.update. Pycharm BUG
        await request.update(status=status)
        return await self.get_schema()
