from datetime import datetime, timedelta

from sqlalchemy import and_, or_

from db import models


def get_notification_settings_statement(
        stmt, profile_id: int | None,
        target: str, ignore: bool = False,
        with_auth_session: bool = False,
):
    if profile_id and not ignore:
        stmt = stmt.outerjoin(
            models.NotificationSetting, and_(
                models.NotificationSetting.group_id == profile_id,
                models.NotificationSetting.user_id == models.User.id,
                models.NotificationSetting.target == target
            ),
        )

        if with_auth_session:
            one_minute_ago = datetime.utcnow() - timedelta(minutes=1)

            or_statement = or_(
                models.NotificationSetting.id.is_(None),
                models.NotificationSetting.is_enabled.is_(True),
                models.NotificationSetting.is_enabled.is_(None),
                and_(
                    models.NotificationSetting.is_enabled.is_(False),
                    models.AuthSession.last_activity > one_minute_ago,
                )
            )
        else:
            or_statement = or_(
                models.NotificationSetting.id.is_(None),
                models.NotificationSetting.is_enabled.is_(True),
                models.NotificationSetting.is_enabled.is_(None),
            )

        stmt = stmt.where(
            or_statement
        )
        if with_auth_session:
            stmt = stmt.group_by(models.AuthSession.id)
        else:
            stmt = stmt.group_by(models.User.id)
    return stmt
