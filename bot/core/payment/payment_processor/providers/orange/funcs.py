from core.payment.exceptions import PaymentNotFoundCredentialsError


def get_orange_payment_data(credentials) -> tuple[str, str, str, bool, str, str]:

    is_sandbox = False
    partner_code = credentials.get("partner_code")
    client_id = credentials.get("client_id")
    client_secret = credentials.get("client_secret")
    phone = credentials.get("phone")
    encrypted_pin_code = credentials.get("encrypted_pin_code")

    if not any([client_id, client_secret]):
        raise PaymentNotFoundCredentialsError(f"{credentials=}")

    return partner_code, client_id, client_secret, is_sandbox, phone, encrypted_pin_code
