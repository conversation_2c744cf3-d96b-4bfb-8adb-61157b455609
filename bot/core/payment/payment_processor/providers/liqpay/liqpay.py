import logging

from fastapi import (HTTPException, status as http_status)
from fastapi.responses import FileResponse

from core.payment.exceptions import (
    LiqpayRroDataNotFoundError, LiqpayRroDataNotValidError,
    PaymentGetSignatureDataError,
)
from db import crud
from db.models import Invoice, Payment
from schemas import PaymentCallBackData, PaymentLiqPayCallBackData, PmtExtTypes
from .funcs import (
    add_fiscal_data, check_callback_data, get_credentials_data, get_liqpay_client,
)
from ..base import PaymentProvider

debugger = logging.getLogger('debugger.payments.liqpay')


async def get_image_ex(payment_method: str, image_path: str):
    try:
        if payment_method == PmtExtTypes.liqpay.value:
            return FileResponse(f'static/images/store/liqpay/{image_path}')
    except Exception as err:
        logging.error(f'{str(err)}')
    raise HTTPException(
        status_code=http_status.HTTP_404_NOT_FOUND, detail='Not found'
    )


class LiqpayProvider(PaymentProvider):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def return_status(self, status: str):
        return {"status": status}

    async def get_payment_uuid(self, data: dict):
        ...

    async def get_payment_result(
            self,
            data: PaymentLiqPayCallBackData,
            credentials: dict,
    ) -> PaymentCallBackData:

        public_key, private_key, _, is_sandbox = get_credentials_data(credentials)

        callback_data = check_callback_data(public_key, private_key, data)
        debugger.debug(f"{callback_data=}")

        payment_data = PaymentCallBackData(
            callback_data=callback_data,
            is_sandbox=is_sandbox,
            status=callback_data.get("status"),
            external_id=callback_data.get('payment_id'),
            card_mask=callback_data.get("sender_card_mask2"),
            card_type=callback_data.get("sender_card_type"),
        )

        return payment_data

    @classmethod
    async def create_payment(
            cls,
            invoice: Invoice,
            payment: Payment,
            amount_to_pay: int,
            credentials: dict,
            brand_id: int,
            store_id: int | None,
            lang: str,
            success_url: str,
            server_url: str,
    ) -> dict:
        result = {"data": {}}

        params = {
            'action': 'pay',
            'server_url': server_url,
            'order_id': payment.uuid_id,
            'amount': f"{amount_to_pay / 100:.2f}",
            'currency': invoice.currency,
            'description': invoice.title,
            'result_url': success_url,
            'language': lang if lang else 'ua',
            'version': '3',
            # for save card token
            # 'customer': order.user_id,
            # 'recurringbytoken': 1,
        }

        liqpay, is_need_fiscal = await get_liqpay_client(credentials)

        if is_need_fiscal:
            order_id = await crud.get_store_order_id_by_invoice(invoice.id)
            if order_id:
                rro_info = await add_fiscal_data(order_id, brand_id, store_id)
                if not rro_info.get('items'):
                    result['admin'] = True
                    raise LiqpayRroDataNotFoundError()

                total_amount_fiscal = sum(
                    product.get('cost', 0) for product in rro_info.get('items')
                )

                debugger.debug(
                    f"rro_info: {total_amount_fiscal=}, {payment.amount=}"
                )

                if abs(round(total_amount_fiscal, 2) - payment.amount / 100) > 1:
                    result['admin'] = True
                    raise LiqpayRroDataNotValidError(
                        total_amount_fiscal=round(total_amount_fiscal, 2),
                        payment_amount=payment.amount / 100
                    )

                if invoice.email:
                    rro_info["delivery_emails"].append(invoice.email)

                params['rro_info'] = rro_info

        debugger.debug(f'{params=}')
        await payment.save_pmt_data({"liqpay": params})

        result['data']['signature'] = liqpay.cnb_signature(params)
        result['data']['data'] = liqpay.cnb_data(params)

        if not any([result.get('signature'), result.get('data')]):
            raise PaymentGetSignatureDataError("")

        return result
