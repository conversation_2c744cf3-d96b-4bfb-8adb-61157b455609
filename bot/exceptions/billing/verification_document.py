from starlette import status

from utils.exceptions import ErrorWithHTTPStatus
from ..base import ObjectNotFoundError


class VerificationDocumentNotFoundError(ObjectNotFoundError):
    pass


class MaximumVerificationDocumentCountReachedError(ErrorWithHTTPStatus):
    status_code = status.HTTP_403_FORBIDDEN

    def __init__(self, max_count: int):
        self.max_count = max_count
        super().__init__(
            max_count=max_count,
            detail_data={"max_count": max_count}
        )
