from starlette import status

from utils.exceptions import ErrorWithHTTPStatus
from ..base import IdNotUniqueError, ObjectListNotFoundError, ObjectNotFoundError


class AdUnitNotFoundError(ObjectNotFoundError):
    pass


class AdUnitsNotFoundError(ObjectListNotFoundError):
    pass


class AdUnitsIdNotUniqueError(IdNotUniqueError):
    pass


class NextAdUnitNotFoundError(ErrorWithHTTPStatus):
    status_code = status.HTTP_404_NOT_FOUND
