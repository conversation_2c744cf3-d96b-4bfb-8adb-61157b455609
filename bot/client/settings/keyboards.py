from db.models import User, ClientBot, UserSettings

from utils.text import f, c
from utils.redefined_classes import *

from utils.keyboards import active_button


async def get_settings_keyboard(chat_id: int, lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=1)

    user = await User.get(chat_id)
    bot = await ClientBot.get_current()
    user_settings = await UserSettings.get(user, bot)

    button_text = await f(
        "change lang button", lang,
        lang_code=lang.upper(),
        current_flag=await f(f"{lang}_flag", lang),
    )
    keyboard.insert(InlineBtn(button_text, callback_data="langs_list"))

    current_timezone = user.get_timezone()
    time_zone_button_text = await f("edit timezone button", lang, current=current_timezone)
    keyboard.insert(InlineBtn(time_zone_button_text, callback_data="edit_timezone"))

    # выбираем все поля, которые заканчиваются на "notification", то есть все настройки
    for setting in filter(lambda x: x.endswith("_notification"), dir(user_settings)):
        setting_text = await f(setting.replace("_notification", "_setting"), lang)
        if getattr(user_settings, setting):
            setting_text = await active_button(lang, setting_text)
            callback_data = {setting: False}
        else:
            callback_data = {setting: True}
        keyboard.insert(InlineBtn(setting_text, callback_data=c("settings", **callback_data)))

    if await user_settings.edited_mailing_mode():
        button_text = await f("mailing notifications settings button", lang)
        keyboard.row(InlineBtn(button_text, callback_data="mailing_notifications_settings"))

    return keyboard


async def get_mailing_notifications_settings_keyboard(user: User, lang: str) -> InlineKb:
    bot = await ClientBot.get_current()

    keyboard = InlineKb(row_width=1)

    user_settings = await UserSettings.get(user, bot)

    groups_settings = await user_settings.edited_mailing_mode()

    for group_settings in groups_settings:
        handle = await f("group mailing setting button", lang, group_name=group_settings.group.name)
        button_text = await f(f"{group_settings.mailing_mode} button", lang, handle=handle)

        mailing_mode = "blocked" if group_settings.mailing_mode == "with_sound" else "with_sound"

        callback_data = c("mailing_mode", id=group_settings.id, m=mailing_mode)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    return keyboard
