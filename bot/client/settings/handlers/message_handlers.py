from pytz import all_timezones

from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from utils.text import f
from utils.router import Router

from ..states import Settings


async def user_timezone_handler(message: types.Message, state: FSMContext, lang: str):
    if message.text not in all_timezones:
        return await message.answer(await f("unknown timezone error", lang))

    await state.update_data(timezone=message.text)
    await Settings.Save.set()
    await Router.state_menu(message, state, lang)


def register_settings_message_handlers(dp: Dispatcher):
    dp.register_message_handler(user_timezone_handler, state=Settings.TimeZone)
