import logging

from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext
from aiogram.types import ContentTypes

from client.helpers import send_error
from client.main.incust_funcs import show_card
from client.main.keyboards import (
    get_menu_keyboard, get_open_scan_receipt_keyboard,
    get_open_shop_web_and_webapp_keyboard,
)
from core.bot.ewallet_handlers import ewallet_button_handler
from core.bot.handlers import MenuButtonFilter
from core.bot.profile.functions import send_tg_profile
from core.chat import user_to_chat
from core.custom_texts import ct
from core.loyalty.incust_client_adapter import (
    get_or_create_incust_customer,
    run_with_incust_client_api,
)
from incust_client_api_client.api.card_info_api import CardInfoApi
from core.incust.types import IncustCustomerInfo
from db.models import LoyaltySettings
from utils.qrcode import generate_qr_code
from core.menu_in_store.functions import send_menu_in_store
from core.referral.functions import share_and_earn
from core.user.functions import send_user_cart_tg
from core.wallet.functions import wallet_show
from db.crud import delete_currently_running_vm_chats
from db.models import (
    Brand, ClientBot, MenuInStore, User,
    UserAnalyticAction,
)
from schemas import ChatTypeEnum
from utils.text import f
from ...filters import ActiveMenuInStoreButtonFilter


async def chat_button_handler(message: types.Message, user: User, bot: ClientBot):
    bot_from_db = await ClientBot.get_current()
    await delete_currently_running_vm_chats(user.id, bot.id)
    await user_to_chat(
        ChatTypeEnum.USER_WITH_GROUP, message, group_id=bot_from_db.group.id
    )


async def hide_full_menu_button_handler(message: types.Message, user: User, lang: str):
    user_bot_activity = await user.activity_in_bot
    result = await user_bot_activity.set_menu_type(show_full_menu=False)
    if not result:
        return await send_error(message)

    keyboard = await get_menu_keyboard(message.chat.id)
    await message.answer(await f(f"full menu hid text", lang), reply_markup=keyboard)


async def show_full_menu_button_handler(message: types.Message, user: User, lang: str):
    user_bot_activity = await user.activity_in_bot
    result = await user_bot_activity.set_menu_type(show_full_menu=True)
    if not result:
        return await send_error(message)

    keyboard = await get_menu_keyboard(message.chat.id)
    await message.answer(await f("full menu showed text", lang), reply_markup=keyboard)


async def profile_button_handler(
    message: types.Message, state: FSMContext, bot: ClientBot, user: User, lang: str
):
    await state.finish()
    await send_tg_profile(message, bot, user, lang)


async def benefits_button_handler(
    message: types.Message, state: FSMContext,
    user: User, bot: ClientBot, lang: str,
):
    await state.finish()

    brand = await Brand.get_by_group(bot.group_id)
    loyalty_settings = await LoyaltySettings.get_loyalty_settings_for_context(
        brand_id=brand.id
    )
    if not loyalty_settings:
        menu_keyboard = await get_menu_keyboard(user, bot)
        return await message.answer(
            await f("loyalty not connected text", lang, brand_name=brand.name),
            reply_markup=menu_keyboard,
        )

    async with state.proxy() as data:
        data["brand_id"] = brand.id
        data["user_id"] = user.id

    logger = logging.getLogger("debugger.benefits_button_handler")

    try:
        incust_customer = await get_or_create_incust_customer(user, loyalty_settings, lang)
        card_info = await run_with_incust_client_api(
            loyalty_settings,
            user,
            CardInfoApi.incust_controllers_client_client_card_info,
            lang=lang,
        )

        incust_customer_info = IncustCustomerInfo(
            incust_customer=incust_customer,
            card_info=card_info,
        )

        # Готуємо ShowIncustQrCardSchema (генеруємо локально QR по external_id)
        from schemas import ShowIncustQrCardSchema  # локальний імпорт, щоб уникнути циклів

        qr_photo = generate_qr_code(user.incust_external_id, convert_8_bit_channel=True)
        qr_result = ShowIncustQrCardSchema(
            identifier=user.incust_external_id,
            photo=qr_photo,
        )

        await show_card(message, qr_result, lang, incust_customer_info)

    except Exception as ex:
        logger.error(ex, exc_info=True)
        await message.answer(await f("client bot nodata incust text", lang))

    await state.finish()
    return None


async def shop_button_handler(
    message: types.Message, state: FSMContext, user: User, lang: str
):
    bot = await ClientBot.get_current()
    brand = await Brand.get_by_group(bot.group.id)

    await state.finish()

    message_text = await ct(bot, lang, "shop", "open text")
    keyboard = await get_open_shop_web_and_webapp_keyboard(bot, brand, lang)
    await message.answer(message_text, reply_markup=keyboard)

    await UserAnalyticAction.save_button_click(user, bot, "shop")


async def share_and_earn_button_handler(
    message: types.Message, state: FSMContext, user: User, lang: str
):
    bot = await ClientBot.get_current()
    brand = await Brand.get_by_group(bot.group.id)

    await state.finish()

    await share_and_earn(user, brand, lang, bot)


async def active_menu_in_store_button_handler(
    message: types.Message, state: FSMContext,
    menu_in_store: MenuInStore,
    user: User, lang: str
):
    await state.finish()
    bot = await ClientBot.get_current()
    await send_menu_in_store(message, state, lang, menu_in_store, user, bot)
    await set_menu_button(bot, user, lang)


async def show_user_cart_button_handler(
    _: types.Message,
    state: FSMContext,
    user: User,
    bot: ClientBot,
    lang: str,
):
    await state.finish()
    await send_user_cart_tg(user, bot, lang)


async def show_scan_receipt_button_handler(
    message: types.Message,
    state: FSMContext,
    bot: ClientBot,
    lang: str,
):
    brand = await Brand.get_by_group(bot.group.id)

    message_text = await f("open scan receipt text", lang)
    keyboard = await get_open_scan_receipt_keyboard(bot, lang, brand)

    await state.finish()
    await message.answer(message_text, reply_markup=keyboard)


async def wallet_button_handler(message: types.Message, user: User, lang: str):
    bot = await ClientBot.get_current()
    brand = await Brand.get_by_group(bot.group.id)

    return await wallet_show(user, brand, lang, message, bot)


def register_main_menu_handlers(dp: Dispatcher):

    dp.register_message_handler(
        chat_button_handler,
        MenuButtonFilter("chat", "start_button"),
        content_types=ContentTypes.TEXT,
        state="*",
    )

    dp.register_message_handler(
        hide_full_menu_button_handler,
        lequal="hide full menu button",
        content_types=ContentTypes.TEXT,
        state="*",
    )

    dp.register_message_handler(
        show_full_menu_button_handler,
        lequal="show full menu button",
        content_types=ContentTypes.TEXT,
        state="*",
    )

    dp.register_message_handler(
        profile_button_handler,
        MenuButtonFilter("main", "profile button"),
        content_types=ContentTypes.TEXT,
        state="*",
    )

    dp.register_message_handler(
        benefits_button_handler,
        MenuButtonFilter("main", "benefits button"),
        content_types=ContentTypes.TEXT,
        state="*",
    )

    dp.register_message_handler(
        shop_button_handler,
        MenuButtonFilter("shop", "button"),
        content_types=ContentTypes.TEXT,
        state="*",
    )
    dp.register_message_handler(
        share_and_earn_button_handler,
        MenuButtonFilter("shop", "share and earn button"),
        content_types=ContentTypes.TEXT,
        state="*",
    )
    dp.register_message_handler(
        show_scan_receipt_button_handler,
        MenuButtonFilter("main", "scan receipt button"),
        content_types=ContentTypes.TEXT,
        state="*",
    )

    dp.register_message_handler(
        active_menu_in_store_button_handler,
        ActiveMenuInStoreButtonFilter(),
        content_types=ContentTypes.TEXT,
        state="*",
    )

    dp.register_message_handler(
        show_user_cart_button_handler,
        MenuButtonFilter("main", "my qr", "show button"),
        content_types=ContentTypes.TEXT,
        state="*",
    )

    dp.register_message_handler(
        wallet_button_handler,
        MenuButtonFilter("main", "wallet button"),
        content_types=ContentTypes.TEXT,
        state="*",
    )

    dp.register_message_handler(
        ewallet_button_handler,
        lequal="client bot main ewallet account button",
        content_types=ContentTypes.TEXT,
        state="*",
    )

    dp.register_message_handler(
        ewallet_button_handler,
        lequal="client bot main ewallet accounts button",
        content_types=ContentTypes.TEXT,
        state="*",
    )
