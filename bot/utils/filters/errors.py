from aiogram import types
from aiogram.dispatcher.filters import BoundFilter


class ExceptionTextFilter(BoundFilter):
    key = "exception_text"

    def __init__(self, exception_text: str):
        if not isinstance(exception_text, str):
            raise ValueError("exception_text must be a string")

        self.exception_text = exception_text

    async def check(self, update: types.Update, exception: Exception) -> bool:
        exception = str(exception)

        if not exception:
            return False

        return self.exception_text in exception

