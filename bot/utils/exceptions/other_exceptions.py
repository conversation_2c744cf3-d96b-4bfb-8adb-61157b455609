class AlreadyInGroupError(Exception):
    def __init__(self, user, group):
        self.txt = f"User {user.name}({user.chat_id}) already in group {group.name}({group.id})"


class AlreadyHasFriendError(Exception):
    def __init__(self, user, friend):
        self.txt = f"User {user.name}({user.chat_id}) already has friend {friend.name}({friend.chat_id})"


class EmptyMessageArgs(Exception):
    pass
