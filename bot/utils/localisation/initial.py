from psutils.localisation import Localisation
from psutils.storage.redis import RedisStorage2

import config as cfg
from config import (
    IS_LOCALISATION_AUTO_TRANSLATE_ENABLED, PATH_TO_GOOGLE_CREDS_JSON, TEXT_VARIABLES,
)
from utils.platform_admins import send_message_to_platform_admins


async def notify_admins(variable: str, lang: str):
    return await send_message_to_platform_admins(
        f"Змінну {variable} не знайдено. Мова: {lang}"
    )


REDIS_PREFIX = "langs"
if cfg.REDIS_PREFIX:
    REDIS_PREFIX += f"-{cfg.REDIS_PREFIX}"

localisation = Localisation(
    TEXT_VARIABLES,
    creds_file_path=PATH_TO_GOOGLE_CREDS_JSON,
    storage=RedisStorage2(
        host=cfg.REDIS_HOST,
        port=cfg.REDIS_PORT,
        db=cfg.LOCALISATION_REDIS_DB,
        prefix=REDIS_PREFIX,
        pool_size=5000,
    ),
    notify_admins_func=notify_admins,
    is_auto_translate_enabled=IS_LOCALISATION_AUTO_TRANSLATE_ENABLED,
)


async def setup_language_localisation(lang: str):
    if IS_LOCALISATION_AUTO_TRANSLATE_ENABLED:
        from db.models import TranslationBackground
        translation_background: TranslationBackground = await (
            TranslationBackground.create(
                lang
            ))
        return translation_background
