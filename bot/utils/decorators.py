import asyncio
import logging
from functools import wraps
from typing import Any

from aiogram import types
from psutils.exceptions import ErrorWithTextVariable
from psutils.redefined_classes import Bo<PERSON>, InlineKb, MenuKb
from psutils.type_vars import FuncT

from utils.text import f


async def _send_error_to_user(
        text_variable: str, text_kwargs: dict[str, Any],
        keyboard: InlineKb | MenuKb | None
):
    from db.models import ClientBot, User  # must be here to avoid circular import

    tg_user = types.User.get_current()
    if not tg_user:
        return

    user_chat_id = tg_user.id
    bot_token = await ClientBot.get_current_bot_token()
    bot = Bot.get_current()
    if not all([user_chat_id, bot_token, bot]):
        return

    with bot.with_token(bot_token):
        lang = await User.get_lang(user_chat_id)
        await bot.send_message(
            user_chat_id, await f(text_variable, lang, **text_kwargs),
            reply_markup=keyboard
        )


def catch_error_with_text_variable(func: FuncT) -> FuncT:

    @wraps(func)
    async def wrapper(*args, **kwargs):
        return_error = kwargs.pop("return_on_error", False)
        error_keyboard = kwargs.pop("error_keyboard", None)
        try:
            result = await func(*args, **kwargs)
        except ErrorWithTextVariable as e:
            await _send_error_to_user(e.text_variable, e.text_kwargs, error_keyboard)
            if return_error:
                logger = logging.getLogger()
                logger.error(e, exc_info=True)
                return e
            else:
                raise
        else:
            return result

    return wrapper


def graceful_shutdown(func: FuncT):
    @wraps(func)
    def wrapper(*args, **kwargs):
        loop = asyncio.new_event_loop()
        loop.run_until_complete(asyncio.ensure_future(func(*args, **kwargs), loop=loop))

    return wrapper
