from copy import deepcopy
from dataclasses import fields, is_dataclass
from typing import Any

from starlette.datastructures import UploadFile


def form_data_to_dict(data: Any, exclude_none: bool = False):
    """
    Converts dataclass into dict. Don't deepcopy UploadFile, because it causes errors
    @param data: dataclass
    @param exclude_none: determines whether to exclude None
    @return: dict from dataclass
    """
    if not is_dataclass(data):
        raise ValueError("data is not dataclass")

    result = {}

    for field in fields(data):
        value = getattr(data, field.name)
        if value is not None and not isinstance(value, UploadFile):
            value = deepcopy(value)

        if not exclude_none or value is not None:
            result[field.name] = value

    return result
