from typing import <PERSON>ple, Type

from psutils.func import kwargs_safe


class BaseSerializer:
    object_type: Type[object]
    fields: Tuple[str]

    @classmethod
    async def serialize_field(cls, value):
        if isinstance(value, dict):
            for k, v in value.items():
                value[k] = await cls.serialize_field(v)
        elif isinstance(value, list | tuple):
            value = list(value)
            for i, v in enumerate(value):
                value[i] = await cls.serialize_field(v)

        return value

    @classmethod
    async def serialize(cls, object, **kwargs):
        serialized_data = {}

        for field in cls.fields:
            if hasattr(cls, f"serialize_{field}"):
                method = getattr(cls, f"serialize_{field}")
                kwargs_safe_method = kwargs_safe(method)
                serialized_value = await kwargs_safe_method(object, data=kwargs)
            else:
                serialized_value = await cls.serialize_field(getattr(object, field))

            serialized_data[field] = serialized_value

        return serialized_data

    @classmethod
    async def serialize_list(cls, objects: list, **kwargs):
        return [await cls.serialize(object, **kwargs) for object in objects]
