import logging
from email.mime.base import MIMEBase

from email_validator import EmailNotValidError, validate_email
from psutils.mailing import GmailClient

logger = logging.getLogger("debugger.email")


async def send_with_attachments(
    gmail: GmailClient,
    destination: str,
    subject: str,
    html: str,
    from_name: str | None = None,
    attachments: list[MIMEBase] | None = None
):
    logger.debug(f'send_with_attachments -> {destination=}, {subject=}, {from_name=}')
    await gmail.send_email(
        destination=destination,
        subject=subject,
        body='',
        html=html,
        from_name=from_name,
        attachments=attachments
    )
    logger.debug(f"Email sent successfully, {destination=}, {subject=}")


def is_valid_email(email_str: str) -> bool:
    if not email_str:
        logging.error(f"Email is None: {email_str=}")
        return False
    try:
        validate_email(email_str)
    except EmailNotValidError as ex:
        logging.error(f"*** NOT VALID EMAIL REASON: {email_str}, {ex}")
        return False
    else:
        return True
