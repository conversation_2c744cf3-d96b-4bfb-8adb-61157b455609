from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from db.models import Poll, User

from utils.text import f
from utils.media import scale_photo
from core.media import download_file
from utils.router import Router

from ...db_funcs import check_user_polls

from friendly.main.keyboards import get_menu_keyboard

from ..states import C<PERSON><PERSON><PERSON>
from ...edit.states import Edit<PERSON>oll


async def cancel_button_handler(message: types.Message, state: FSMContext, user: User, lang: str):
    await state.finish()
    keyboard = await get_menu_keyboard(user, lang)
    await message.answer(await f("action cancel text", lang), reply_markup=keyboard)

    has_polls = await check_user_polls(user.id)
    if has_polls:
        await EditPoll.List.set()
        await Router.state_menu(message, state, lang)


async def name_handler(message: types.Message, state: FSMContext, lang: str):

    if message.content_type != "text":
        return await message.answer(await f("not text error", lang))

    if message.text == await f("skip button", lang):
        name = await Poll.generate_name(lang)
    else:
        name = message.text

    await state.update_data(name=name)

    await CreatePoll.Question.set()
    await Router.state_menu(message, state)


async def question_handler(message: types.Message, state: FSMContext, lang: str):

    if message.content_type not in Poll.AVAILABLE_CONTENT_TYPES:
        return await message.answer(await f("unsupported poll format", lang))

    if message.content_type == "text":
        await state.update_data(question=message.text, content_type=message.content_type)
        await CreatePoll.FirstOption.set()
        return await Router.state_menu(message, state)

    file_path = await download_file(message)

    if message.content_type == "photo":
        await scale_photo(file_path)

    await state.update_data(
        question=message.caption,
        content_type=message.content_type,
        file_path=file_path
    )

    await CreatePoll.FirstOption.set()
    await Router.state_menu(message, state, lang)


async def first_option_handler(message: types.Message, state: FSMContext, lang: str):
    if message.content_type != "text":
        return await message.answer(await f("not text error", lang))

    await state.update_data(options=[message.text])

    await CreatePoll.ElseOptions.set()
    await Router.state_menu(message, state, lang)


async def save_button_handler(message: types.Message, state: FSMContext, lang: str):
    await CreatePoll.Save.set()
    await Router.state_menu(message, state, lang)


async def else_option_handler(message: types.Message, state: FSMContext, lang: str):
    if message.content_type != "text":
        return await message.answer(await f("not text error", lang))

    async with state.proxy() as state_data:
        state_data["options"].append(message.text)

    await Router.state_menu(message, state, lang)


def register_create_poll_message_handlers(dp: Dispatcher):
    dp.register_message_handler(
        cancel_button_handler,
        cancel_button=True,
        content_types=types.ContentType.TEXT,
        state=CreatePoll,
    )

    dp.register_message_handler(
        name_handler,
        content_types=types.ContentTypes.ANY,
        state=CreatePoll.Name,
    )

    dp.register_message_handler(
        question_handler,
        content_types=types.ContentTypes.ANY,
        state=CreatePoll.Question,
    )

    dp.register_message_handler(
        first_option_handler,
        content_types=types.ContentTypes.ANY,
        state=CreatePoll.FirstOption,
    )

    dp.register_message_handler(
        save_button_handler,
        save_button=True,
        content_types=types.ContentTypes.TEXT,
        state=CreatePoll.ElseOptions,
    )

    dp.register_message_handler(
        else_option_handler,
        content_types=types.ContentTypes.ANY,
        state=CreatePoll.ElseOptions,
    )
