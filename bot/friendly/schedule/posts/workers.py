import asyncio
import logging
import traceback

from psutils.convertors import datetime_to_str

from config import FRIENDLY_SCHEDULE_STOPPED_TIMEOUT
from db import DBSession, own_session
from db.models import SchedulesChannelsAssociation
from utils.date_time import utcnow
from utils.platform_admins import send_message_to_platform_admins
from utils.processes_manager.background_worker import LoopBackgroundWorker
from utils.text import f
from .db_funcs import get_schedules_for_stopped, get_schedules_to_enable
from .functions import send_error_for_admins, send_post_to_channel


class FriendlyPostsWorker(LoopBackgroundWorker):
    DEFAULT_NAME = "friendly posts"
    DEFAULT_TIMEOUT = 1

    async def iteration(self):
        with DBSession():
            schedules_to_enable = await get_schedules_to_enable()
            if not schedules_to_enable:
                return "no_schedules"

            for schedule, channel, sca, groups in schedules_to_enable:
                try:
                    await send_post_to_channel(schedule, channel)
                except Exception as e:
                    association = (
                        None if schedule.channel_id == channel.id
                        else await SchedulesChannelsAssociation.get(
                            schedule.id, channel.id
                        )
                    )
                    post_index = (
                        schedule.index_send_message
                        if schedule.channel_id == channel.id
                        else association.index_send_message
                    )

                    await send_error_for_admins(schedule, channel)

                    await schedule.channel.add_log(
                        await f(
                            "friendly logs error schedule send post text",
                            channel.lang,
                            post_index=post_index,
                            schedule_name=schedule.name,
                            date_time=datetime_to_str(utcnow())
                        )
                    )

                    error = "\n".join(traceback.format_stack())
                    await send_message_to_platform_admins(error)
                    await send_message_to_platform_admins(
                        await f(
                            "schedule error", "ru",
                            name=schedule.name,
                            id=schedule.id,
                            channel_name=channel.name,
                        )
                    )
                    logger = logging.getLogger("error.scheduler")
                    logger.error(e, exc_info=True)

                    if schedule.channel_id == channel.id:
                        return await schedule.stop()
                    else:
                        return await association.stop()
            return "sent"


@own_session
async def stop_old_schedules():
    loop = asyncio.get_event_loop()
    schedules_for_stopped = await get_schedules_for_stopped()

    if not schedules_for_stopped:
        timeout = FRIENDLY_SCHEDULE_STOPPED_TIMEOUT
        return loop.call_later(timeout, asyncio.create_task, stop_old_schedules())

    try:
        for schedule in schedules_for_stopped:
            await schedule.channel.add_log(
                await f(
                    "friendly logs warning schedule stop from worker text",
                    schedule.channel.lang,
                    count_days=schedule.count_days
                )
            )
            await schedule.stop()
    except:
        pass

    loop.call_soon(asyncio.create_task, stop_old_schedules())
