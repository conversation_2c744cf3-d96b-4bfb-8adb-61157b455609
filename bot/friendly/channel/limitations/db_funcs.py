from datetime import datetime
from typing import List

from sqlalchemy import and_, or_, func

from db import db_func, sess
from db.models import Channel, ChatMember


@db_func
def get_members_without_agree_rules() -> List[int]:
    query = sess().query(ChatMember.id)
    query = query.join(Channel, Channel.id == ChatMember.channel_id)

    query = query.filter(Channel.need_agree_rules != "nobody")
    query = query.filter(ChatMember.is_agreed_rules.is_(False))
    query = query.filter(ChatMember.ban_user_status.is_(False))

    condition_new_user = and_(
        Channel.need_agree_rules == "new",
        ChatMember.joined_datetime > Channel.need_agree_rules_set_datetime
    )
    conditions_needed_agree_rules = or_(
        Channel.need_agree_rules == "all",
        condition_new_user,
    )
    query = query.filter(conditions_needed_agree_rules)

    time_now = datetime.utcnow()
    conditions_time = and_(
        ChatMember.rules_start_datetime.is_not(None),
        func.timediff(time_now, ChatMember.rules_start_datetime) > Channel.limited_agree_rules_time
    )
    query = query.filter(conditions_time)

    result = sess().scalars(query).all()
    return result
