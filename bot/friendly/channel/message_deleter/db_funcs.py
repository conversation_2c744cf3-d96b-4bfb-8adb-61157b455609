from datetime import datetime
from typing import List

from sqlalchemy import delete, exists, func, select, union_all
from sqlalchemy.orm import aliased

from config import OLD_MESSAGES_QUERY_LIMIT, TIMEOUT_DELETE_RESTRICTION_MESSAGES
from db import db_func, sess
from db.models import Channel, ClientBot, FriendlyChatMessage


@db_func
def get_old_messages() -> list[tuple[int, int, int, str, int, str, str]]:
    now = datetime.utcnow()

    def get_base_stmt():
        return select(
            FriendlyChatMessage.id,
            FriendlyChatMessage.message_id,
            FriendlyChatMessage.chat_id,
            FriendlyChatMessage.message_type,
            Channel.id,
            Channel.name,
            ClientBot.token,
        ).join(
            Channel,
            FriendlyChatMessage.chat_id == Channel.chat_id,
        ).join(
            ClientBot,
            FriendlyChatMessage.bot_id == ClientBot.id
        )

    restrictions_stmt = get_base_stmt().where(
        FriendlyChatMessage.message_type == "restriction_message",
        FriendlyChatMessage.datetime + TIMEOUT_DELETE_RESTRICTION_MESSAGES <= now
    )

    on_screen_conditions = (
        Channel.system_message_on_screen_time.is_not(None),
        (func.unix_timestamp(
            FriendlyChatMessage.datetime
        ) + Channel.system_message_on_screen_time) <= now.timestamp()
    )

    friendly_chat_message_2 = aliased(FriendlyChatMessage)

    welcome_message_stmt = get_base_stmt().where(
        *on_screen_conditions,
        FriendlyChatMessage.message_type == "welcome_message",
        exists(friendly_chat_message_2.id).where(
            friendly_chat_message_2.chat_id == FriendlyChatMessage.chat_id,
            friendly_chat_message_2.message_type == "welcome_message",
            friendly_chat_message_2.datetime > FriendlyChatMessage.datetime,
        )
    )

    other_stmt = get_base_stmt().where(
        *on_screen_conditions,
        FriendlyChatMessage.message_type.not_in(
            ("welcome_message", "restriction_message")
        ),
    )

    statements = [s.limit(OLD_MESSAGES_QUERY_LIMIT) for s in
                  (restrictions_stmt, welcome_message_stmt, other_stmt)]

    stmt = select(union_all(*statements).subquery())
    return sess().execute(stmt).fetchall()


@db_func
def delete_old_messages_by_ids(ids: list[int]):
    stmt = delete(FriendlyChatMessage)
    stmt = stmt.where(FriendlyChatMessage.id.in_(ids))
    sess().execute(stmt)
    sess().commit()
    return True


@db_func
def get_restriction_messages() -> List["FriendlyChatMessage"]:
    query = sess().query(FriendlyChatMessage)
    query = query.filter(FriendlyChatMessage.message_type == "restriction_message")
    result = query.all()
    return result
