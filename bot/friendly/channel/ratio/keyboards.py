from utils.text import f
from utils.redefined_classes import InlineKb, InlineBtn

from utils.keyboards import previous_button


async def get_limit_message_menu_keyboard(current_message_limit: int, lang: str):
    keyboard = InlineKb(row_width=1)

    if current_message_limit:
        button_text = await f("reset message limit button", lang)
        keyboard.insert(InlineBtn(button_text, callback_data="remove_message_limit"))

    keyboard.row(await previous_button(lang, mode=None))
    return keyboard
