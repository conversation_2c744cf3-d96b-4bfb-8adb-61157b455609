from contextlib import suppress

from aiogram import Dispatcher, types
from aiogram.types import ContentTypes
from aiogram.utils.exceptions import MessageError

from config import DEBUG
from db.models import Channel, ClientBot
from loggers import J<PERSON><PERSON>ogger


async def join_notification_handler(message: types.Message):
    channel = await Channel.get(chat_id=message.chat.id)
    if not channel or not channel.need_delete_join_notification:
        return

    bot_from_db = await ClientBot.get_current()
    logger = JSONLogger(
        "friendly.limitations", {
            "channel": {
                "id": channel.id,
                "name": channel.name,
            },
            "bot": {
                "id": bot_from_db.id,
                "name": bot_from_db.display_name,
            },
            "user": message.from_user,
        }
    )

    with suppress(MessageError):
        if DEBUG:
            logger.debug(
                f"Deleting join notification in chat {channel.name}: "
                f"{message.from_user.full_name}"
            )
        await message.delete()


async def left_notification_handler(message: types.Message):
    bot_from_db = await ClientBot.get_current()
    channel = await Channel.get(chat_id=message.chat.id, bot_id=bot_from_db.id)
    if not channel or not channel.need_delete_leave_notification:
        return

    logger = JSONLogger(
        "friendly.limitations", {
            "channel": {
                "id": channel.id,
                "name": channel.name,
            },
            "bot": {
                "id": bot_from_db.id,
                "name": bot_from_db.display_name,
            },
            "user": message.from_user,
        }
    )

    with suppress(MessageError):
        if DEBUG:
            logger.debug(
                f"Deleting left notification in chat {channel.name}: "
                f"{message.from_user.full_name}"
            )
        await message.delete()


def register_channel_telegram_notifications_handlers(dp: Dispatcher):
    dp.register_message_handler(
        join_notification_handler,
        content_types=ContentTypes.NEW_CHAT_MEMBERS,
        state="*"
    )

    dp.register_message_handler(
        left_notification_handler,
        content_types=ContentTypes.LEFT_CHAT_MEMBER,
        state="*"
    )
