from datetime import datetime, timedelta

from aiogram.dispatcher import FSMContext

from db.models import Draw, DrawResult

from utils.message import send_tg_message


async def send_notification(draw_result: DrawResult):
    utcnow = datetime.utcnow()

    try:
        await send_tg_message(draw_result.user.chat_id, "text", bot_token=draw_result.bot.token,
                              text=draw_result.draw.terms)
    except:
        pass

    last_datetime = draw_result.time_need_notification if draw_result.time_need_notification else utcnow
    interval = draw_result.draw.time_spending - last_datetime
    delta = interval.total_seconds() // (draw_result.count_notifications + 1)
    time_need_notification = last_datetime + timedelta(seconds=delta)

    count_notifications = draw_result.count_notifications + 1
    await draw_result.update(count_notifications=count_notifications, time_need_notification=time_need_notification)
