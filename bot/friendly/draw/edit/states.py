from aiogram.dispatcher.filters.state import StatesGroup, State


class EditDraw(StatesGroup):
    ChooseDraw = State()
    SelectMenu = State()
    ChooseField = State()
    Name = State()
    Media = State()
    Description = State()
    Terms = State()
    TimeSpending = State()
    ChannelsForSubscription = State()
    ChannelToInviteFriends = State()
    CountInviteFriends = State()
    FollowLink = State()
    CountFollowedLink = State()
    ChannelForWritingMessages = State()
    CountNeededMessages = State()
    InstructionsForWritingMessages = State()
    FilterWordsMessage = State()
    IsPublic = State()
    CountAwardCategories = State()
    AwardCategoryName = State()
    AwardCategoryCountWinners = State()
    AwardCategoryAward = State()
    Contact = State()
    CountNotifications = State()
    DeleteDraw = State()
    DeleteFollowLink = State()
