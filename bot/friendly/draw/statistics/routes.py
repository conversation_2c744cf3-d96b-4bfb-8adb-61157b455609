from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import Draw

from utils.keyboards import previous_button

from utils.redefined_classes import InlineKb, InlineBtn

from utils.message import send_tg_message
from utils.text import f, c

from utils.router import Router

from friendly.draw.statistics import list_users_with_all_conditions, count_followed_link_users_in_draw

from friendly.draw.statistics.list_drawers import MembersListDrawer

from friendly.draw.statistics.states import DrawStatistics

from friendly.draw.winners.db_funcs import get_winners


async def send_show_results_draw_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    draw = await Draw.get(state_data.get("draw_id"))

    users_count = await count_followed_link_users_in_draw(draw)
    message_text1 = await f("draw info count users followed link text", lang, users_count=users_count)

    met_all_conditions = await list_users_with_all_conditions(draw)
    users_count = len(met_all_conditions)
    message_text2 = await f("draw info count users all conditions text", lang, users_count=users_count)

    keyboard = InlineKb(row_width=2)

    members_count = len(draw.results)
    button_text = await f("draw info members button", lang, members_count=members_count)
    callback_data = c("show_members", draw_id=draw.id)
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    winners_count = await get_winners(draw.id, operation="count")
    button_text = await f("draw info winners button", lang, winners_count=winners_count)
    callback_data = c("show_winners", draw_id=draw.id)
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("draw settings button", lang)
    callback_data = c("draw_settings", draw_id=draw.id)
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("draw delete button", lang)
    callback_data = c("delete_draw", draw_id=draw.id)
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    keyboard.insert(await previous_button(lang))

    message_text = "\n".join([message_text1, message_text2])
    return await send_tg_message(message.chat.id, "text", keyboard=keyboard, text=message_text)


def register_statistics_draw_routes(router: Router):
    router.add_route(DrawStatistics.ShowMembers, MembersListDrawer())
    router.add_route(DrawStatistics.ShowResults, send_show_results_draw_menu)
