from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import Draw, DrawResult

from utils.message import send_or_edit_message

from utils.router import Router

from utils.text import f

from friendly.draw.winners.list_drawers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MembersList<PERSON>rawer, AwardsListDrawer

from friendly.draw.winners.states import DrawWinners


def register_winners_draw_routes(router: Router):
    router.add_route(DrawWinners.ShowWinners, WinnersListDrawer())
    router.add_route(DrawWinners.<PERSON>oseWinner, MembersListDrawer())
    router.add_route(DrawWinners.ChooseAward, AwardsListDrawer())
