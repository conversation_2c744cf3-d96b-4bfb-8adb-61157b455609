from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from utils.router import Router

from ..states import CreateDraw


async def create_draw_button_handler(message: types.Message, state: FSMContext, lang: str):
    await state.update_data(main=True)

    await CreateDraw.first()
    await Router.state_menu(message, state, lang, set_state_message=True)


def register_create_draw_menu_handlers(dp: Dispatcher):
    dp.register_message_handler(
        create_draw_button_handler,
        lequal="create draw button",
        chat_type="private",
        content_types=types.ContentTypes.TEXT,
        state="*",
    )
