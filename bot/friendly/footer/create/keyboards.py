from aiogram.dispatcher import FSMContext

from utils.redefined_classes import InlineKb, InlineBtn

from utils.text import f, c

from utils.keyboards import active_button, get_navigation_keyboard


async def get_create_footer_keyboard(state: FSMContext, lang: str) -> InlineKb:
    state_data = await state.get_data()
    need_concatenation = state_data.get("need_concatenation", False)

    keyboard = InlineKb(row_width=2)

    button_text = await f("footer concatenation button", lang)
    if need_concatenation:
        button_text = await active_button(lang, button_text)

    callback_data = "concatenation"
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    return await get_navigation_keyboard(lang, keyboard)
