from aiogram import Dispatcher, types

from db.models import User

from ...functions import update_user_statistic

from ...status import Status

from ..functions import edit_post_keyboard


async def interest_button_handler(
        callback_query: types.CallbackQuery,
        callback_data: dict, user: User, lang: str,
):
    interest_post_id = callback_data.get("interest_post_id")
    interest = callback_data.get("name")
    statistics = await update_user_statistic(user.id, interest_post_id, interest, status=Status.LIKE)
    await edit_post_keyboard(callback_query.message, statistics, lang)


async def interest_not_show_button_handler(
        callback_query: types.CallbackQuery,
        callback_data: dict, user: User, lang: str,
):
    interest_post_id = callback_data.get("interest_post_id")
    statistics = await update_user_statistic(user.id, interest_post_id, status=Status.NOTHING)
    await edit_post_keyboard(callback_query.message, statistics, lang)


async def interest_all_button_handler(
        callback_query: types.CallbackQuery,
        callback_data: dict, user: User, lang: str,
):
    interest_post_id = callback_data.get("interest_post_id")
    statistics = await update_user_statistic(user.id, interest_post_id, status=Status.ALL)
    await edit_post_keyboard(callback_query.message, statistics, lang)


def register_interests_posts_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        interest_button_handler,
        callback_mode="interest",
        state="*",
    )

    dp.register_callback_query_handler(
        interest_not_show_button_handler,
        callback_mode="interest_now_show",
        state="*",
    )

    dp.register_callback_query_handler(
        interest_all_button_handler,
        callback_mode="interest_all",
        state="*",
    )
