from aiogram import types
from aiogram.dispatcher import FSMContext

from utils.router import Router
from utils.text import f

from .states import ConnectEmail
from ..api import api
from ..functions import get_token, send_lip_lep_user_info


async def send_enter_email(message: types.Message, lang: str):
    text = await f("lip lep enter email to connect", lang)
    return await message.answer(text)


async def send_confirm_email(message: types.Message, lang: str):
    text = await f("lip lep wait for confirm email message", lang)
    return await message.edit_text(text)


async def send_enter_password(message: types.Message, lang: str, mode: str):
    text = await f("lip lep create password", lang)
    if mode == "edit":
        return await message.edit_text(text)
    return await message.answer(text)


async def send_enter_confirm_password(message: types.Message, lang: str):
    text = await f("lip lep confirm password", lang)
    return await message.edit_text(text)


async def connect_email(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    await state.finish()

    token = await get_token(state.storage, state.user)
    await api.set_email(lang, state_data.get("email"), )

    lip_lep_user = await api.get_me(lang, token)

    await send_lip_lep_user_info(message, lip_lep_user, lang, with_keyboard=True)
    new_message = await message.answer(await f("email was connected to account", lang))
    await message.delete()
    return new_message


def register_connect_email_routes(router: Router):
    router.add_route(ConnectEmail.Email, send_enter_email)
    router.add_route(ConnectEmail.ConfirmEmail, send_confirm_email)
    router.add_route(ConnectEmail.Password, send_enter_password)
    router.add_route(ConnectEmail.ConfirmPassword, send_enter_confirm_password)
    router.add_route(ConnectEmail.ConnectEmail, connect_email)
