from utils.redefined_classes import InlineKb, InlineBtn
from utils.text import f


async def get_other_telegram_connected_keyboard(lang: str):
    keyboard = InlineKb()

    keyboard.row(InlineBtn(await f("use other telegram button", lang), callback_data="use_other_account"))
    keyboard.row(InlineBtn(await f("connect this telegram button", lang), callback_data="connect_this_tg"))

    return keyboard
