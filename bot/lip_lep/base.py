import logging
from abc import abstractmethod, ABC
from contextlib import suppress

from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext
from aiogram.utils.exceptions import MessageError
from aiohttp import web

from psutils.func import check_function_spec

from db import DBSession
from db.models import ClientBot, User
from lip_lep.api.errors import ApiError

from utils.router import Router
from utils.text import f


class BaseEmailConfirmedHandler(ABC):
    path: str

    def __init__(self, dp: Dispatcher):
        self.dp = dp

    async def __call__(self, request: web.Request):
        try:
            result = await request.json()
        except Exception as e:
            logger = logging.getLogger()
            logger.error(e, exc_info=True)
            result = None

        if not result:
            return web.HTTPBadRequest()

        chat_id = result.get("chat_id")
        confirmed = result.get("confirmed")

        if not confirmed:
            raise web.HTTPBadRequest()

        with DBSession():
            bot = await ClientBot.get_lip_lep_bot()
            with ClientBot.with_id(bot.id):
                with self.dp.bot.with_token(bot.token):
                    state = self.dp.current_state(user=chat_id)

                    user = await User.get(chat_id)
                    lang = await user.get_lang()

                    message = await Router.get_state_message(state, no_error=True)

                    kwargs = {
                        "request": request,
                        "message": message,
                        "state": state,
                        "user": user,
                        "lang": lang,
                        "bot": bot,
                    }
                    kwargs.update(result)

                    safe_kwargs = check_function_spec(self.process, kwargs)
                    try:
                        result = await self.process(**safe_kwargs)
                    except ApiError as err:
                        text = await f(
                            "lip lep api error", lang,
                            code=err.code, detail=err.text,
                        )
                        await message.answer(text)
                    except Exception as e:
                        logger = logging.getLogger()
                        logger.error(e, exc_info=True)
                        await message.answer(await f("error", lang))
                    return result or web.Response()

    @abstractmethod
    async def process(self, **kwargs):
        raise NotImplementedError

    @classmethod
    def setup(cls, app: web.Application, dp: Dispatcher):
        handler = cls(dp)
        app.router.add_post(cls.path, handler)


class NextStateEmailConfirmedHandler(BaseEmailConfirmedHandler):
    state: str
    mode: str = "new"

    async def process(self, message: types.Message, state: FSMContext, chat_id: int, lang: str):
        text = await f("lip lep email confirmed message", lang)

        with suppress(MessageError):
            if self.mode in ("delete", "new"):
                await self.dp.bot.send_message(chat_id, text)
                if self.mode == "delete":
                    await message.delete()
            elif self.mode == "edit":
                await self.dp.bot.edit_message_text(text, chat_id, message.message_id)

        await state.set_state(self.state)
        await Router.state_menu(state=state, get_state_message=True, set_state_message=True, mode="new")

