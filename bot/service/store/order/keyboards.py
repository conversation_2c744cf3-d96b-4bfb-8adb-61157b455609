from aiogram import types

from config import CRM_HOST
from db import crud
from db.models import StoreOrder
from schemas import OrderShippingStatusEnum
from utils.redefined_classes import InlineBtn, InlineKb
from utils.text import c, f


async def add_store_order_actions_buttons(order: StoreOrder, keyboard: InlineKb, lang: str):
    order_status = order.status
    order_payment_method = order.payment_method
    order_status_pay = order.status_pay
    order_shipment_status = order.shipment_status

    if order_status == "open_unconfirmed":
        button_text = await f("confirm order button", lang)
        callback_data = c(
            "store_order_status",
            order_id=order.id,
            status='open_confirmed',
        )
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    if order_status.startswith("open"):
        button_text = await f("close order button", lang)
        callback_data = c("store_order_status", order_id=order.id, status='closed')
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

        button_text = await f("service cancel order button", lang)
        callback_data = c("store_order_status", order_id=order.id, status='canceled')
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    if order_payment_method != "online" and order_status not in (
            'open_unconfirmed', 'closed', 'canceled'
    ) and order_status_pay != OrderShippingStatusEnum.PAYED.value:
        button_text = await f("confirm pay order button", lang)
        callback_data = c("store_order_status", order_id=order.id, status='payed')
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    shipment = await crud.get_order_shipment(order.id)
    if shipment.base_type == "delivery" and order_shipment_status != OrderShippingStatusEnum.DELIVERED.value:
        button_text = await f("service delivery menu order button", lang)
        callback_data = c("del_status_mo", order_id=order.id, status=order_status)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    web_app = types.WebAppInfo(
        url=f"{CRM_HOST}/order/{order.id}?listType=inbox&itemIdField=orderId"
    )
    keyboard.row(InlineBtn(await f('manage store order button', lang), web_app=web_app))
