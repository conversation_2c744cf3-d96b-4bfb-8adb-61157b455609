from typing import Any, Literal, TypeAlias

from pydantic import BaseModel, Field, validator

CustomTextParamTypeLiteral: TypeAlias = Literal["vm"]

CustomTextsObjectTypeLiteral: TypeAlias = Literal["bot", "profile", "qrmenu"]


class BaseCustomTextFieldSchema(BaseModel):
    path: tuple[str, ...]
    display_text: str
    display_text_raw: str | None = Field(
        None, nullable=True,
        description="Display text, if specified different from field localisation value"
    )


class CustomTextStringTranslationSchema(BaseModel):
    value: str | None = Field(None, nullable=True)


class CustomTextStringSchema(BaseCustomTextFieldSchema):
    type: Literal["string"]
    is_button: bool
    value: str | None = Field(None, nullable=True)
    localisation_value: str | None = Field(None, nullable=True)
    default_value: str | None = Field(None, nullable=True)
    edit_middle_text_variable: str | None = Field(None, nullable=True)

    need_use_localisation: bool
    need_disable: bool
    need_enable: bool
    additional_params: dict[str, Any] | None = Field(None, nullable=True)

    is_default: bool

    translations: dict[str, CustomTextStringTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )

    @validator("value")
    def validate_value(cls, value: Any) -> Any:
        if value == "":
            return None
        return value


class UpdateCustomTextData(BaseModel):
    path: tuple[str, ...]
    value: str | None = Field(None, nullable=True)

    translations: dict[str, CustomTextStringTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )


class CustomTextDictSchema(BaseCustomTextFieldSchema):
    type: Literal["dict"]
    items: list["CustomTextSchema"]


CustomTextSchema: TypeAlias = CustomTextStringSchema | CustomTextDictSchema
CustomTextDictSchema.update_forward_refs()
