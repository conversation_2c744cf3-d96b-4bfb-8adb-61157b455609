from typing import Literal

from pydantic import BaseModel

from .base import AddressComponent


class AutocompleteParamsSchema(BaseModel):
    input: str
    components: str | None = None
    language: str | None = None
    location: str | None = None
    locationbias: str | None = None
    locationrestriction: str | None = None
    offset: int | None = None
    origin: str | None = None
    radius: float | None = None
    region: str | None = None
    sessiontoken: str
    strictbounds: bool | None = None
    types: str | None = None


class PlaceDetailsParamsSchema(BaseModel):
    place_id: str
    language: str | None = None
    region: str | None = None
    reviews_no_translations: bool | None = None
    reviews_sort: str | None = None
    sessiontoken: str
    fields: str | None = None


class MainTextMatchedSubstrings(BaseModel):
    length: int | None = None
    offset: int | None = None


class StructuredFormatting(BaseModel):
    main_text: str
    main_text_matched_substrings: list[MainTextMatchedSubstrings] | None = None
    secondary_text: str | None = None


class Terms(BaseModel):
    offset: int
    value: str


class Prediction(BaseModel):
    description: str
    matched_substrings: list[MainTextMatchedSubstrings] | None = None
    place_id: str
    reference: str
    structured_formatting: StructuredFormatting | None = None
    terms: list[Terms]
    types: list[str]


class Predictions(BaseModel):
    predictions: list[Prediction] | None = None
    status: Literal[
        "OK", "ZERO_RESULTS", "NOT_FOUND", "INVALID_REQUEST", "OVER_QUERY_LIMIT",
        "REQUEST_DENIED", "UNKNOWN_ERROR"]
    error_message: str | None = None


class PredictionsResponse(BaseModel):
    predictions: Predictions


class PlaceLatLng(BaseModel):
    lat: float
    lng: float


class PlaceBounds(BaseModel):
    northeast: PlaceLatLng
    southwest: PlaceLatLng


class PlaceGeometry(BaseModel):
    location: PlaceLatLng
    viewport: PlaceBounds


class Place(BaseModel):
    place_id: str | None = None
    geometry: PlaceGeometry
    formatted_address: str | None = None
    address_components: list[AddressComponent] | None = None


class PlaceDetails(BaseModel):
    html_attributions: list[str] | None = None
    result: Place | None = None
    status: Literal[
        "OK", "ZERO_RESULTS", "NOT_FOUND", "INVALID_REQUEST", "OVER_QUERY_LIMIT",
        "REQUEST_DENIED", "UNKNOWN_ERROR"]
    info_message: str | None = None
    error_message: str | None = None
