import enum

from pydantic import BaseModel

import schemas
from schemas import PaymentProviderItemSchema


class IncustSpecialAccountWithPaymentMethod(schemas.SpecialAccount):
    provider: schemas.BusinessPaymentMethodEnum


class PaymentMethodForSpecialAccount(BaseModel):
    """Метод оплати для спеціального рахунку Incust з відповідними даними провайдера"""
    provider: schemas.BusinessPaymentMethodEnum
    business_payment_data_id: int
    fields: list[schemas.PaymentProviderItemFieldSchema] = []
    is_enabled: bool = True


class SpecialAccountWithPaymentMethods(schemas.SpecialAccount):
    """Спеціальний рахунок Incust з методами оплати"""
    payment_methods: list[PaymentMethodForSpecialAccount] = []


class AdminSpecialsResponse(BaseModel):
    """Відповідь для методу get_specials містить список спеціальних рахунків з методами оплати"""
    specials: list[SpecialAccountWithPaymentMethods] | None = None


class ObjectPaymentSettingsTarget(enum.Enum):
    STORE = "store"
    INVOICE_TEMPLATE = "invoice_template"
