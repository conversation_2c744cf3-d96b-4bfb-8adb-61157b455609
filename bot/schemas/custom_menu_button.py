import enum
from datetime import datetime

from pydantic import BaseModel, Field


class CustomMenuButtonActionTypeEnum(enum.Enum):
    VM = "vm"


class AdminBotCustomMenuButtonTranslationSchema(BaseModel):
    text: str | None = Field(None, nullable=True)


class BaseAdminBotCustomMenuButtonSchema(BaseModel):
    text: str = Field(description="button text")
    action_type: CustomMenuButtonActionTypeEnum = Field(description="action type")
    position: int = Field(description="position")
    vm_id: int | None = Field(None, nullable=True, description="virtual manager ID")
    translations: dict[str, AdminBotCustomMenuButtonTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )


class AdminBotCustomMenuButtonSchema(BaseAdminBotCustomMenuButtonSchema):
    id: int = Field(description="custom menu button ID")
    time_created: datetime = Field(description="time created")


class SetAdminBotCustomMenuButtonSchema(BaseAdminBotCustomMenuButtonSchema):
    id: int | str = Field(description="custom menu button ID")
