from typing import Any

import stripe
from openai import BaseModel
from pydantic import Field


class BillingPaymentMethodSchema(BaseModel):
    id: str
    type: str
    display_name: str
    billing_details: stripe.PaymentMethod.BillingDetails
    card_brand: str | None = Field(None, nullable=True)
    last4: str | None = Field(None, nullable=True)
    exp_year: int | None = Field(None, nullable=True)
    exp_month: int | None = Field(None, nullable=True)
    data: dict[str, Any]
    is_detached: bool
