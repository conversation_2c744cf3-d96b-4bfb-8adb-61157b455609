from decimal import Decimal

from pydantic import BaseModel, Field

from .product import BillingProductCode
from .service_packet_item import (
    BillingRecurringInterval, BillingScheme, BillingTierSchema, BillingTiersMode,
    BillingTransformQuantity,
    BillingUsageType,
)
from .. import BaseORMModel


class BillingSubscriptionItemSchema(BaseORMModel):
    id: int
    name: str
    description: str
    product_code: BillingProductCode
    stripe_product_id: str

    packet_item_id: int

    billing_scheme: BillingScheme
    tiers_mode: BillingTiersMode | None = Field(None, nullable=True)
    tiers: list[BillingTierSchema] | None = Field(None, nullable=True)

    unit_amount: Decimal
    quantity: int
    quantity_adjustable: bool
    min_quantity: int
    max_quantity: int

    recurring_interval: BillingRecurringInterval
    recurring_interval_count: int

    usage_type: BillingUsageType

    transform_quantity: BillingTransformQuantity | None = Field(nullable=True)

    used_quantity: int

    amount: Decimal


class UpdateBillingSubscriptionItemData(BaseModel):
    quantity: int
