from dataclasses import dataclass
from typing import Annotated

from fastapi import File, Form, UploadFile
from pydantic import BaseModel, Field

from core.ext.types import ExternalTypeLiteral
from .payments import CreateAdminStorePaymentData, PaymentSettingsExtraDataSchema
from ..base import BaseORMModel
from ..store.store import (
    BannerSchema, BillingSettings, StoreCustomFieldSchema,
    WorkingDaySchema,
)


class AdminStoreListSchema(BaseORMModel):
    id: int
    profile_id: int
    name: str
    currency: str

    is_enabled: bool

    read_allowed: bool = False
    edit_allowed: bool = False

    image_url: str | None = Field(None, nullable=True)

    banners_count: int = 0


class AdminStoreTranslationSchema(BaseModel):
    name: str | None = Field(None, nullable=True)
    description: str | None = Field(None, nullable=True)


class BaseStoreFields(BaseModel):
    description: str | None = Field(
        default=None,
        nullable=True,
        max_length=1024,
    )
    ai_description: str | None = Field(
        default=None,
        nullable=True,
        max_length=4096,
    )

    latitude: str | None = Field(None, nullable=True)
    longitude: str | None = Field(None, nullable=True)

    external_id: str | None = Field(None, nullable=True)
    external_type: ExternalTypeLiteral | None = Field(None, nullable=True)


class AdminStoreSchema(AdminStoreListSchema, BaseStoreFields):
    custom_fields: list[StoreCustomFieldSchema] = Field(default_factory=list)
    banners: list[BannerSchema] | None = Field(None, nullable=True)

    billing_settings: BillingSettings | None = Field(None, nullable=True)

    working_days: list[WorkingDaySchema] | None = Field(None, nullable=True)
    is_enabled_emenu: bool = Field(default=False)

    translations: dict[str, AdminStoreTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )
    task_id: int | None = None


class CreateAdminStoreSchema(BaseModel):
    store: AdminStoreSchema
    extra_data: PaymentSettingsExtraDataSchema | None = Field(None, nullable=True)


class AdminUpdateStoreData(BaseStoreFields):
    is_enabled: bool | None = None

    name: str | None = Field(
        default=None,
        max_length=255,
    )
    currency: str | None = Field(
        default=None,
        max_length=3,
    )
    is_enabled_emenu: bool = Field(
        default=False,
    )

    custom_fields: dict[str, str | None] = Field(
        default=None,
        description=(
            "Custom fields to be updated."
            "Specified fields will be updated."
            "(specify null to delete field)"
        )
    )

    translations: dict[str, AdminStoreTranslationSchema | None] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations to be updated."
            "The key is language ISO code and "
            "the value is fields translations object."
            "(specify null as value to delete translation or "
            "null as field value to delete field translation,"
            "if auto translations enabled will be automatically translated after "
            "deleting)"
        )
    )

    client_web_pages: list[int] | None = Field(
        None, nullable=True,
        description="List of client web pages ids to be associated with the store."
    )


@dataclass
class AdminStoreCreateBanner:
    name: str | None = Field(None, nullable=True)
    url: str | None = Field(None, nullable=True)
    position: int | None = Field(None)
    is_visible: bool | None = Field(None)
    media_id: int | None = Field(None)


@dataclass
class AdminStoreCreateBannerForm:
    name: str | None = Form(None, nullable=True)
    url: str | None = Form(None, nullable=True)
    position: int | None = Form(None)
    is_visible: bool | None = Form(None)
    file: UploadFile = File()


class AdminCreateStoreData(BaseModel):
    name: str
    currency: str
    is_enabled: bool = True
    description: str | None = None

    banners: list[AdminStoreCreateBanner] | None = Field(
        None, nullable=True,
        description="List of banners to be associated with the store."
    )
    client_web_pages: list[int] | None = Field(
        None, nullable=True,
        description="List of client web pages ids to be associated with the store."
    )
    # image: Annotated[UploadFile, None] = Field(None, nullable=True)
    media_id: Annotated[int, None] = Field(None, nullable=True)
    payment_data: list[CreateAdminStorePaymentData] | None = Field(None, nullable=True)
