from fastapi import Query
from pydantic import Field
from pydantic.dataclasses import dataclass

from ..base import BaseORMModel


class AdminConnectedObjectSchema(BaseORMModel):
    id: int
    name: str
    read_allowed: bool = False
    edit_allowed: bool = False


class AdminConnectedAttributesSchema(AdminConnectedObjectSchema):
    selected_by_default: bool | None = Field(None, nullable=True)
    price_impact: int | None = Field(None, nullable=True)


@dataclass
class AdminListParams:
    offset: int | None = Query(None, nullable=True)
    limit: int = Query(10)
    search_text: str | None = Query(None, nullable=True)
    store_id: int | None = Query(None, nullable=True)
