from dataclasses import dataclass
from typing import Annotated, Dict, List, Optional, Union

from fastapi import File, Form, Query, UploadFile
from pydantic import BaseModel, Field, validator

from core.ext.types import ExternalTypeLiteral, SelectMode
from schemas.payment_settings.liqpay import UnitName
from schemas.payment_settings.liqpay.schemas import TaxList
from .base import AdminConnectedObjectSchema
from ..base import BaseORMModel
from ..gallery import Gallery
from ..store.product import ProductTypeLiteral


class BaseAdminProductSchema(BaseORMModel):
    type: ProductTypeLiteral
    is_enabled: bool
    product_id: str | None = Field(None, nullable=True)
    product_group_id: int | None = Field(None, nullable=True)

    external_id: str | None = Field(None, nullable=True)
    external_type: ExternalTypeLiteral | None = Field(None, nullable=True)

    raw_internal_name: str | None = Field(None, nullable=True)

    name: str
    is_available: bool = True

    price: float
    old_price: float | None = Field(None, nullable=True)

    is_weight: bool | None = False
    weight_unit: str | None = Field(None, nullable=True)

    description: str | None = Field(None, nullable=True)

    buy_min_quantity: int = 1

    floating_sum_enabled: bool = False
    floating_sum_min: float = 0
    floating_sum_max: float = 0
    floating_sum_options: list[float | int] | None = Field(None, nullable=True)
    floating_sum_options_str: str | None = Field(None, nullable=True)
    floating_sum_user_sum_enabled: bool = True

    pti_info_text: str | None = Field(None, nullable=True)
    pti_info_link: str | None = Field(None, nullable=True)

    liqpay_id: str | None = Field(None, nullable=True)
    liqpay_unit_name: UnitName | None = Field(None, nullable=True)
    liqpay_codifier: str | None = Field(None, nullable=True)
    liqpay_tax_list: TaxList | None = Field(None, nullable=True)

    need_auth: bool = False
    floating_qty_enabled: bool = False

    topup_server_api_url: str | None = Field(None, nullable=True)
    topup_terminal_api_key: str | None = Field(None, nullable=True)
    topup_account_id: str | None = Field(None, nullable=True)
    topup_enabled_card: bool | None = Field(None, nullable=True)

    incust_terminal_id: str | None = Field(None, nullable=True)


class AdminProductListSchema(BaseAdminProductSchema):
    id: int
    read_allowed: bool = False
    edit_allowed: bool = False

    internal_name: str | None = Field(None, nullable=True)

    profile_id: int | None = None

    image_url: str | None = Field(None, nullable=True)
    image_media_id: int | None = Field(None, nullable=True)
    thumbnail_url: str | None = Field(None, nullable=True)
    position: int | None = Field(None, nullable=True)
    categories_str: str | None = None


class AdminProductTranslationSchema(BaseModel):
    name: str | None = Field(None, nullable=True)
    description: str | None = Field(None, nullable=True)
    pti_info_text: str | None = Field(None, nullable=True)


class AdminProductSchema(AdminProductListSchema):
    gallery: Gallery | None = Field(None, nullable=True)

    stores: list[AdminConnectedObjectSchema] = Field(
        default_factory=list,
        description="List of connected stores. Here will be only stores current user "
                    "has access to"
    )

    categories: list[AdminConnectedObjectSchema] = Field(
        default_factory=list,
        description="List of connected categories. Here will be all product categories"
    )

    attribute_groups: list[AdminConnectedObjectSchema] = Field(
        default_factory=list,
        description="List of connected attribute groups. Here will be all product "
                    "attribute groups"
    )

    translations: dict[str, AdminProductTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )

    task_id: int | None = None
    media_id: int | None = None

    incust_terminal_id: str | None = Field(None, nullable=True)


class AdminBaseProductModifyData(BaseAdminProductSchema):
    image_url: str | None = Field(
        default=None,
        nullable=True,
        description="Image url. Image will be downloaded and saved to 7loc server and "
                    "attached to product"
    )
    media_id: str | None = Field(
        default=None,
        nullable=True,
        alias="image_media_id",
        description="Id of Media previously uploaded to 7loc server with POST /{"
                    "profile_id}/storage endpoint"
    )


class AminProductProductsGroupWithModifiersSchema(BaseORMModel):
    modifier_id: int | None = None
    modifier_value: str | None = None


class AdminCharacteristicValueTranslationSchema(BaseModel):
    value: str | None = Field(None, nullable=True)


class AdminConnectedCharacteristicToProductBaseSchema(BaseModel):
    characteristic_id: int = Field(
        description="Characteristic id. Access to store:edit action is required"
    )
    value: str = Field(
        description="Characteristic value for product in store",
        max_length=100
    )


class AdminCharacteristicToProductFormDataSchema(
    AdminConnectedCharacteristicToProductBaseSchema
):
    translations: Annotated[Optional[str | dict | None], Form(nullable=True)] = None


class AdminConnectCharacteristicToProductData(
    AdminConnectedCharacteristicToProductBaseSchema
):

    translations: dict[str, AdminCharacteristicValueTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )


class AdminCreateProductData(AdminBaseProductModifyData):
    is_enabled: bool = True
    price: float = 0

    stores: list[int] | None = Field(
        default=None,
        nullable=True,
        description="list of store ids to connect product. Access to store:edit "
                    "action is required"
    )

    categories: list[int] | None = Field(
        default=None,
        nullable=True,
        description="list of category ids to connect product. Access to category:read "
                    "action is required"
    )

    translations: dict[str, dict | None] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations to be updated."
            "The key is language ISO code and "
            "the value is fields translations object."
            "(specify null as value to delete translation or "
            "null as field value to delete field translation,"
            "if auto translations enabled will be automatically translated after "
            "deleting)"
        )
    )

    floating_sum_options_str: str | None = Field(None, nullable=True)

    products_group_modifiers: list[
                                  AminProductProductsGroupWithModifiersSchema] | None \
        = Field(
        None, nullable=True
    )


class AdminConnectStoreToProductData(BaseModel):
    store_id: int = Field(
        description="Store id. Access to store:edit action is required"
    )
    price: float = Field(
        description="Specific price for product in store"
    )
    old_price: float | None = Field(
        default=None,
        nullable=True,
        description="Specific old price for product in store."
    )


class AdminUpdateProductData(AdminBaseProductModifyData):
    type: ProductTypeLiteral | None = None
    is_enabled: bool | None = None
    name: str | None = None

    price: float | int | None = None
    old_price: float | int | None = Field(None, nullable=True)

    image_url: str | None = Field(
        default=None,
        nullable=True,
        description="Image url. Image will be downloaded and saved to 7loc server and "
                    "attached to product"
    )
    media_id: str | None = Field(
        default=None,
        nullable=True,
        alias="image_media_id",
        description="Id of Media previously uploaded to 7loc server with POST /{"
                    "profile_id}/storage endpoint"
    )

    buy_min_quantity: int | None = Field(default=None, nullable=True)

    is_weight: bool | None = None
    weight_unit: str | None = Field(None, nullable=True)

    translations: dict[str, AdminProductTranslationSchema | None] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations to be updated."
            "The key is language ISO code and "
            "the value is fields translations object."
            "(specify null as value to delete translation or "
            "null as field value to delete field translation,"
            "if auto translations enabled will be automatically translated after "
            "deleting)"
        )
    )

    need_update_stores: bool | None = Field(
        default=False,
        nullable=True,
        description="Update stores data. Access to store:edit action is required"
    )
    stores: list[AdminConnectStoreToProductData] | None = Field(
        default=None,
        nullable=True,
        description=(
            "List of stores data to connect. "
            "Access to store:edit action is required"
        ),
    )

    floating_sum_options_str: str | None = Field(None, nullable=True)

    @validator('raw_internal_name', pre=True, always=True)
    def convert_empty_string_to_none(cls, v):
        if v == '':
            return None
        return v

    incust_terminal_id: str | None = Field(None, nullable=True)


class AdminConnectProductsToObjectData(BaseModel):
    products: list[int] = Field(
        min_items=1,
        description="List of product ids to connect. Access to product:read action is "
                    "required",
    )
    replace: bool = Field(
        default=False,
        description="Is new list replaces current list"
    )


class AdminProductsConnectedToObjectResult(BaseModel):
    replaced: bool = False
    connected_products: list[AdminProductListSchema]


class AdminDisconnectProductsData(BaseModel):
    products: list[int] | None = Field(
        default=None,
        nullable=True,
        min_items=1,
        description=(
            "List of product ids to disconnect. "
            "Ignored if disconnect_all is specified"
        ),
    )
    disconnect_all: bool = False


class AdminProductsDisconnectedResult(BaseModel):
    is_all_deleted: bool = False
    products_ids_disconnected: list[int] | None = Field(None, nullable=True)


class AdminAddCategoriesToProductData(BaseModel):
    categories: list[int] = Field(
        min_items=1,
        description="List of category ids to add to product. Access to category:edit "
                    "action is required",
    )
    replace: bool = Field(
        default=False,
        description=(
            "Replace current category list. "
            "Access to category:edit action for all current categories is required"
        )
    )


class AdminDeleteCategoriesFromProductData(BaseModel):
    categories: list[int] | None = Field(
        default=None,
        nullable=True,
        min_items=1,
        description=(
            "List of category ids to delete from product. "
            "Access to category:edit action is required."
            "Ignored if disconnect_all is specified"
        ),
    )
    delete_all: bool = False


class AdminConnectStoresToProductData(BaseModel):
    stores: list[AdminConnectStoreToProductData] = Field(
        description=(
            "List of stores data to connect. "
            "Access to store:edit action is required"
        ),
    )
    replace: bool = Field(
        default=False,
        description=(
            "Is new list replaces current list. "
            "Only stores with store:edit access will be replaced. "
        )
    )


class AdminConnectCharacteristicsToProductData(BaseModel):
    characteristics: list[AdminConnectCharacteristicToProductData] = Field(
        description=(
            "List of characteristics with value data to connect. "
            "Access to store:edit action is required"
        ),
    )
    replace: bool = Field(
        default=False,
        description=(
            "Is new list replaces current list. "
            "Only stores with store:edit access will be replaced. "
        )
    )


class AdminConnectCategoriesToProductData(BaseModel):
    categories: list[int] = Field(
        description=(
            "List of category ids to add to product. "
            "Access to category:edit action is required."
        )
    )
    replace: bool = Field(
        default=False,
        description=(
            "Is new list replaces current list. "
            "Only categories with category:edit access will be replaced. "
        )
    )


class AdminProductStoreData(BaseORMModel):
    store_id: int
    store_name: str
    store_image_url: str | None = None

    price: float
    old_price: float | None = Field(None, nullable=True)

    read_allowed: bool
    edit_allowed: bool


class AdminProductModifierValueSchema(BaseORMModel):
    modifier_value_id: int
    modifier_value: str
    modifier_id: int
    modifier_name: str
    translations: dict[str, AdminCharacteristicValueTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )


class AdminProductForProductGroupSchema(BaseORMModel):
    id: int
    name: str
    position: int
    internal_name: str
    product_id: str
    modifiers: list[AdminProductModifierValueSchema] | None = None
    read_allowed: bool
    edit_allowed: bool


class AdminProductForAddInProductGroupSchema(BaseORMModel):
    id: int
    name: str
    internal_name: str
    logo_url: str | None = None
    read_allowed: bool
    edit_allowed: bool


@dataclass
class AdminProductGalleryItemSchema:
    position: int | None = Form(None)
    file: UploadFile | None = File(None)


class AdminValidateObjectIdSchema(BaseModel):
    result: bool
    detail: str | None = None


class AdminProductGalleryImagesPositionSchema(BaseORMModel):
    images_ids: list[int] | None = None


class AdminProductGalleryItemForCreate(BaseORMModel):
    media_id: int
    position: int | None = None


class AdminProductGalleryFilesForCreate(BaseORMModel):
    image: Annotated[UploadFile | str, None] = Form(None, nullable=True)
    position: int | None = None


class AdminProductCreateFormSchema(BaseModel):
    name: Annotated[str, Form()]
    type: Annotated[ProductTypeLiteral, Form()]
    image: Annotated[UploadFile | str, None] = Form(None, nullable=True)

    is_available: Annotated[bool, Form()] = True

    price: Annotated[float, Form()]
    old_price: Annotated[Optional[float | None], Form(nullable=True)] = None

    product_id: Annotated[str | None, Form(nullable=True)] = None
    product_group_id: Annotated[Optional[int | None], Form(nullable=True)] = None

    external_id: Annotated[Optional[str | None], Form(nullable=True)] = None
    external_type: Annotated[
        Optional[ExternalTypeLiteral | None], Form(nullable=True)] = None

    is_weight: Annotated[Optional[bool | None], Form(nullable=True)] = False
    weight_unit: Annotated[Optional[str | None], Form(nullable=True)] = None

    description: Annotated[Optional[str | None], Form(nullable=True)] = None

    buy_min_quantity: Annotated[Optional[int], Form(nullable=True)] = 1

    floating_sum_enabled: Annotated[bool, Form()] = False
    floating_sum_min: Annotated[Optional[float | None], Form(nullable=True)] = None
    floating_sum_max: Annotated[Optional[float | None], Form(nullable=True)] = None
    floating_sum_options: Annotated[List[Union[float, int]], Form()] = None
    floating_sum_options_str: Annotated[
        Optional[str | None], Form(nullable=True)] = None
    floating_sum_user_sum_enabled: Annotated[bool, Form()] = True

    pti_info_text: Annotated[Optional[str | None], Form(nullable=True)] = None
    pti_info_link: Annotated[Optional[str | None], Form(nullable=True)] = None

    liqpay_id: Annotated[Optional[str | None], Form(nullable=True)] = None
    liqpay_unit_name: Annotated[UnitName, Form()] = None
    liqpay_codifier: Annotated[Optional[str | None], Form(nullable=True)] = None
    liqpay_tax_list: Annotated[TaxList, Form()] = None

    need_auth: Annotated[bool, Form()] = False
    floating_qty_enabled: Annotated[bool, Form()] = False

    stores: Annotated[List[AdminConnectStoreToProductData], Form(nullable=True)] = None

    categories: Annotated[List[int], Form(nullable=True)] = None

    image_url: Annotated[Optional[str | None], Form(nullable=True)] = None
    media_id: Annotated[Optional[str | None], Form(nullable=True)] = None

    gallery_media_items: list[AdminProductGalleryItemForCreate] | None = None
    gallery_files: list[AdminProductGalleryFilesForCreate] | None = None

    products_group_modifiers: list[
                                  AminProductProductsGroupWithModifiersSchema] | None \
        = None

    characteristics: list[AdminCharacteristicToProductFormDataSchema] | None = None

    attributes_groups: list[int] | None = None

    translations: Annotated[Optional[str | dict | None], Form(nullable=True)] = None

    raw_internal_name: Annotated[Optional[str | None], Form(nullable=True)] = None

    @validator('raw_internal_name', pre=True, always=True)
    def convert_empty_string_to_none(cls, v):
        if v == '':
            return None
        return v

    class Config:
        orm_mode = True
        arbitrary_types_allowed = True


class AdminProductAddToStoreSchema(BaseModel):
    id: int
    price: float
    old_price: float | None = None


class AdminProductsBaseMassActionSchema(BaseModel):
    included: list[int]
    excluded: list[int]


class AdminProductsAddToStorePricesSchema(AdminProductsBaseMassActionSchema):
    prices: Dict[int, Dict[str, float | int]] | None = None


class AdminProductsMassChangeStatusSchema(AdminProductsBaseMassActionSchema):
    status: bool


class AdminProductsMassChangeActiveStatusSchema(AdminProductsBaseMassActionSchema):
    status: bool


class AdminProductBasicInfoSchema(AdminProductAddToStoreSchema):
    name: str


class ProductsActionsQueryParams(BaseModel):
    store_ids: Optional[List[int]] = Query(
        None, description="List of store IDs to filter products"
    )
    category_ids: Optional[List[int]] = Query(
        None, description="List of category IDs to filter products"
    )
    search_text: Optional[str] = Query(
        None, description="Search text for filtering products by name or description"
    )
    product_type: Optional[str] = Query(
        None, description="Product type"
    )
    without_categories: bool = Query(
        False, description="Return products without linked category"
    )
    without_stores: bool = Query(
        False, description="Return products without linked store"
    )
    mode: SelectMode = Query(
        SelectMode.SELECTED, description="Select mode for filtering products"
    )
