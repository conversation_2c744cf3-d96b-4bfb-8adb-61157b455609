from enum import Enum, unique
from fastapi import Form
from pydantic import BaseModel, Field
from pydantic.dataclasses import dataclass
from typing import Annotated, Literal, TypeAlias

from schemas.base import BaseORMModel, EnumWithValues, ExtraFeeSchema
from ..ewallet.ewallet import EwalletSchema
from ..incust.incust_pay import IncustPayPaymentData
from ..payment_settings.schemas import PaymentValues


class NewPrePaymentData(BaseModel):
    payment_settings_id: int
    object_payment_settings_id: Annotated[int | None, Field(nullable=True)] = None
    comment: Annotated[str | None, Field(nullable=True)] = None
    skip_process_order_payment: bool = False


class MakePaymentData(NewPrePaymentData):
    is_webview: bool = False
    args: list[PaymentValues | None] | None = None
    user_phone: str | None = None


@dataclass
class PaymentLiqPayCallBackData:
    data: str = Form(...)
    signature: str = Form(...)


class PaymentCheckOutData(BaseModel):
    url: str | None = None
    payment_id: int | None = None


class PaymentCheckOutDataBase(BaseModel):
    data: PaymentCheckOutData | None
    description: str | None = None
    error: str | None = None
    is_qr: bool | None = False
    is_external: bool | None = False
    is_email_required: bool | None = False
    args: list[PaymentValues | None] | None = None
    is_make_on_click: bool | None = False


class FondyPaymentData(BaseModel):
    merchant_id: int
    amount: int
    currency: str

    orderId: str | None = None
    order_desc: str | None = None

    merchant_data: str | None = None
    product_id: str | None = None
    sender_email: str | None = None
    lang: str | None = None

    required_rectoken: Literal["Y", "N"] = "N"
    rectoken: str | None = None
    receiver_rectoken: str | None = None

    subscription: Literal["Y", "N"] = "N"
    subscription_callback_url: str | None = None

    response_url: str | None = None
    server_callback_url: str | None = None

    payment_systems: str | None = None
    payment_method: str | None = None
    default_payment_system: str | None = None

    preauth: Literal["Y", "N"] = "N"
    delayed: Literal["Y", "N"] = "Y"
    verification: Literal["Y", "N"] = "N"
    verification_type: Literal["amount", "code"] = "amount"

    lifetime: int | None = None
    design_id: int | None = None


class FondyCheckOutResponseData(BaseModel):
    response_status: str

    checkout_url: str | None = None
    payment_id: int | None = None

    error_code: int | None = None
    error_message: str | None = None


class FondyCheckOutData(PaymentCheckOutDataBase):
    payment_id: int | None


class EpayTokenResponse(BaseModel):
    access_token: str
    expires_in: int
    refresh_token: str = ""

    scope: str
    token_type: str


class EpayDataResponse(BaseModel):
    auth: EpayTokenResponse | None
    invoiceId: str
    invoiceIdAlt: str | None = None

    amount: int
    currency: str


class EpayBasePaymentData(BaseModel):
    terminal: str
    amount: int
    currency: str

    invoiceId: str
    invoiceIdAlt: str | None = None

    language: str
    description: str
    accountId: str | None = None


class EpayPaymentData(EpayBasePaymentData):
    backLink: str
    failureBackLink: str | None = None

    postLink: str
    failurePostLink: str | None = None

    name: str | None = None
    email: str | None = None
    phone: str | None = None

    cardSave: bool = False
    auth: EpayTokenResponse
    data: str | None = None


class EpayCheckoutData(PaymentCheckOutDataBase):
    data: EpayPaymentData


class EpayPaymentResponse(EpayBasePaymentData):
    id: str
    dateTime: str

    cardMask: str
    cardType: str
    cardId: str | None = None
    issuer: str

    reference: str | None = None
    secure: Literal["yes", "no"]
    tokenRecipient: str | None = None

    code: Literal["ok", "error"]
    reason: str | None = None
    reasonCode: int = 0

    name: str | None = None
    email: str | None = None
    phone: str | None = None

    ip: str | None = None
    ipCountry: str | None = None
    ipCity: str | None = None
    ipRegion: str | None = None
    ipDistrict: str | None = None
    ipLongitude: float | None = None
    ipLatitude: float | None = None


class EpayWebhook(BaseModel):
    data: EpayPaymentResponse


class FreedompayPaymentData(BaseModel):
    pg_merchant_id: int

    pg_order_id: str
    pg_amount: float
    pg_currency: str
    pg_description: str

    pg_salt: str
    pg_sig: str | None = None

    pg_check_url: str = ""
    pg_result_url: str
    pg_request_method: Literal["GET", "POST", "XML"] = "POST"
    pg_success_url: str
    pg_failure_url: str
    pg_success_url_method: Literal["GET", "POST"] = "POST"
    pg_failure_url_method: Literal["GET", "POST"] = "POST"

    pg_state_url: str | None = None
    pg_state_url_method: Literal["GET", "POST", "AUTOGET", "AUTOPOST"] = "AUTOPOST"
    pg_site_url: str | None = None

    pg_payment_system: str | None = None
    pg_lifetime: int = 86400

    pg_user_phone_text: str | None = None
    pg_user_contact_email: str | None = None
    pg_user_ip: str | None = None

    pg_postpone_payment: bool = False
    pg_language: Literal["en", "ru"]
    pg_testing_mode: bool = False
    pg_user_id: str | None = None
    pg_recurring_start: bool = False
    pg_recurring_lifetime: int = 1

    pg_receipt_positions: dict | None = None

    payment_uuid_id: str | None = None

    pg_auto_clearing: bool = False
    pg_payment_method: Literal[
        "wallet", "internetbank", "other", "bankcard", "cash", "mobile_commerce"] = \
        "bankcard"
    pg_timeout_after_payment: int = 10
    pg_generate_qr: bool = False
    pg_3ds_challenge: bool = False
    pg_commission_discount: bool = False
    pg_commission_discount_fix: float | None = None
    pg_commission_discount_percentage: float | None = None

    pg_idempotency_key: str | None = None


class LiqPayPaymentData(BaseModel):
    data: str | None = None
    signature: str | None = None


class LiqPayCheckOutData(PaymentCheckOutDataBase):
    data: LiqPayPaymentData | None


class OrangePaymentData(BaseModel):
    orange_money_url: str | None = None
    orange_maxit_url: str | None = None


class OrangeCheckOutData(PaymentCheckOutDataBase):
    data: OrangePaymentData | None


class KPayPaymentData(BaseModel):
    payment_id: int | None = None
    qrcode: str | None = None


class KPayCheckOutData(PaymentCheckOutDataBase):
    data: KPayPaymentData | None


class CheckoutPayBaseSchema(BaseModel):
    amount_to_pay: float


class LiqpayCheckoutPaySchema(CheckoutPayBaseSchema):
    type: Literal["liqpay"]
    data: LiqPayCheckOutData


class OrangeCheckoutPaySchema(CheckoutPayBaseSchema):
    type: Literal["orange"]
    data: OrangeCheckOutData


class KPayCheckoutPaySchema(CheckoutPayBaseSchema):
    type: Literal["kpay"]
    data: KPayCheckOutData


class FondyCheckoutPaySchema(CheckoutPayBaseSchema):
    type: Literal["fondy"]
    data: FondyCheckOutData


class EPayCheckoutPaySchema(CheckoutPayBaseSchema):
    type: Literal["epay"]
    data: EpayCheckoutData


class IncustPayCheckoutPaySchema(CheckoutPayBaseSchema):
    type: Literal["incust_pay"]
    data: IncustPayPaymentData


class BasicCheckoutPaySchema(CheckoutPayBaseSchema):
    type: Literal[
        "stripe", "unipos", "flutterwave", "wave", "pl24", "tpay", "freedompay",
        "comsa", "momo", "directpay", "tj", "airtel", "ewallet", "tg_pay"]
    data: PaymentCheckOutDataBase


PaymentCheckoutProviderData: TypeAlias = (LiqpayCheckoutPaySchema |
                                          BasicCheckoutPaySchema |
                                          OrangeCheckoutPaySchema |
                                          KPayCheckoutPaySchema |
                                          FondyCheckoutPaySchema |
                                          EPayCheckoutPaySchema |
                                          IncustPayCheckoutPaySchema)


class CalculatedExtraFees(BaseModel):
    total_extra_fee: int
    journal_entries: list[ExtraFeeSchema]


PaymentCommentLiteral = Literal["required", "optional", "disabled"]


class PaymentMethodSchema(BaseModel):
    provider: str
    is_online: bool
    settings_id: int
    object_settings_id: int | None = Field(None, nullable=True)
    need_comment: PaymentCommentLiteral

    logo: str | None = None
    fee_value: float | int | None = None
    fee_percent: float | int | None = None
    name: str | None = None
    default_name: str | None = None
    desc: str | None = None
    label_comment: str | None = None
    incust_pay_data: IncustPayPaymentData | None = None

    ewallet: Annotated[EwalletSchema | None, Field(nullable=True)] = None


class AvailablePaymentSchema(BaseORMModel):
    available_telegram: bool = False
    is_multiple_payment_methods: bool
    single_payment_method: PaymentMethodSchema | None = None
    qr_pmt_methods: set[str] = Field(default_factory=set)
    email_required: set[str] = Field(default_factory=set)
    is_epay_test_mode: bool = False
    methods: list[PaymentMethodSchema]


class CustomPaymentSchema(BaseModel):
    id: int
    name: str
    description: str | None
    icon_url: str | None
    price: float

    need_comment: bool
    label_comment: str

    is_enabled: bool

    info: str


class CustomPaymentMethodData(BaseModel):
    id: int
    comment: str | None = None


@unique
class CheckoutEventTypes(EnumWithValues):
    completed = 'completed'
    expired = 'expired'
    failed = 'failed'


class BusinessPaymentStatusEnum(Enum):
    SUCCESS = "success"
    FAILED = "failed"


class BusinessPaymentResult(BaseModel):
    status: BusinessPaymentStatusEnum
    error: str | None = None
    detail: dict | None = None

    def dict(self, **kwargs):
        d = super().dict(**kwargs)
        d["status"] = d["status"].value  # Перетворюємо Enum у строку
        return d
