from typing import Literal
from pydantic import BaseModel

GetCustomerAccountsScope = Literal["retail", "corporate", "all"]

CustomerChargeOrWriteOffBenefitsType = Literal["regular-bonuses", "promotional-bonuses", "special-account"]


class CustomerChargeOrWriteOffBenefitsSchemaBase(BaseModel):
    amount: float
    comment: str | None = None
    currency: str | None = None
    id: str
    id_type: str | None = None
    special_account_id: str | None = None
    type: CustomerChargeOrWriteOffBenefitsType


class CustomerChargeBenefitsSchema(CustomerChargeOrWriteOffBenefitsSchemaBase):
    promotional_bonuses_expire_date: str | None = None
    promotional_bonuses_starting_date: str | None = None


class CustomerWriteOffBenefitsSchema(CustomerChargeOrWriteOffBenefitsSchemaBase):
    ...
