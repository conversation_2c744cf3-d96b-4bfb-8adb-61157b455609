import io
from email.mime.base import MIMEBase
from enum import unique
from typing import Literal, Optional

from pydantic import BaseModel

from schemas.base import EnumWithValues
from incust_client_api_client.models.card_info import CardInfo
from .loyalty import CouponBatch, InCustCouponSchema
from ..templater import BaseTemplateSchema


class IncustErrorOrBaseSchema(BaseModel):
    code: int | None = None
    message: str | None = None
    description: str | None = None


class GenerateTemporaryCodeResponseSchema(BaseModel):
    code: str
    redeem_allowed: bool


class SetChatBotTokenSchema(BaseModel):
    channel: Literal['facebook', 'telegram', 'viber', 'wawebproxy', 'wabcloud']
    token: str


class CancelTransactionSchema(BaseModel):
    transaction_id: str
    comment: Optional[str | None] = None


class FinalizeCheckSchema(BaseModel):
    id: str
    comment: Optional[str | None] = None


class TrustedRegisterSchema(BaseModel):
    system: str
    create: bool
    token: str


class LanguageResponseSchema(BaseModel):
    language: str


class ReqIncustProfileFields(BaseModel):
    name: str | None = None
    birth_date: str | None = None
    gender: str | None = None


class ShowIncustQrCardSchema(BaseModel):
    class Config:
        arbitrary_types_allowed = True

    identifier: str | None = None
    photo: io.BytesIO | None = None
    temp_code: str | None = None
    text: str | None = None


@unique
class IncustRedeemedTypes(EnumWithValues):
    at_order: str = 'at_order'
    pre_auth: str = 'pre_auth'
    reserve: str = 'reserve'


class CouponCode(BaseModel):
    code: str | None
    error: str | None
    valid: bool | None


class CouponAddedResult(BaseModel):
    codes: list[CouponCode] | None
    coupons: list[InCustCouponSchema] | None
    message: str | None


class CouponShowDataBase(BaseModel):
    title: str | None
    text: str | None
    coupon_link: str | None
    url: str | None
    image: str | None
    pdf_url: str | None
    code: str | None
    coupon_id: str | None


class CouponShowData(CouponShowDataBase):
    pdf: MIMEBase | None = None
    pdf_direct_url: str | None = None
    coupon_id: str | None = None
    share_allowed: int | None = 0
    type: Literal['check-modifier', 'certificate', 'external'] | None
    valid: bool | None = None
    locked: bool | None = None
    error: str | None = None
    description: str | None

    class Config:
        arbitrary_types_allowed = True


class Info(BaseModel):
    channel: str
    code: str
    length: int
    recipient: str


class IncustPayConfirmResponse(BaseModel):
    code: int
    info: Info
    message: str


class IncustResultResponse(BaseModel):
    code: int | None = None
    description: str | None = None
    message: str | None = None


class CouponAddedResultUser(BaseModel):
    code: int | None = None
    coupons: list[str] | None
    vouchers: list[CouponShowDataBase] | None = None
    message: str | None = None


class IncustData(BaseModel):
    white_label_id: str | None = None
    loyalty_id: str | None = None
    server_api: str | None = None
    client_url: str | None = None
    type_client_auth: str | None = None
    loyalty_applicable_type: str | None = None
    prohibit_redeeming_bonuses: bool | None = False
    prohibit_redeeming_coupons: bool | None = False


class IncustCustomerData(BaseModel):
    token: str | None = None
    external_id: str | None = None
    user_card: CardInfo | None = None


class IncustUserCardTemplate(BaseTemplateSchema):
    TEMPLATE_PATH = "incust_card.html"

    username: str
    qr: str
    photo: str | None
    code: str
    title: str


class LoyaltyReferralCode(BaseModel):
    code: str | None = None
    loyalty_id: str | None = None


class LoyaltyAcceptInvitation(BaseModel):
    code: int | None = None
    description: str | None = None
    message: str | None = None


class CouponRedeemResult(BaseModel):
    code: int | None = None
    description: str | None = None
    message: str | None = None


RulesTypeLiteral = Literal["by-all-rules", "by-charge-only-rules", "without-rules"]


class IncustNewsData(BaseModel):
    category_title: str | None = None
    coupon_batches: list[CouponBatch] | None = None
    created: str | None = None
    description: str | None = None
    description_extended: str | None = None
    id: str | None = None
    link: str | None = None
    # loyalty: Loyalty | None = None
    photo: str | None = None
    # pos: Pos | None = None
    priority: int | None = None
    row_offset: int | None = None
    title: str | None = None
    type: str | None = None


from pydantic import BaseModel, Field
from typing import Literal

class CustomerBenefitsTopUpOperationData(BaseModel):
    amount: float = Field(..., description="Amount credited benefits")
    comment: None | str = Field(None, description="Comment")
    currency: None | str = Field(None, description="Bonus points currency ISO string")
    expire: None | str = Field(None, description="Value of promotional bonuses expire date")
    id: str = Field(..., min_length=3, description="User identifier (phone number, card code, QR code)")
    id_type: None | Literal[
        'phone', 'qr', 'email', 'card', 'temporary-card', 'id', 'external-id', 'social-network', 'ssn', 'itin', 'itn'
    ] = Field(None, description="User identifier type")
    promotional_bonuses_expire_date: None | str = Field(None, description="Alias of expire field (DEPRECATED)")
    promotional_bonuses_starting_date: None | str = Field(None, description="Alias of starting field (DEPRECATED)")
    skip_message: None | bool = Field(None, description="Skip notifications")
    special_account_id: None | str = Field(None, description="Special account ID")
    starting: None | str = Field(None, description="Value of bonuses starting date")
    type: Literal['regular-bonuses', 'promotional-bonuses', 'special-account'] = Field(..., description="Benefit type")
