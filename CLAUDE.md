# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# Pay4Say - Повне керівництво розробника

## 1. Глобальні правила взаємодії

### 1.1 Мова спілкування
- Усі відповіді надавати українською мовою
- Назви змінних, функцій та класів пишуться англійською

### 1.2 Робота з кодом
- **НЕ виводити код у повідомленнях**
- Вносити зміни безпосередньо у файли
- Перевіряти наявність полів та атрибутів обєктів, методів перед використанням
- Уникати зайвих запитів на підтвердження
- Після змін перевіряти код на помилки лінтера
- Виправляти виявлені помилки
- Дотримуватись стилю коду проекту

### 1.3 Заборонені дії
- **НЕ запускати файли на виконання**
- **НЕ робити деплой проекту**
- **НЕ створювати і НЕ виконувати міграції бази даних**
- **НЕ створювати тести** (якщо не вказано спеціально)
- **НЕ запускати тести автоматично**
- **НЕ робити коміти**

### 1.4 Критично важливі правила
- Якщо розробник каже "я вже додав/змінив/виправив/вніс зміни/зробив" - **НЕ змінювати цей код**
- Ретельно читати коментарі перед внесенням змін
- **ЗАВЖДИ** поважати існуючі зміни розробника
- **НЕ дублювати** код, який розробник вже реалізував
- При зміні сигнатури функції завжди шукати всі її виклики
- Аналізувати, звідки беруться обов'язкові параметри
- Використовувати правильні джерела даних замість хардкоду

### 1.5 Політика відповідей
- Не робити великі описи зробленого. Дуже коротко підсумовувати
- Не виводити код у відповіді. Економити токени

## 2. Технологічний стек та структура проекту

### 2.1 Основні технології
- **Backend**: FastAPI (порт 1914)
- **Telegram боти**: aiogram (Admin Bot, Service Bot, Client Bot)
- **WhatsApp бот**: самописна бібліотека aiowhatsapp
- **База даних**: SQLAlchemy для основного коду, Django ORM тільки для міграцій
- **Віртуальне середовище**: `/home/<USER>/pay4say/.venv/`
- **Робоче середовище**: WSL (Ubuntu)
- **Pydantic**: v1

### 2.2 Шляхи проекту
- **Основний проект**: `\home\vik\pay4say`
- папка запуску: `\home\vik\pay4say\bot`

### 2.3 Загальна структура
```
/home/<USER>/pay4say/
├── bot/                    # Основна директорія проекту (коренева для імпортів)
├── site/                   # Django застосунок (міграції БД)
├── tests/                  # Тести проекту
├── docs/                   # Документація
├── .venv/                  # Віртуальне середовище Python
└── qrcodes_api_project/    # Окремий сервіс для QR кодів
```

## 21. Спеціальні інструкції

### 21.1 Важливі застереження
- **Запам'ятай назавжди:** Ти НЕ створюєш і не виконуєш ніякі міграції. НЕ запускаєш ніякі тести.
- **КРИТИЧНО ВАЖЛИВО:** При зміні структури моделей БД ЗАВЖДИ оновлювати обидві ORM системи:
  - SQLAlchemy моделі в `bot/db/models/`
  - Django моделі в `site/events/models/`
  - Зміни в структурі полів повинні бути ідентичними в обох системах
  - Django ORM використовується ТІЛЬКИ для міграцій БД
  - SQLAlchemy використовується для роботи з даними в проекті

## 6. Структура API

### 6.1 Platform API (`/bot/api/platform/`)
API для адміністраторів платформи:
- `router/billing/` - біллінг платформи
- `router/ewallets/` - управління гаманцями
- `router/admins/` - управління адміністраторами
- `router/business_payment_settings/` - налаштування платежів

### 6.2 Admin API (`/bot/api/admin/`)
API для адмін-панелі бізнесів:
- `router/users/` - управління користувачами
- `router/products/` - управління товарами
- `router/stores/` - управління магазинами
- `router/bots/` - налаштування ботів
- `router/custom_texts/` - кастомні тексти
- `router/payment_settings/` - налаштування платежів

### 6.3 Client API (`/bot/api/client_api_router/`)
API для клієнтських застосунків:
- `bot/` - API для telegram ботів
- `web/` - веб API для клієнтів
- `store/` - функціонал магазину
- `payments/` - обробка платежів

## 8. Інтеграція з InCust API

### 8.1 Клієнти incust API та адаптер до них

- **InCust API клієнт**: `\home\vik\in-cust-api-client\incust_client_api_client`
- **InCust Terminal API**: `\home\vik\in-cust-terminal-api-client\incust_terminal_api_client`
- **InCust методи для взаємодії з новими клієнтами до АПІ інкаст через налаштування з LoyaltySettings**: `/home/<USER>/pay4say/bot/core/loyalty/incust_client_adapter.py` . Методи методи run_with_incust_client_api або run_with_incust_terminal_api.

Перед використанням методів клієнта в адаптері отримати налаштування.
Налаштування для клієнтів АПІ отримувати з LoyaltySettings.get_loyalty_settings_for_context передаючи всі можливі параметри. для параметрів лояльності еволет обовязково передавати еволлет_ід.
Метод отримує найбільш пріоритетний параметр лояльності для даного контексту використання.

Якщо для запиту до АПІ потрібно юзера то в адаптер передаємо user.

В даний момент відбувається перехід на роботу з АПІ інкаст через ці нові клієнти, адаптер та LoyaltySettings. 
Відповідно коли потрібно переробити існуючий процес роботи з АПІ інкаст, слід використовувати цей адаптер та налаштування.
НЕ використовувати IncustService, brand.is_incust... все що повязане із існуючим механізмом роботи з АПІ інкаст.

Використовувати оригінальні схеми нових клієнтів інкаст. Не залишати ніякі старі схеми для сумісності. Повнінстю переходити на нові клієнти і їх схеми в процесі який переробляється.

При переході аналізувати існуючу логіку і зберегти її. Заміняти потрібно самі звернення до АПІ інкасту за допомогою нових клієнтів і адаптера. використовувати методи run_with_incust_client_api або run_with_incust_terminal_api. Щоб визначити які саме методи потрібно передати для викликів у старому коді шукаємо роут який використовувався для звернення до АПІ інкаст і по цьому визначаємо метод нового клієнта. 

### 8.2 Використання
Нові АПІ Клієнти incust додані в проект як пакети, тому використовуються відповідні імпорти.

## 9. Віртуальне середовище

### 9.1 Активація
```bash
cd /home/<USER>/pay4say
source .venv/bin/activate
```

### 9.2 Використання без активації
```bash
/home/<USER>/pay4say/.venv/bin/python [script.py]
/home/<USER>/pay4say/.venv/bin/pip install [package]
```

## 10. Робота з локалізацією

### 10.1 Основні функції
```python
from utils.text import f

# Стандартні переклади
error_message = await f("error_key", lang, variable=value)

# Функція t() з обов'язковими параметрами
await t(
    object,
    lang,
    original_lang,  # group.lang
    fallback_to_original_on_error=True,
    group_id=group_id,
    is_auto_translate_allowed=group.is_translate
)
```

### 10.2 Конвенції
- Ключі в Google таблиці в `UPPER_CASE_WITH_UNDERSCORES`
- Формат: `КЛЮЧ[Tab]ENGLISH[Tab]UKRAINIAN[Tab]RUSSIAN`
- Підтримка змінних: `{variable_name}`

## 12. Обробка помилок

### 12.2 Базовий клас
```python
from exceptions import ErrorWithTextVariable

class CustomError(ErrorWithTextVariable):
    text_variable = "error_key"
    status_code = 400
```


## 3. Робота з базою даних

### 3.1 Дві ORM системи
- **SQLAlchemy**: Основна ORM для роботи з даними в проекті
- **Django ORM**: Використовується тільки для міграцій БД
- При зміні моделей БД потрібно оновлювати обидві системи

### 3.2 Декоратор @db_func
- Використовується для перетворення синхронних запитів в асинхронні
- Методи під цим декоратором викликаються через `await`

### 3.3 Використання методів моделей
Для простих КРУД операцій використовувати вбудовані методи моделей які наслідуються від `BaseDBModel` і інших класів:
```python
item = await Model.get(id)
items = await Model.get_list(filter1=value1, filter2=value2)
new_item = await Model.create(**data)
await item.update(**data)
await item.delete()
exists = await Model.is_exists(**filters)
```

### 3.4 Коли створювати CRUD функції
CRUD функції під декоратором `@db_func` створюються **тільки** для:
- Складних JOIN запитів між таблицями
- Спеціальної логіки фільтрації
- Агрегації даних (COUNT, SUM, GROUP BY)
- Bulk операцій
- Складних транзакцій

**НЕ створювати** CRUD функції для базових операцій!

### 3.5 Структура БД
- **Локація**: `/bot/db/` - робити імпорти `from db.models import User`
- **Декоратор**: `@db_func` для синхронних функцій
- **Сесія**: `sess()` (імпорт з `db`) - для КРУД функцій.