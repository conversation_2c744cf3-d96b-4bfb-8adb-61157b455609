import asyncio
import logging
from psutils.exceptions import ErrorWithTextVariable

from db import DBSession
from core.external_data.liqpay.service import get_shipments_for_liqpay
from schemas.payment_settings.liqpay import TaxList
from utils.text import f

# Налаштування логування
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger('debugger.liqpay')

async def main():
    """Скрипт для дебагу функції get_shipments_for_liqpay"""
    try:
        with DBSession():
            brand_id = 94  # Homex
            # store_id= 905 # homex правий берег
            store_id= None # homex правий берег
            
            logger.info(f"Початок тестування для brand_id: {brand_id}")
            result = await get_shipments_for_liqpay(brand_id, store_id, "А")
            print(f"Found {len(result)} shipments")
            for item in result:
                print(f"  - {item}")
            
    except ErrorWithTextVariable as e:
        logging.error(e, exc_info=True)
        print(await f(e.text_variable, "uk", **e.text_kwargs))
        raise
    else:
        print("success")

if __name__ == "__main__":
    asyncio.run(main()) 