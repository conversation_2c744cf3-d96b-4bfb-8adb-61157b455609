import pytest
from aiohttp import ClientSession, ClientTimeout, BasicAuth
import asyncio

async def fetch_check(url):
    proxy_auth = BasicAuth('YtJ8cPxGu', 'TyGQjjw8U')
    proxy_url = "http://93.190.123.130:63156"
    timeout = ClientTimeout(connect=4, sock_read=7)
    
    async with ClientSession(timeout=timeout) as session:
        async with session.get(
            url,
            proxy=proxy_url, 
            proxy_auth=proxy_auth,
            verify_ssl=False
        ) as resp:
            assert resp.status == 200, f"Request failed with status {resp.status}"
            return await resp.text()

@pytest.mark.asyncio
async def test_fetch_uz_check():
    """Тест отримання чеку з UZ системи."""
    url = "https://ofd.soliq.uz/check?t=UZ210317225111&r=178750&c=20240925192544&s=011553220380"
    result = await fetch_check(url)
    
    # Перевірки
    assert result is not None, "No response received"
    assert len(result) > 0, "Empty response received"
    print(f"Response length: {len(result)}")
    print(f"First 100 characters: {result[:100]}") 