import asyncio
import logging

import pytest
from psutils.exceptions import ErrorWithTextVariable

from db import DBSession, crud
from db.models import User
from utils.text import f

logger = logging.getLogger("debugger")

@pytest.mark.asyncio
async def test_delete_user():
    """Тест видалення користувача."""
    try:
        with DBSession():
            chat_id = **********  # Petro Cerkanyuk
            user = await User.get(chat_id)
            
            assert user is not None, "User not found"

            user_id = user.id
            print(f"{user.id=}")
            print(f"{user.incust_external_id=}")
            is_need_delete_incust_customer = True
            await crud.delete_user_account(user, is_need_delete_incust_customer)
            
            # Перевіряємо, що користувача видалено
            deleted_user = await User.get_by_id(user_id)
            assert deleted_user.status == "deactivated", "User was not deleted"
            
    except ErrorWithTextVariable as e:
        logging.error(e, exc_info=True)
        print(await f(e.text_variable, "uk", **e.text_kwargs))
        raise
    else:
        print("success")


if __name__ == "__main__":
    asyncio.run(test_delete_user())