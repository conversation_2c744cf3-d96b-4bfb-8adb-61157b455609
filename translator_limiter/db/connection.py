from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, AsyncEngine
from sqlalchemy.orm import sessionmaker

from config import DB_URL


engine: AsyncEngine = create_async_engine(DB_URL, echo=False, pool_pre_ping=True, pool_size=20)

__make_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)  # type: ignore


def make_session() -> AsyncSession:
    return __make_session()
